from clickhouse_driver import Client
import pandas as pd
from datetime import datetime

# 连接到数据库
client_ap = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 生成两个表：quarterly_data_filled 和 long_time_fundamental_na_list ===")

def create_quarterly_data_filled_table():
    """
    创建季报数据补充表
    """
    print("\n1. 创建 quarterly_data_filled 表...")
    
    # 先删除已存在的表
    try:
        client_ap.execute("DROP TABLE IF EXISTS quarterly_data_filled")
        print("  ✅ 删除旧表成功")
    except:
        pass
    
    create_table_sql = """
    CREATE TABLE quarterly_data_filled (
        id String,
        stock_symbol String,
        financial_period_absolute String,
        statement_type String,
        item_name String,
        original_value Nullable(Float64),
        
        data_status String,
        fill_method Nullable(String),
        source_periods Nullable(String),
        
        filled_simple_estimates Nullable(Float64),
        corrected_filled_simple_estimates Nullable(Float64),
        
        original_effective_date Nullable(Date),
        estimated_effective_date Nullable(Date),
        corrected_effective_date Nullable(Date),
        
        created_at DateTime,
        updated_at DateTime,
        confidence_score Nullable(Float64),
        notes Nullable(String)
    )
    ENGINE = MergeTree()
    ORDER BY (stock_symbol, financial_period_absolute, statement_type, item_name)
    PARTITION BY stock_symbol
    """
    
    try:
        client_ap.execute(create_table_sql)
        print("  ✅ quarterly_data_filled 表创建成功")
        return True
    except Exception as e:
        print(f"  ❌ 创建表时出错: {e}")
        return False

def create_long_time_na_table():
    """
    创建长期缺失数据分析表
    """
    print("\n2. 创建 long_time_fundamental_na_list 表...")
    
    # 先删除已存在的表
    try:
        client_ap.execute("DROP TABLE IF EXISTS long_time_fundamental_na_list")
        print("  ✅ 删除旧表成功")
    except:
        pass
    
    create_table_sql = """
    CREATE TABLE long_time_fundamental_na_list (
        stock_symbol String,
        analysis_date Date,
        
        -- 可用数据统计
        total_available_periods Int32,
        earliest_period String,
        latest_period String,
        
        -- 按类型分类的可用期间数
        annual_periods_count Int32,
        quarterly_periods_count Int32,
        half_yearly_periods_count Int32,
        
        -- 详细的可用期间列表
        available_periods Array(String),
        available_periods_dates Array(Date),
        available_periods_item_counts Array(Int32),
        
        -- 缺失期间分析
        missing_periods Array(String),
        missing_periods_types Array(String),
        missing_periods_reasons Array(String),
        
        -- 数据质量评估
        data_completeness_score Float64,
        historical_coverage_years Int32,
        recent_data_availability String,
        
        -- 元数据
        created_at DateTime,
        notes String
    )
    ENGINE = MergeTree()
    ORDER BY (stock_symbol, analysis_date)
    PARTITION BY stock_symbol
    """
    
    try:
        client_ap.execute(create_table_sql)
        print("  ✅ long_time_fundamental_na_list 表创建成功")
        return True
    except Exception as e:
        print(f"  ❌ 创建表时出错: {e}")
        return False

def get_direct_calculation_data(stock_symbol, missing_period):
    """
    获取直接计算的数据
    """
    year = int(missing_period[2:6])
    quarter = int(missing_period[7])
    
    results = []
    
    # 通过年报计算（我们的主要方法）
    annual_period = f'FY{year}'
    
    # 获取同年其他已知季度
    known_quarters = []
    for q in range(1, 5):
        if q != quarter:
            test_period = f'FY{year}Q{q}'
            query_test = f"""
            SELECT COUNT(*) as count
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = '{stock_symbol}'
              AND financial_period_absolute = '{test_period}'
            """
            try:
                result_test = client_ap.execute(query_test)
                if result_test[0][0] > 0:
                    known_quarters.append(test_period)
            except:
                continue
    
    if known_quarters:
        known_quarters_str = "', '".join(known_quarters)
        
        query_annual_calc = f"""
        SELECT 
            a.item_name,
            a.statement_type,
            a.value as annual_value,
            SUM(q.value) as known_quarters_sum,
            (a.value - SUM(q.value)) as calculated_value
        FROM (
            SELECT item_name, statement_type, value
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = '{stock_symbol}'
              AND financial_period_absolute = '{annual_period}'
        ) a
        JOIN (
            SELECT item_name, statement_type, value
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = '{stock_symbol}'
              AND financial_period_absolute IN ('{known_quarters_str}')
        ) q ON a.item_name = q.item_name AND a.statement_type = q.statement_type
        WHERE a.value IS NOT NULL AND q.value IS NOT NULL
        GROUP BY a.item_name, a.statement_type, a.value
        ORDER BY a.statement_type, a.item_name
        """
        
        try:
            result = client_ap.execute(query_annual_calc)
            for item_name, statement_type, a_value, q_sum, calc_value in result:
                results.append({
                    'stock_symbol': stock_symbol,
                    'financial_period_absolute': missing_period,
                    'statement_type': statement_type,
                    'item_name': item_name,
                    'original_value': None,
                    'data_status': 'corrected_real',
                    'fill_method': 'annual_direct',
                    'source_periods': f'{annual_period},{",".join(known_quarters)}',
                    'filled_simple_estimates': None,
                    'corrected_filled_simple_estimates': round(calc_value, 2),
                    'original_effective_date': None,
                    'estimated_effective_date': None,
                    'corrected_effective_date': datetime.now().date(),
                    'created_at': datetime.now(),
                    'updated_at': datetime.now(),
                    'confidence_score': 0.92,
                    'notes': f'Direct calculation using annual data minus {len(known_quarters)} known quarters'
                })
        except Exception as e:
            print(f"      查询{stock_symbol}-{missing_period}年报计算时出错: {e}")
    
    return results

def insert_quarterly_data():
    """
    插入季报数据到 quarterly_data_filled 表
    """
    print("\n3. 插入季报补充数据...")
    
    # 可直接计算的股票和期间
    direct_calculable = {
        'CCEP': ['FY2023Q1', 'FY2023Q2', 'FY2024Q1', 'FY2024Q2'],
        'MRP': ['FY2023Q1', 'FY2023Q2'],
        'VG': ['FY2023Q1', 'FY2023Q2'],
        'SIG': ['FY2023Q1'],
        'AU': ['FY2024Q1', 'FY2024Q2']
    }
    
    total_inserted = 0
    
    for stock_symbol, missing_periods in direct_calculable.items():
        print(f"  处理股票: {stock_symbol}")
        
        for missing_period in missing_periods:
            print(f"    插入期间: {missing_period}")
            
            # 获取计算数据
            period_results = get_direct_calculation_data(stock_symbol, missing_period)
            
            if period_results:
                # 批量插入数据
                for result in period_results:
                    # 生成ID
                    result['id'] = f"{result['stock_symbol']}|{result['financial_period_absolute']}|{result['item_name']}"
                    
                    # 插入单条记录
                    insert_sql = """
                    INSERT INTO quarterly_data_filled VALUES (
                        %(id)s, %(stock_symbol)s, %(financial_period_absolute)s, %(statement_type)s, %(item_name)s,
                        %(original_value)s, %(data_status)s, %(fill_method)s, %(source_periods)s,
                        %(filled_simple_estimates)s, %(corrected_filled_simple_estimates)s,
                        %(original_effective_date)s, %(estimated_effective_date)s, %(corrected_effective_date)s,
                        %(created_at)s, %(updated_at)s, %(confidence_score)s, %(notes)s
                    )
                    """
                    
                    try:
                        client_ap.execute(insert_sql, result)
                        total_inserted += 1
                    except Exception as e:
                        print(f"      插入记录时出错: {e}")
                        continue
                
                print(f"      插入 {len(period_results)} 条记录")
    
    print(f"  ✅ 总计插入 {total_inserted} 条记录")
    return total_inserted

def insert_post_estimation_data():
    """
    插入POST的估算数据
    """
    print("\n4. 插入POST估算数据...")
    
    post_periods = ['FY2023Q1', 'FY2023Q2', 'FY2023Q3', 'FY2024Q1', 'FY2024Q2', 'FY2024Q3']
    
    # 为POST创建占位记录，表示需要估算
    for period in post_periods:
        placeholder_record = {
            'id': f'POST|{period}|PLACEHOLDER',
            'stock_symbol': 'POST',
            'financial_period_absolute': period,
            'statement_type': 'placeholder',
            'item_name': 'NEEDS_ESTIMATION',
            'original_value': None,
            'data_status': 'needs_estimation',
            'fill_method': 'rolling_average',
            'source_periods': 'TBD',
            'filled_simple_estimates': None,
            'corrected_filled_simple_estimates': None,
            'original_effective_date': None,
            'estimated_effective_date': None,
            'corrected_effective_date': None,
            'created_at': datetime.now(),
            'updated_at': datetime.now(),
            'confidence_score': 0.75,
            'notes': f'POST {period} requires rolling average estimation due to irregular reporting schedule'
        }
        
        insert_sql = """
        INSERT INTO quarterly_data_filled VALUES (
            %(id)s, %(stock_symbol)s, %(financial_period_absolute)s, %(statement_type)s, %(item_name)s,
            %(original_value)s, %(data_status)s, %(fill_method)s, %(source_periods)s,
            %(filled_simple_estimates)s, %(corrected_filled_simple_estimates)s,
            %(original_effective_date)s, %(estimated_effective_date)s, %(corrected_effective_date)s,
            %(created_at)s, %(updated_at)s, %(confidence_score)s, %(notes)s
        )
        """
        
        try:
            client_ap.execute(insert_sql, placeholder_record)
            print(f"    插入POST {period}占位记录")
        except Exception as e:
            print(f"    插入POST {period}时出错: {e}")

def insert_na_analysis_data():
    """
    插入长期缺失数据分析（虽然我们没有真正长期缺失的股票）
    """
    print("\n5. 插入长期缺失数据分析...")
    
    # 创建一个示例记录，说明我们的分析结果
    analysis_record = {
        'stock_symbol': 'ANALYSIS_SUMMARY',
        'analysis_date': datetime.now().date(),
        'total_available_periods': 0,
        'earliest_period': 'N/A',
        'latest_period': 'N/A',
        'annual_periods_count': 0,
        'quarterly_periods_count': 0,
        'half_yearly_periods_count': 0,
        'available_periods': [],
        'available_periods_dates': [],
        'available_periods_item_counts': [],
        'missing_periods': [],
        'missing_periods_types': [],
        'missing_periods_reasons': [],
        'data_completeness_score': 1.0,
        'historical_coverage_years': 0,
        'recent_data_availability': 'All stocks have sufficient historical data',
        'created_at': datetime.now(),
        'notes': 'Analysis completed: No stocks require long-term fundamental data NA listing. All missing quarterly data can be calculated or estimated.'
    }
    
    insert_sql = """
    INSERT INTO long_time_fundamental_na_list VALUES (
        %(stock_symbol)s, %(analysis_date)s, %(total_available_periods)s, %(earliest_period)s, %(latest_period)s,
        %(annual_periods_count)s, %(quarterly_periods_count)s, %(half_yearly_periods_count)s,
        %(available_periods)s, %(available_periods_dates)s, %(available_periods_item_counts)s,
        %(missing_periods)s, %(missing_periods_types)s, %(missing_periods_reasons)s,
        %(data_completeness_score)s, %(historical_coverage_years)s, %(recent_data_availability)s,
        %(created_at)s, %(notes)s
    )
    """
    
    try:
        client_ap.execute(insert_sql, analysis_record)
        print("  ✅ 插入分析总结记录成功")
    except Exception as e:
        print(f"  ❌ 插入分析记录时出错: {e}")

def verify_tables():
    """
    验证表的创建和数据插入
    """
    print("\n6. 验证表创建和数据插入...")
    
    # 验证 quarterly_data_filled 表
    try:
        result1 = client_ap.execute("SELECT COUNT(*) FROM quarterly_data_filled")
        quarterly_count = result1[0][0]
        print(f"  quarterly_data_filled 表记录数: {quarterly_count:,}")
        
        # 按状态统计
        result2 = client_ap.execute("SELECT data_status, COUNT(*) FROM quarterly_data_filled GROUP BY data_status")
        print(f"  按状态分布:")
        for status, count in result2:
            print(f"    {status}: {count:,}条")
            
    except Exception as e:
        print(f"  ❌ 验证quarterly_data_filled表时出错: {e}")
    
    # 验证 long_time_fundamental_na_list 表
    try:
        result3 = client_ap.execute("SELECT COUNT(*) FROM long_time_fundamental_na_list")
        na_count = result3[0][0]
        print(f"  long_time_fundamental_na_list 表记录数: {na_count:,}")
        
    except Exception as e:
        print(f"  ❌ 验证long_time_fundamental_na_list表时出错: {e}")

def main():
    """
    主函数
    """
    print("开始创建两个表...")
    
    try:
        # 1. 创建表结构
        table1_success = create_quarterly_data_filled_table()
        table2_success = create_long_time_na_table()
        
        if not (table1_success and table2_success):
            print("❌ 表创建失败，停止执行")
            return
        
        # 2. 插入数据
        inserted_count = insert_quarterly_data()
        insert_post_estimation_data()
        insert_na_analysis_data()
        
        # 3. 验证结果
        verify_tables()
        
        print(f"\n=== 两个表创建完成 ===")
        print(f"✅ quarterly_data_filled: 存储补充的季报数据")
        print(f"✅ long_time_fundamental_na_list: 存储长期缺失数据分析")
        print(f"📊 主要数据: {inserted_count:,}条直接计算记录")
        print(f"📋 辅助数据: POST的6个估算占位记录")
        
    except Exception as e:
        print(f"❌ 执行过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

