from clickhouse_driver import Client
import pandas as pd
from datetime import datetime, timedelta

# 连接到数据库
client_ap = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 改进的季报数据补充机制 ===")

def identify_missing_quarterly_data():
    """
    识别有缺失季报数据的股票和具体缺失的期间
    """
    missing_stocks_periods = {
        'CCEP': ['FY2023Q1', 'FY2023Q2', 'FY2024Q1', 'FY2024Q2'],
        'POST': ['FY2023Q1', 'FY2023Q2', 'FY2023Q3', 'FY2024Q1', 'FY2024Q2', 'FY2024Q3'],
        'MRP': ['FY2023Q1', 'FY2023Q2'],
        'VG': ['FY2023Q1', 'FY2023Q2'],
        'SIG': ['FY2023Q1'],
        'AU': ['FY2024Q1', 'FY2024Q2']
    }
    
    return missing_stocks_periods

def get_direct_calculation_data(stock_symbol, missing_period):
    """
    检查是否可以通过半年报/年报减去已知季度来直接计算缺失季度
    """
    year = int(missing_period[2:6])
    quarter = int(missing_period[7])
    
    direct_calc_results = []
    
    # 情况1: 通过半年报计算
    if quarter <= 2:
        # Q1或Q2缺失，检查H1和另一个季度
        half_year_period = f'FY{year}H1'
        other_quarter = f'FY{year}Q2' if quarter == 1 else f'FY{year}Q1'
        
        # 查询半年报和另一个季度的数据
        query_half_calc = f"""
        SELECT 
            h.item_name,
            h.statement_type,
            h.value as half_year_value,
            q.value as quarter_value,
            (h.value - q.value) as calculated_value
        FROM (
            SELECT item_name, statement_type, value
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = '{stock_symbol}'
              AND financial_period_absolute = '{half_year_period}'
        ) h
        JOIN (
            SELECT item_name, statement_type, value
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = '{stock_symbol}'
              AND financial_period_absolute = '{other_quarter}'
        ) q ON h.item_name = q.item_name AND h.statement_type = q.statement_type
        WHERE h.value IS NOT NULL AND q.value IS NOT NULL
        """
        
        try:
            result = client_ap.execute(query_half_calc)
            for item_name, statement_type, h_value, q_value, calc_value in result:
                direct_calc_results.append({
                    'method': 'half_year_direct',
                    'source_data': f'{half_year_period} - {other_quarter}',
                    'item_name': item_name,
                    'statement_type': statement_type,
                    'calculated_value': calc_value,
                    'confidence_score': 0.95,
                    'notes': f'Direct calculation: {half_year_period}({h_value}) - {other_quarter}({q_value}) = {calc_value}'
                })
        except Exception as e:
            print(f"      半年报直接计算查询出错: {e}")
    
    # 情况2: 通过年报计算
    annual_period = f'FY{year}'
    
    # 获取同年其他已知季度
    known_quarters = []
    for q in range(1, 5):
        if q != quarter:
            test_period = f'FY{year}Q{q}'
            query_test = f"""
            SELECT COUNT(*) as count
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = '{stock_symbol}'
              AND financial_period_absolute = '{test_period}'
            """
            try:
                result = client_ap.execute(query_test)
                if result[0][0] > 0:
                    known_quarters.append(test_period)
            except:
                continue
    
    if known_quarters:
        # 构建年报减去已知季度的查询
        known_quarters_str = "', '".join(known_quarters)
        
        query_annual_calc = f"""
        SELECT 
            a.item_name,
            a.statement_type,
            a.value as annual_value,
            SUM(q.value) as known_quarters_sum,
            (a.value - SUM(q.value)) as calculated_value
        FROM (
            SELECT item_name, statement_type, value
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = '{stock_symbol}'
              AND financial_period_absolute = '{annual_period}'
        ) a
        JOIN (
            SELECT item_name, statement_type, value
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = '{stock_symbol}'
              AND financial_period_absolute IN ('{known_quarters_str}')
        ) q ON a.item_name = q.item_name AND a.statement_type = q.statement_type
        WHERE a.value IS NOT NULL AND q.value IS NOT NULL
        GROUP BY a.item_name, a.statement_type, a.value
        """
        
        try:
            result = client_ap.execute(query_annual_calc)
            for item_name, statement_type, a_value, q_sum, calc_value in result:
                direct_calc_results.append({
                    'method': 'annual_direct',
                    'source_data': f'{annual_period} - [{", ".join(known_quarters)}]',
                    'item_name': item_name,
                    'statement_type': statement_type,
                    'calculated_value': calc_value,
                    'confidence_score': 0.92,
                    'notes': f'Direct calculation: {annual_period}({a_value}) - known_quarters_sum({q_sum}) = {calc_value}'
                })
        except Exception as e:
            print(f"      年报直接计算查询出错: {e}")
    
    return direct_calc_results

def analyze_long_term_missing_data(stock_symbol):
    """
    分析长期缺失数据的股票，生成详细的数据可用性报告
    """
    # 获取该股票所有可用的财务期间
    query_available = f"""
    SELECT DISTINCT 
        financial_period_absolute,
        MIN(period_end_date) as period_end_date,
        COUNT(DISTINCT item_name) as item_count
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = '{stock_symbol}'
      AND period_end_date IS NOT NULL
    GROUP BY financial_period_absolute
    ORDER BY financial_period_absolute
    """
    
    try:
        result = client_ap.execute(query_available)
        available_periods = []
        for period, end_date, item_count in result:
            available_periods.append({
                'financial_period': period,
                'period_end_date': end_date,
                'item_count': item_count,
                'data_type': classify_period_type(period)
            })
        
        # 分析缺失的期间（基于时间范围推断应该有的期间）
        missing_analysis = analyze_missing_periods(available_periods)
        
        return {
            'stock_symbol': stock_symbol,
            'available_periods': available_periods,
            'missing_analysis': missing_analysis,
            'total_available': len(available_periods),
            'earliest_period': available_periods[0]['financial_period'] if available_periods else None,
            'latest_period': available_periods[-1]['financial_period'] if available_periods else None
        }
        
    except Exception as e:
        print(f"    分析{stock_symbol}长期缺失数据时出错: {e}")
        return None

def classify_period_type(period):
    """
    分类财务期间类型
    """
    if 'Q' in period:
        return 'quarterly'
    elif 'H' in period:
        return 'half_yearly'
    elif period.startswith('FY') and len(period) == 6:
        return 'annual'
    else:
        return 'other'

def analyze_missing_periods(available_periods):
    """
    分析缺失的期间
    """
    if not available_periods:
        return []
    
    # 提取年份范围
    years = set()
    for period in available_periods:
        period_str = period['financial_period']
        if period_str.startswith('FY') and len(period_str) >= 6:
            try:
                year = int(period_str[2:6])
                years.add(year)
            except:
                continue
    
    if not years:
        return []
    
    min_year = min(years)
    max_year = max(years)
    
    # 生成应该存在的期间列表
    expected_periods = []
    available_period_set = set(p['financial_period'] for p in available_periods)
    
    for year in range(min_year, max_year + 1):
        # 检查年报
        annual = f'FY{year}'
        if annual not in available_period_set:
            expected_periods.append({
                'missing_period': annual,
                'period_type': 'annual',
                'year': year,
                'reason': 'Expected annual report missing'
            })
        
        # 检查季报
        for quarter in range(1, 5):
            quarterly = f'FY{year}Q{quarter}'
            if quarterly not in available_period_set:
                expected_periods.append({
                    'missing_period': quarterly,
                    'period_type': 'quarterly',
                    'year': year,
                    'quarter': quarter,
                    'reason': 'Expected quarterly report missing'
                })
    
    return expected_periods

def create_long_term_na_table():
    """
    创建长期缺失数据分析表
    """
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS long_time_fundamental_na_list (
        stock_symbol String,
        analysis_date Date,
        
        -- 可用数据统计
        total_available_periods Int32,
        earliest_period String,
        latest_period String,
        
        -- 按类型分类的可用期间数
        annual_periods_count Int32,
        quarterly_periods_count Int32,
        half_yearly_periods_count Int32,
        
        -- 详细的可用期间列表
        available_periods Array(String),
        available_periods_dates Array(Date),
        available_periods_item_counts Array(Int32),
        
        -- 缺失期间分析
        missing_periods Array(String),
        missing_periods_types Array(String),
        missing_periods_reasons Array(String),
        
        -- 数据质量评估
        data_completeness_score Float64,
        historical_coverage_years Int32,
        recent_data_availability String,
        
        -- 元数据
        created_at DateTime,
        notes String
    )
    ENGINE = MergeTree()
    ORDER BY (stock_symbol, analysis_date)
    PARTITION BY stock_symbol
    """
    
    try:
        client_ap.execute(create_table_sql)
        print("  ✅ 长期缺失数据分析表创建成功")
    except Exception as e:
        print(f"  ❌ 创建长期缺失数据表时出错: {e}")

def process_improved_filling():
    """
    改进的季报数据填补处理流程
    """
    print("\n1. 开始改进的数据填补流程...")
    
    missing_data = identify_missing_quarterly_data()
    
    # 分类处理
    direct_calculable = []  # 可直接计算的
    need_estimation = []    # 需要估算的
    long_term_missing = []  # 长期缺失的
    
    for stock_symbol, missing_periods in missing_data.items():
        print(f"\n  分析股票: {stock_symbol}")
        
        # 检查是否有足够历史数据进行估算
        query_history_check = f"""
        SELECT COUNT(DISTINCT financial_period_absolute) as period_count
        FROM priority_quality_fundamental_data_complete_deduped
        WHERE stock_symbol = '{stock_symbol}'
          AND financial_period_absolute REGEXP 'FY[0-9]{{4}}Q[1-4]'
        """
        
        try:
            result = client_ap.execute(query_history_check)
            historical_quarters = result[0][0]
            
            if historical_quarters < 3:
                # 历史数据不足，归类为长期缺失
                long_term_missing.append(stock_symbol)
                print(f"    归类为长期缺失 (只有{historical_quarters}个历史季度)")
                continue
                
        except Exception as e:
            print(f"    检查历史数据时出错: {e}")
            long_term_missing.append(stock_symbol)
            continue
        
        # 对每个缺失期间检查是否可直接计算
        for missing_period in missing_periods:
            print(f"    检查缺失期间: {missing_period}")
            
            # 尝试直接计算
            direct_calc_data = get_direct_calculation_data(stock_symbol, missing_period)
            
            if direct_calc_data:
                print(f"      ✅ 可直接计算 ({len(direct_calc_data)}个科目)")
                direct_calculable.append({
                    'stock_symbol': stock_symbol,
                    'missing_period': missing_period,
                    'calc_data': direct_calc_data
                })
            else:
                print(f"      需要估算")
                need_estimation.append({
                    'stock_symbol': stock_symbol,
                    'missing_period': missing_period
                })
    
    print(f"\n2. 分类结果:")
    print(f"  可直接计算: {len(direct_calculable)}个期间")
    print(f"  需要估算: {len(need_estimation)}个期间")
    print(f"  长期缺失: {len(long_term_missing)}只股票")
    
    # 处理长期缺失数据
    if long_term_missing:
        print(f"\n3. 处理长期缺失数据...")
        create_long_term_na_table()
        
        for stock_symbol in long_term_missing:
            print(f"  分析股票: {stock_symbol}")
            analysis_result = analyze_long_term_missing_data(stock_symbol)
            
            if analysis_result:
                print(f"    可用期间: {analysis_result['total_available']}")
                print(f"    时间范围: {analysis_result['earliest_period']} - {analysis_result['latest_period']}")
                print(f"    缺失期间: {len(analysis_result['missing_analysis'])}")
    
    # 处理可直接计算的数据
    if direct_calculable:
        print(f"\n4. 处理可直接计算的数据...")
        total_direct_calc = 0
        
        for item in direct_calculable:
            stock = item['stock_symbol']
            period = item['missing_period']
            calc_data = item['calc_data']
            
            print(f"  {stock} - {period}: {len(calc_data)}个科目可直接计算")
            total_direct_calc += len(calc_data)
            
            # 这里可以插入到quarterly_data_filled表，data_status='corrected_real'
    
    return {
        'direct_calculable': len(direct_calculable),
        'need_estimation': len(need_estimation),
        'long_term_missing': len(long_term_missing),
        'total_direct_calc_items': total_direct_calc if 'total_direct_calc' in locals() else 0
    }

def main():
    """
    主处理流程
    """
    print("开始改进的季报数据补充流程...")
    
    try:
        results = process_improved_filling()
        
        print(f"\n=== 改进处理结果 ===")
        print(f"✅ 可直接计算的期间: {results['direct_calculable']}")
        print(f"📊 直接计算的科目数: {results['total_direct_calc_items']}")
        print(f"🔄 需要估算的期间: {results['need_estimation']}")
        print(f"⚠️ 长期缺失的股票: {results['long_term_missing']}")
        
        print(f"\n改进要点:")
        print(f"1. ✅ 对可直接计算的数据使用 corrected_real 状态")
        print(f"2. ✅ 长期缺失股票单独建表分析")
        print(f"3. ✅ 提高了数据填补的准确性和可靠性")
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")

if __name__ == "__main__":
    main()

