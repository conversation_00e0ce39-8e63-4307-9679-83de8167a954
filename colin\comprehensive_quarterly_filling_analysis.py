from clickhouse_driver import Client
from datetime import datetime, timedelta

# 连接到数据库
client_ap = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 全面的季报补充分析 ===")

def analyze_historical_data_availability():
    """
    分析每只股票的历史数据可用性，识别长期缺失
    """
    print("\n1. 分析历史数据可用性...")
    
    stocks_to_analyze = ['CCEP', 'POST', 'MRP', 'VG', 'SIG', 'AU']
    long_term_na_stocks = []
    
    for stock in stocks_to_analyze:
        print(f"\n📊 分析 {stock}:")
        
        # 获取该股票所有可用的财务期间
        periods_query = f"""
        SELECT DISTINCT financial_period_absolute, 
               MIN(period_end_date) as earliest_date,
               MAX(period_end_date) as latest_date,
               COUNT(DISTINCT item_name) as item_count
        FROM priority_quality_fundamental_data_complete_deduped
        WHERE stock_symbol = '{stock}'
        GROUP BY financial_period_absolute
        ORDER BY financial_period_absolute
        """
        
        try:
            results = client_ap.execute(periods_query)
            
            if not results:
                print(f"  ❌ 没有任何财务数据")
                long_term_na_stocks.append({
                    'stock': stock,
                    'reason': '完全没有财务数据',
                    'available_periods': [],
                    'missing_analysis': '需要其他数据源'
                })
                continue
            
            # 分析期间类型
            annual_periods = []
            quarterly_periods = []
            half_yearly_periods = []
            
            print(f"  可用期间:")
            for period, earliest, latest, item_count in results:
                print(f"    {period}: {item_count}个科目 ({earliest} - {latest})")
                
                if period.endswith('Q1') or period.endswith('Q2') or period.endswith('Q3') or period.endswith('Q4'):
                    quarterly_periods.append(period)
                elif 'H1' in period or 'H2' in period:
                    half_yearly_periods.append(period)
                elif period.startswith('FY') and not any(q in period for q in ['Q', 'H']):
                    annual_periods.append(period)
            
            print(f"  年报: {len(annual_periods)}个 {annual_periods}")
            print(f"  季报: {len(quarterly_periods)}个 {quarterly_periods}")
            print(f"  半年报: {len(half_yearly_periods)}个 {half_yearly_periods}")
            
            # 计算时间跨度
            if results:
                earliest_year = min([int(p[2:6]) for p, _, _, _ in results if p.startswith('FY')])
                latest_year = max([int(p[2:6]) for p, _, _, _ in results if p.startswith('FY')])
                coverage_years = latest_year - earliest_year + 1
                
                print(f"  时间跨度: {earliest_year}-{latest_year} ({coverage_years}年)")
                
                # 判断是否为长期缺失
                recent_years = [2023, 2024]
                recent_data = [p for p in quarterly_periods if int(p[2:6]) in recent_years]
                
                if coverage_years < 3 or len(recent_data) < 4:
                    print(f"  ⚠️ 长期缺失：覆盖年限{coverage_years}年，近期季报{len(recent_data)}个")
                    long_term_na_stocks.append({
                        'stock': stock,
                        'reason': f'历史数据不足，覆盖{coverage_years}年，近期季报{len(recent_data)}个',
                        'available_periods': [p for p, _, _, _ in results],
                        'coverage_years': coverage_years,
                        'recent_quarterly_count': len(recent_data)
                    })
                else:
                    print(f"  ✅ 数据充足：可用于滚动估算")
            
        except Exception as e:
            print(f"  ❌ 查询{stock}时出错: {e}")
    
    return long_term_na_stocks

def identify_correction_scenarios():
    """
    识别各种修正场景和对应的effective_date
    """
    print(f"\n2. 识别修正场景...")
    
    scenarios = []
    
    # 场景1: 有年报 + Q3 + Q4 (等于两个半年报)
    print(f"\n📋 场景1: 年报 + Q3 + Q4 → 可修正Q1, Q2")
    scenario1_stocks = ['CCEP', 'MRP', 'VG', 'AU']
    
    for stock in scenario1_stocks:
        for year in [2023, 2024]:
            annual_period = f'FY{year}'
            
            # 检查是否有年报和下半年季报
            check_query = f"""
            SELECT 
                SUM(CASE WHEN financial_period_absolute = '{annual_period}' THEN 1 ELSE 0 END) as has_annual,
                SUM(CASE WHEN financial_period_absolute = 'FY{year}Q3' THEN 1 ELSE 0 END) as has_q3,
                SUM(CASE WHEN financial_period_absolute = 'FY{year}Q4' THEN 1 ELSE 0 END) as has_q4
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = '{stock}'
              AND financial_period_absolute IN ('{annual_period}', 'FY{year}Q3', 'FY{year}Q4')
            """
            
            try:
                result = client_ap.execute(check_query)
                has_annual, has_q3, has_q4 = result[0]
                
                if has_annual > 0 and has_q3 > 0 and has_q4 > 0:
                    # 获取年报的期间结束日期作为effective_date基准
                    date_query = f"""
                    SELECT period_end_date
                    FROM priority_quality_fundamental_data_complete_deduped
                    WHERE stock_symbol = '{stock}'
                      AND financial_period_absolute = '{annual_period}'
                    LIMIT 1
                    """
                    
                    date_result = client_ap.execute(date_query)
                    if date_result:
                        annual_end_date = date_result[0][0]
                        # effective_date = 年报期间结束日期 + 1天
                        effective_date = annual_end_date + timedelta(days=1)
                        
                        scenarios.append({
                            'type': 'annual_plus_h2',
                            'stock': stock,
                            'year': year,
                            'correctable_periods': [f'FY{year}Q1', f'FY{year}Q2'],
                            'source_periods': [annual_period, f'FY{year}Q3', f'FY{year}Q4'],
                            'calculation_method': 'annual_minus_h2',
                            'effective_date': effective_date,
                            'confidence': 0.92,
                            'notes': f'年报减去下半年(Q3+Q4)得到上半年(Q1+Q2)'
                        })
                        
                        print(f"  ✅ {stock} {year}: 可修正Q1,Q2 (effective_date: {effective_date})")
                
            except Exception as e:
                print(f"  ❌ 检查{stock} {year}时出错: {e}")
    
    # 场景2: 有年报 + Q2 + Q3 + Q4 (完整的3个季度)
    print(f"\n📋 场景2: 年报 + Q2 + Q3 + Q4 → 可直接计算Q1")
    # SIG已经在之前处理过了
    
    # 场景3: 有半年报数据
    print(f"\n📋 场景3: 半年报修正场景")
    # 检查是否有半年报数据可用于修正
    
    # 场景4: 需要滚动估算
    print(f"\n📋 场景4: 滚动估算场景")
    # POST等没有足够历史数据的
    
    return scenarios

def get_annual_report_effective_dates():
    """
    获取年报的实际发布日期，用于确定effective_date
    """
    print(f"\n3. 获取年报发布日期...")
    
    # 查询年报的period_end_date，这通常是财务年度结束日期
    # effective_date应该是报告实际发布日期+1天
    
    stocks = ['CCEP', 'MRP', 'VG', 'AU', 'SIG']
    
    for stock in stocks:
        print(f"\n📅 {stock}的年报日期:")
        
        dates_query = f"""
        SELECT financial_period_absolute, 
               period_end_date,
               announcement_date,
               updated_at_original
        FROM priority_quality_fundamental_data_complete_deduped
        WHERE stock_symbol = '{stock}'
          AND financial_period_absolute LIKE 'FY20%'
          AND NOT (financial_period_absolute LIKE '%Q%' OR financial_period_absolute LIKE '%H%')
        GROUP BY financial_period_absolute, period_end_date, announcement_date, updated_at_original
        ORDER BY financial_period_absolute
        """
        
        try:
            results = client_ap.execute(dates_query)
            
            for period, end_date, announce_date, update_date in results:
                print(f"  {period}:")
                print(f"    期间结束: {end_date}")
                print(f"    公告日期: {announce_date}")
                print(f"    更新日期: {update_date}")
                
                # effective_date逻辑：
                # 1. 如果有announcement_date，用announcement_date + 1
                # 2. 否则用period_end_date + 90天（估算发布延迟）
                if announce_date:
                    effective_date = announce_date + timedelta(days=1)
                    print(f"    生效日期: {effective_date} (基于公告日期)")
                else:
                    effective_date = end_date + timedelta(days=90)
                    print(f"    生效日期: {effective_date} (基于期间结束+90天)")
                
        except Exception as e:
            print(f"  ❌ 查询{stock}日期时出错: {e}")

def create_comprehensive_filling_plan():
    """
    创建全面的补充计划
    """
    print(f"\n4. 创建全面的补充计划...")
    
    # 1. 长期缺失股票 → long_time_fundamental_na_list
    long_term_na = analyze_historical_data_availability()
    
    # 2. 修正场景 → quarterly_data_filled (corrected_real)
    correction_scenarios = identify_correction_scenarios()
    
    # 3. 估算场景 → quarterly_data_filled (estimated)
    
    print(f"\n=== 全面计划总结 ===")
    print(f"📊 长期缺失股票: {len(long_term_na)}个")
    for stock_info in long_term_na:
        print(f"  {stock_info['stock']}: {stock_info['reason']}")
    
    print(f"📊 修正场景: {len(correction_scenarios)}个")
    for scenario in correction_scenarios:
        print(f"  {scenario['stock']} {scenario['year']}: {scenario['calculation_method']} (effective: {scenario['effective_date']})")
    
    return long_term_na, correction_scenarios

def main():
    """
    主函数
    """
    print("开始全面的季报补充分析...")
    
    try:
        # 获取年报发布日期信息
        get_annual_report_effective_dates()
        
        # 创建全面计划
        long_term_na, correction_scenarios = create_comprehensive_filling_plan()
        
        print(f"\n=== 下一步行动 ===")
        print(f"1. 更新 long_time_fundamental_na_list 表")
        print(f"2. 重新生成 quarterly_data_filled 表，包含正确的修正场景")
        print(f"3. 为每种场景设置正确的 effective_date")
        print(f"4. 区分 corrected_real, corrected_estimated, 和 needs_estimation")
        
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

