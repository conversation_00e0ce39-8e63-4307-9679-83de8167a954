from clickhouse_driver import Client
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def connect_to_ap_research():
    """连接到ap_research数据库"""
    client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')
    print("✅ 连接ap_research数据库成功")
    return client

def calculate_robust_cagr(values, years, method='geometric'):
    """健壮的CAGR计算"""
    if len(values) < 2:
        return None
    
    # 寻找最长连续正值区间
    if method == 'geometric':
        positive_runs = []
        current_run = []
        
        for i, val in enumerate(values):
            if val > 0:
                current_run.append((i, val))
            else:
                if len(current_run) >= 2:
                    positive_runs.append(current_run)
                current_run = []
        
        if len(current_run) >= 2:
            positive_runs.append(current_run)
        
        if positive_runs:
            # 选择最长区间
            longest_run = max(positive_runs, key=len)
            if len(longest_run) >= 2:
                first_val = longest_run[0][1]
                last_val = longest_run[-1][1]
                years_span = len(longest_run) - 1
                
                if first_val > 0 and last_val > 0:
                    return ((last_val / first_val) ** (1/years_span) - 1) * 100
    
    # 算术平均增长率作为备选
    growth_rates = []
    for i in range(1, len(values)):
        if values[i-1] != 0:
            growth_rate = (values[i] - values[i-1]) / abs(values[i-1]) * 100
            growth_rates.append(growth_rate)
    
    if growth_rates:
        return np.mean(growth_rates)
    
    return None

def calculate_complete_improved_factors(stock_symbol, stock_data):
    """根据改进方案计算完整的24个因子"""
    
    factors = {}
    
    # 按年份排序
    stock_data = stock_data.sort_values('fiscal_year')
    latest_data = stock_data.iloc[-1]
    
    # 计算上市年限
    listing_years = len(stock_data)
    
    print(f"   📊 {stock_symbol}: {listing_years}年数据")
    
    # 【1】tech_premium（技术溢价）
    try:
        rd_expense = latest_data.get('Research & Development Expense', 0) or 0
        rd_supplemental = latest_data.get('Research & Development Expense - Supplemental', 0) or 0
        total_rd = rd_expense + rd_supplemental
        revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
        
        if revenue > 0:
            tech_premium_raw = (total_rd / revenue) * 100
            factors['tech_premium'] = min(tech_premium_raw, 50)  # 上限50%
        else:
            factors['tech_premium'] = None
    except:
        factors['tech_premium'] = None
    
    # 【2】tech_gap_warning（技术差距警告）
    try:
        if listing_years >= 2 and factors.get('tech_premium') is not None:
            prev_data = stock_data.iloc[-2]
            prev_rd = (prev_data.get('Research & Development Expense', 0) or 0) + \
                     (prev_data.get('Research & Development Expense - Supplemental', 0) or 0)
            prev_revenue = prev_data.get('Revenue from Business Activities - Total', 0) or 0
            
            if prev_revenue > 0:
                prev_tech_premium = (prev_rd / prev_revenue) * 100
                if prev_tech_premium > 0:
                    change_rate = (factors['tech_premium'] - prev_tech_premium) / prev_tech_premium * 100
                    factors['tech_gap_warning'] = np.clip(change_rate, -100, 200)
                else:
                    factors['tech_gap_warning'] = None
            else:
                factors['tech_gap_warning'] = None
        else:
            factors['tech_gap_warning'] = None
    except:
        factors['tech_gap_warning'] = None
    
    # 【3】patent_density（专利密度）
    try:
        total_rd = (latest_data.get('Research & Development Expense', 0) or 0) + \
                  (latest_data.get('Research & Development Expense - Supplemental', 0) or 0)
        total_assets = latest_data.get('Total Assets', 0) or 0
        
        if total_assets > 0:
            patent_density_raw = (total_rd / total_assets) * 100
            factors['patent_density'] = min(patent_density_raw, 20)  # 上限20%
        else:
            factors['patent_density'] = None
    except:
        factors['patent_density'] = None
    
    # 【4】ecosystem_cash_ratio（生态现金比率）
    try:
        cash = latest_data.get('Cash & Cash Equivalents', 0) or 0
        short_term_investments = latest_data.get('Short-Term Investments', 0) or 0
        total_cash = cash + short_term_investments
        revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
        
        if revenue > 0:
            cash_ratio_raw = (total_cash / revenue) * 100
            factors['ecosystem_cash_ratio'] = min(cash_ratio_raw, 200)  # 上限200%
        else:
            factors['ecosystem_cash_ratio'] = None
    except:
        factors['ecosystem_cash_ratio'] = None
    
    # 【5】adjusted_roe（修正为ROE）
    try:
        net_income = latest_data.get('Normalized Net Income - Bottom Line', 0) or 0
        total_equity = (latest_data.get('Shareholders\' Equity - Attributable to Parent Shareholders - Total', 0) or 
                       latest_data.get('Common Equity - Total', 0) or 
                       latest_data.get('Common Equity Attributable to Parent Shareholders', 0) or 0)
        
        if total_equity > 0:
            roe_raw = (net_income / total_equity) * 100
            factors['adjusted_roe'] = np.clip(roe_raw, -50, 50)
        else:
            factors['adjusted_roe'] = None
    except:
        factors['adjusted_roe'] = None
    
    # 【6】fcf_quality（自由现金流质量）
    try:
        fcf = latest_data.get('Free Cash Flow', 0) or 0
        net_income = latest_data.get('Normalized Net Income - Bottom Line', 0) or 0
        
        if net_income != 0:
            fcf_quality_raw = fcf / net_income
            factors['fcf_quality'] = np.clip(fcf_quality_raw, -5, 5)
        else:
            factors['fcf_quality'] = None
    except:
        factors['fcf_quality'] = None
    
    # 【7】dynamic_safety_margin（动态安全边际）
    try:
        if factors.get('tech_premium') is not None and factors.get('ecosystem_cash_ratio') is not None:
            factors['dynamic_safety_margin'] = factors['tech_premium'] + factors['ecosystem_cash_ratio']
        else:
            factors['dynamic_safety_margin'] = None
    except:
        factors['dynamic_safety_margin'] = None
    
    # 【8】revenue_growth_continuity（营收增长连续性）
    try:
        if listing_years >= 2:
            years_to_use = min(3, listing_years - 1)
            growth_rates = []
            
            for i in range(1, years_to_use + 1):
                current_revenue = stock_data.iloc[-i].get('Revenue from Business Activities - Total', 0) or 0
                prev_revenue = stock_data.iloc[-i-1].get('Revenue from Business Activities - Total', 0) or 0
                
                if prev_revenue > 0 and current_revenue > 0:
                    growth_rate = (current_revenue - prev_revenue) / prev_revenue * 100
                    growth_rates.append(growth_rate)
            
            if len(growth_rates) >= 2:
                std_raw = np.std(growth_rates, ddof=1)
                factors['revenue_growth_continuity'] = min(std_raw, 50)  # 上限50%
            else:
                factors['revenue_growth_continuity'] = None
        else:
            factors['revenue_growth_continuity'] = None
    except:
        factors['revenue_growth_continuity'] = None
    
    # 【9】effective_tax_rate_improvement（税率同比变化）
    try:
        if listing_years >= 2:
            current_taxes = latest_data.get('Income Taxes', 0) or 0
            current_ebit = latest_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
            prev_data = stock_data.iloc[-2]
            prev_taxes = prev_data.get('Income Taxes', 0) or 0
            prev_ebit = prev_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
            
            if current_ebit > 0 and prev_ebit > 0:
                current_rate = (current_taxes / current_ebit) * 100
                prev_rate = (prev_taxes / prev_ebit) * 100
                rate_change = current_rate - prev_rate
                factors['effective_tax_rate_improvement'] = np.clip(rate_change, -50, 50)
            else:
                factors['effective_tax_rate_improvement'] = None
        else:
            factors['effective_tax_rate_improvement'] = None
    except:
        factors['effective_tax_rate_improvement'] = None
    
    # 【10】financial_health（财务健康度）
    try:
        current_assets = latest_data.get('Total Current Assets', 0) or 0
        current_liabilities = latest_data.get('Total Current Liabilities', 0) or 0
        
        if current_liabilities > 0:
            health_raw = current_assets / current_liabilities
            factors['financial_health'] = min(health_raw, 5)  # 上限5
        else:
            factors['financial_health'] = None
    except:
        factors['financial_health'] = None
    
    # 【11】valuation_bubble_signal（PEG比率）
    try:
        close_price = latest_data.get('close_price', 0) or 0
        eps = latest_data.get('EPS - Diluted - including Extraordinary Items Applicable to Common - Total', 0) or 0
        
        if listing_years >= 3 and close_price > 0 and eps > 0:
            # 计算revenue_cagr
            years_for_cagr = min(3, listing_years)
            revenue_values = []
            for i in range(years_for_cagr):
                revenue = stock_data.iloc[-years_for_cagr+i].get('Revenue from Business Activities - Total', 0) or 0
                revenue_values.append(revenue)
            
            revenue_cagr = calculate_robust_cagr(revenue_values, years_for_cagr, 'geometric')
            
            if revenue_cagr and revenue_cagr > 0:
                pe = close_price / eps
                peg_raw = pe / revenue_cagr
                factors['valuation_bubble_signal'] = min(abs(peg_raw), 5)  # 上限5
            else:
                factors['valuation_bubble_signal'] = None
        else:
            factors['valuation_bubble_signal'] = None
    except:
        factors['valuation_bubble_signal'] = None
    
    # 【12】roe_stability（ROE稳定性）
    try:
        years_for_stability = min(5, listing_years)
        if years_for_stability >= 3:
            roe_values = []
            
            for i in range(years_for_stability):
                year_data = stock_data.iloc[-i-1]
                net_income = year_data.get('Normalized Net Income - Bottom Line', 0) or 0
                total_equity = (year_data.get('Shareholders\' Equity - Attributable to Parent Shareholders - Total', 0) or 
                               year_data.get('Common Equity - Total', 0) or 
                               year_data.get('Common Equity Attributable to Parent Shareholders', 0) or 0)
                
                if total_equity > 0:
                    roe = (net_income / total_equity) * 100
                    roe_values.append(roe)
            
            if len(roe_values) >= 3:
                std_raw = np.std(roe_values, ddof=1)
                factors['roe_stability'] = min(std_raw, 20)  # 上限20%
            else:
                factors['roe_stability'] = None
        else:
            factors['roe_stability'] = None
    except:
        factors['roe_stability'] = None
    
    # 【13】revenue_yoy（营收同比增长）
    try:
        if listing_years >= 2:
            current_revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
            prev_revenue = stock_data.iloc[-2].get('Revenue from Business Activities - Total', 0) or 0
            
            if prev_revenue > 0:
                yoy_raw = (current_revenue - prev_revenue) / prev_revenue * 100
                factors['revenue_yoy'] = np.clip(yoy_raw, -100, 200)
            else:
                factors['revenue_yoy'] = None
        else:
            factors['revenue_yoy'] = None
    except:
        factors['revenue_yoy'] = None
    
    # 【14】revenue_cagr（营收CAGR）
    try:
        years_for_cagr = min(5, listing_years)
        if years_for_cagr >= 3:
            revenue_values = []
            for i in range(years_for_cagr):
                revenue = stock_data.iloc[-years_for_cagr+i].get('Revenue from Business Activities - Total', 0) or 0
                revenue_values.append(revenue)
            
            factors['revenue_cagr'] = calculate_robust_cagr(revenue_values, years_for_cagr, 'geometric')
        else:
            factors['revenue_cagr'] = None
    except:
        factors['revenue_cagr'] = None
    
    # 【15】net_income_yoy（净利润同比增长）
    try:
        if listing_years >= 2:
            current_ni = latest_data.get('Normalized Net Income - Bottom Line', 0) or 0
            prev_ni = stock_data.iloc[-2].get('Normalized Net Income - Bottom Line', 0) or 0
            
            if prev_ni != 0:
                factors['net_income_yoy'] = (current_ni - prev_ni) / abs(prev_ni) * 100
            else:
                factors['net_income_yoy'] = None
        else:
            factors['net_income_yoy'] = None
    except:
        factors['net_income_yoy'] = None
    
    # 【16】profit_revenue_ratio（利润营收比）
    try:
        net_income = latest_data.get('Normalized Net Income - Bottom Line', 0) or 0
        revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
        
        if revenue > 0:
            ratio_raw = (net_income / revenue) * 100
            factors['profit_revenue_ratio'] = np.clip(ratio_raw, -50, 50)
        else:
            factors['profit_revenue_ratio'] = None
    except:
        factors['profit_revenue_ratio'] = None
    
    # 【17】fcf_per_share（每股自由现金流）
    try:
        fcf = latest_data.get('Free Cash Flow', 0) or 0
        shares = latest_data.get('Shares used to calculate Diluted EPS - Total', 0) or 0
        
        if shares > 0:
            fcf_per_share_raw = fcf / shares
            factors['fcf_per_share'] = np.clip(fcf_per_share_raw, -50, 100)
        else:
            factors['fcf_per_share'] = None
    except:
        factors['fcf_per_share'] = None
    
    # 【18】fcf_cagr（自由现金流CAGR）
    try:
        years_for_cagr = min(5, listing_years)
        if years_for_cagr >= 3:
            fcf_values = []
            for i in range(years_for_cagr):
                fcf = stock_data.iloc[-years_for_cagr+i].get('Free Cash Flow', 0) or 0
                fcf_values.append(fcf)
            
            factors['fcf_cagr'] = calculate_robust_cagr(fcf_values, years_for_cagr, 'geometric')
        else:
            factors['fcf_cagr'] = None
    except:
        factors['fcf_cagr'] = None
    
    # 【19】operating_margin（营业利润率）
    try:
        operating_income = latest_data.get('Operating Income', 0) or 0
        revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
        
        if revenue > 0:
            margin_raw = (operating_income / revenue) * 100
            factors['operating_margin'] = np.clip(margin_raw, -50, 100)
        else:
            factors['operating_margin'] = None
    except:
        factors['operating_margin'] = None
    
    # 【20】operating_margin_std（营业利润率稳定性）
    try:
        years_for_stability = min(4, listing_years)
        if years_for_stability >= 2:
            margin_values = []
            
            for i in range(years_for_stability):
                year_data = stock_data.iloc[-i-1]
                operating_income = year_data.get('Operating Income', 0) or 0
                revenue = year_data.get('Revenue from Business Activities - Total', 0) or 0
                
                if revenue > 0:
                    margin = (operating_income / revenue) * 100
                    margin_values.append(margin)
            
            if len(margin_values) >= 2:
                factors['operating_margin_std'] = np.std(margin_values, ddof=1)
            else:
                factors['operating_margin_std'] = None
        else:
            factors['operating_margin_std'] = None
    except:
        factors['operating_margin_std'] = None
    
    # 【21】roic（资本回报率）
    try:
        net_income = latest_data.get('Normalized Net Income - Bottom Line', 0) or 0
        total_debt = latest_data.get('Debt - Total', 0) or 0
        total_equity = (latest_data.get('Shareholders\' Equity - Attributable to Parent Shareholders - Total', 0) or 
                       latest_data.get('Common Equity - Total', 0) or 
                       latest_data.get('Common Equity Attributable to Parent Shareholders', 0) or 0)
        
        invested_capital = total_debt + total_equity
        
        if invested_capital > 0:
            roic_raw = (net_income / invested_capital) * 100
            factors['roic'] = np.clip(roic_raw, -30, 50)
        else:
            factors['roic'] = None
    except:
        factors['roic'] = None
    
    # 【22】roic_cagr（ROIC CAGR）
    try:
        years_for_cagr = min(5, listing_years)
        if years_for_cagr >= 3:
            roic_values = []
            
            for i in range(years_for_cagr):
                year_data = stock_data.iloc[-years_for_cagr+i]
                net_income = year_data.get('Normalized Net Income - Bottom Line', 0) or 0
                total_debt = year_data.get('Debt - Total', 0) or 0
                total_equity = (year_data.get('Shareholders\' Equity - Attributable to Parent Shareholders - Total', 0) or 
                               year_data.get('Common Equity - Total', 0) or 
                               year_data.get('Common Equity Attributable to Parent Shareholders', 0) or 0)
                
                invested_capital = total_debt + total_equity
                
                if invested_capital > 0 and net_income > 0:  # 只考虑正净利润
                    roic = (net_income / invested_capital) * 100
                    roic_values.append(roic)
            
            factors['roic_cagr'] = calculate_robust_cagr(roic_values, len(roic_values), 'geometric')
        else:
            factors['roic_cagr'] = None
    except:
        factors['roic_cagr'] = None
    
    # 【23】effective_tax_rate（有效税率）
    try:
        tax_expense = latest_data.get('Income Taxes', 0) or 0
        ebit = latest_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
        
        if ebit > 0:
            tax_rate_raw = (tax_expense / ebit) * 100
            factors['effective_tax_rate'] = np.clip(tax_rate_raw, -50, 50)
        else:
            factors['effective_tax_rate'] = None
    except:
        factors['effective_tax_rate'] = None
    
    # 【24】effective_tax_rate_std（有效税率稳定性）
    try:
        years_for_stability = min(4, listing_years)
        if years_for_stability >= 2:
            tax_rate_values = []
            
            for i in range(years_for_stability):
                year_data = stock_data.iloc[-i-1]
                tax_expense = year_data.get('Income Taxes', 0) or 0
                ebit = year_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
                
                if ebit > 0:
                    # 应用合理性检查
                    tax_rate = (tax_expense / ebit) * 100
                    if -50 <= tax_rate <= 50:  # 只保留合理税率
                        tax_rate_values.append(tax_rate)
            
            if len(tax_rate_values) >= 2:
                factors['effective_tax_rate_std'] = np.std(tax_rate_values, ddof=1)
            else:
                factors['effective_tax_rate_std'] = None
        else:
            factors['effective_tax_rate_std'] = None
    except:
        factors['effective_tax_rate_std'] = None
    
    return factors

def main():
    """主函数：运行改进版因子计算"""
    
    print("=== 完整改进版24个因子计算 ===")
    print()
    
    # 连接数据库并获取数据
    client = connect_to_ap_research()
    
    # 获取股票列表
    query = "SELECT DISTINCT stock_symbol FROM stock_performance_2020_2025_cumulative ORDER BY stock_symbol"
    result = client.execute(query)
    stock_list = [row[0] for row in result]
    print(f"📊 获取到 {len(stock_list)} 只股票")
    
    # 获取基本面数据
    annual_query = """
    SELECT 
        stock_symbol,
        financial_period_absolute,
        statement_type,
        item_name,
        value,
        period_end_date
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE financial_period_absolute REGEXP '^FY[0-9]{4}$'
    ORDER BY stock_symbol, financial_period_absolute, statement_type, item_name
    """
    
    annual_result = client.execute(annual_query)
    annual_df = pd.DataFrame(annual_result, columns=[
        'stock_symbol', 'financial_period_absolute', 'statement_type', 'item_name', 'value', 'period_end_date'
    ])
    
    # 透视数据
    annual_pivot = annual_df.pivot_table(
        index=['stock_symbol', 'financial_period_absolute', 'period_end_date'],
        columns='item_name',
        values='value',
        aggfunc='first'
    ).reset_index()
    
    annual_pivot['fiscal_year'] = annual_pivot['financial_period_absolute'].str.extract(r'FY(\d{4})').astype(int)
    
    # 获取价格数据
    price_query = """
    SELECT stock_symbol, trade_date, close
    FROM priority_quality_stock_hfq
    ORDER BY stock_symbol, trade_date
    """
    
    price_result = client.execute(price_query)
    price_df = pd.DataFrame(price_result, columns=['stock_symbol', 'trade_date', 'close_price'])
    
    # 为年报数据匹配价格
    print("📊 匹配价格数据...")
    merged_data = []
    
    for stock in stock_list[:20]:  # 测试前20只股票
        stock_annual = annual_pivot[annual_pivot['stock_symbol'] == stock].copy()
        stock_price = price_df[price_df['stock_symbol'] == stock].copy()
        
        if len(stock_annual) > 0 and len(stock_price) > 0:
            stock_price['trade_date'] = pd.to_datetime(stock_price['trade_date'])
            
            for idx, row in stock_annual.iterrows():
                effective_date = pd.to_datetime(row['period_end_date'])
                future_prices = stock_price[stock_price['trade_date'] >= effective_date]
                if len(future_prices) > 0:
                    stock_annual.loc[idx, 'close_price'] = future_prices.iloc[0]['close_price']
            
            merged_data.append(stock_annual)
    
    if merged_data:
        final_data = pd.concat(merged_data, ignore_index=True)
    else:
        final_data = annual_pivot.copy()
        final_data['close_price'] = np.nan
    
    # 计算因子
    print(f"\n🔄 开始计算改进版因子...")
    print("=" * 80)
    
    all_factors = []
    successful_count = 0
    
    for stock_symbol in stock_list[:20]:  # 测试前20只股票
        try:
            stock_data = final_data[final_data['stock_symbol'] == stock_symbol]
            
            if len(stock_data) == 0:
                print(f"   ❌ {stock_symbol}: 无数据")
                continue
            
            factors = calculate_complete_improved_factors(stock_symbol, stock_data)
            factors['stock_symbol'] = stock_symbol
            all_factors.append(factors)
            successful_count += 1
            
        except Exception as e:
            print(f"   ❌ {stock_symbol}: 计算失败 - {str(e)}")
    
    print("=" * 80)
    print(f"✅ 成功计算 {successful_count} 只股票的因子")
    
    # 保存结果
    if all_factors:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 转换为DataFrame
        factors_df = pd.DataFrame(all_factors)
        
        # 重新排列列顺序
        columns_order = ['stock_symbol'] + [col for col in factors_df.columns if col != 'stock_symbol']
        factors_df = factors_df[columns_order]
        
        # 保存到CSV
        filename = f"colin/complete_improved_24_factors_{timestamp}.csv"
        factors_df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"✅ 因子数据已保存到: {filename}")
        
        # 打印统计信息
        print(f"\n📊 因子覆盖率统计:")
        print("-" * 70)
        for col in factors_df.columns:
            if col != 'stock_symbol':
                valid_count = factors_df[col].notna().sum()
                coverage = (valid_count / len(factors_df)) * 100
                print(f"{col:<35} {valid_count:>3}/{len(factors_df):>3} ({coverage:>5.1f}%)")
        
        return factors_df
    
    return None

if __name__ == "__main__":
    result = main()

