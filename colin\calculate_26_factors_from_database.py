from clickhouse_driver import Client
import pandas as pd
import numpy as np
from datetime import datetime, date
import traceback

def connect_to_ap_research():
    """连接到ap_research数据库"""
    client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')
    print("✅ 连接ap_research数据库成功")
    return client

def get_stock_list(client):
    """从数据库获取股票列表"""
    print("\n1. 获取821个股票列表...")
    
    query = "SELECT DISTINCT stock_symbol FROM stock_performance_2020_2025_cumulative ORDER BY stock_symbol"
    result = client.execute(query)
    stock_list = [row[0] for row in result]
    
    print(f"   📊 获取到 {len(stock_list)} 只股票")
    return stock_list

def get_fundamental_data(client):
    """获取基本面数据"""
    print("\n2. 获取基本面数据...")
    
    # 获取年报数据（用于主要因子计算）
    annual_query = """
    SELECT 
        stock_symbol,
        financial_period_absolute,
        statement_type,
        item_name,
        value,
        period_end_date
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE financial_period_absolute REGEXP '^FY[0-9]{4}$'
    ORDER BY stock_symbol, financial_period_absolute, statement_type, item_name
    """
    
    annual_result = client.execute(annual_query)
    print(f"   📊 获取到 {len(annual_result)} 条年报记录")
    
    # 转换为DataFrame
    annual_df = pd.DataFrame(annual_result, columns=[
        'stock_symbol', 'financial_period_absolute', 'statement_type', 'item_name', 'value', 'period_end_date'
    ])
    
    return annual_df

def get_price_data(client):
    """获取价格数据"""
    print("\n3. 获取价格数据...")
    
    price_query = """
    SELECT 
        stock_symbol,
        trade_date,
        close
    FROM priority_quality_stock_hfq
    ORDER BY stock_symbol, trade_date
    """
    
    price_result = client.execute(price_query)
    print(f"   📊 获取到 {len(price_result)} 条价格记录")
    
    # 转换为DataFrame (注意：数据库字段是close，但我们命名为close_price保持一致性)
    price_df = pd.DataFrame(price_result, columns=['stock_symbol', 'trade_date', 'close_price'])
    
    return price_df

def prepare_data_for_factors(annual_df, price_df):
    """准备因子计算所需的数据结构"""
    print("\n4. 准备因子计算数据结构...")
    
    # 透视年报数据
    annual_pivot = annual_df.pivot_table(
        index=['stock_symbol', 'financial_period_absolute', 'period_end_date'],
        columns='item_name',
        values='value',
        aggfunc='first'
    ).reset_index()
    
    # 提取年份
    annual_pivot['fiscal_year'] = annual_pivot['financial_period_absolute'].str.extract(r'FY(\d{4})').astype(int)
    
    # 添加价格数据（获取每个财年末的价格）
    annual_pivot['effective_date'] = pd.to_datetime(annual_pivot['period_end_date'])
    
    # 为每个股票添加价格数据
    merged_data = []
    
    for stock in annual_pivot['stock_symbol'].unique():
        stock_annual = annual_pivot[annual_pivot['stock_symbol'] == stock].copy()
        stock_price = price_df[price_df['stock_symbol'] == stock].copy()
        
        if len(stock_price) > 0:
            stock_price['trade_date'] = pd.to_datetime(stock_price['trade_date'])
            
            for idx, row in stock_annual.iterrows():
                # 找到最接近effective_date的价格
                if pd.notna(row['effective_date']):
                    nearest_price = stock_price[stock_price['trade_date'] <= row['effective_date']]
                    if len(nearest_price) > 0:
                        latest_price = nearest_price.iloc[-1]['close_price']
                        stock_annual.loc[idx, 'close_price'] = latest_price
        
        merged_data.append(stock_annual)
    
    if merged_data:
        final_df = pd.concat(merged_data, ignore_index=True)
    else:
        final_df = annual_pivot.copy()
        final_df['close_price'] = np.nan
    
    print(f"   📊 合并后数据: {len(final_df)} 条记录")
    return final_df

def calculate_26_factors_for_stock(stock_data, stock_symbol):
    """为单个股票计算26个因子"""
    factors = {'stock_symbol': stock_symbol}
    
    if len(stock_data) == 0:
        print(f"   ⚠️ {stock_symbol}: 无数据")
        return factors
    
    try:
        # 按年份排序
        stock_data = stock_data.sort_values('fiscal_year')
        latest_data = stock_data.iloc[-1]
        
        # 计算上市年限
        listing_years = len(stock_data)
        
        print(f"   📊 {stock_symbol}: {listing_years}年数据")
        
        # 1. 技术溢价 (tech_premium) - 研发强度
        try:
            rd_expense = latest_data.get('Research & Development Expense', 0) or 0
            rd_supplemental = latest_data.get('Research & Development Expense - Supplemental', 0) or 0
            total_rd = rd_expense + rd_supplemental
            revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
            
            if revenue > 0:
                factors['tech_premium'] = (total_rd / revenue) * 100
            else:
                factors['tech_premium'] = None
        except:
            factors['tech_premium'] = None
        
        # 2. 技术差距警告 (tech_gap_warning)
        try:
            if listing_years >= 2:
                current_rd_ratio = factors.get('tech_premium', 0) or 0
                prev_data = stock_data.iloc[-2]
                prev_rd = (prev_data.get('Research & Development Expense', 0) or 0) + (prev_data.get('Research & Development Expense - Supplemental', 0) or 0)
                prev_revenue = prev_data.get('Revenue from Business Activities - Total', 0) or 0
                
                if prev_revenue > 0:
                    prev_rd_ratio = (prev_rd / prev_revenue) * 100
                    if current_rd_ratio > 0 and prev_rd_ratio > 0:
                        factors['tech_gap_warning'] = (current_rd_ratio - prev_rd_ratio) / prev_rd_ratio * 100
                    else:
                        factors['tech_gap_warning'] = None
                else:
                    factors['tech_gap_warning'] = None
            else:
                factors['tech_gap_warning'] = None
        except:
            factors['tech_gap_warning'] = None
        
        # 3. 专利密度 (patent_density) - 使用研发费用/总资产作为代理
        try:
            total_rd = (latest_data.get('Research & Development Expense', 0) or 0) + (latest_data.get('Research & Development Expense - Supplemental', 0) or 0)
            total_assets = latest_data.get('Total Assets', 0) or 0
            
            if total_assets > 0:
                factors['patent_density'] = (total_rd / total_assets) * 100
            else:
                factors['patent_density'] = None
        except:
            factors['patent_density'] = None
        
        # 4. 生态现金比率 (ecosystem_cash_ratio)
        try:
            cash = latest_data.get('Cash & Cash Equivalents', 0) or 0
            short_term_investments = latest_data.get('Short-Term Investments', 0) or 0
            total_cash = cash + short_term_investments
            revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
            
            if revenue > 0:
                factors['ecosystem_cash_ratio'] = (total_cash / revenue) * 100
            else:
                factors['ecosystem_cash_ratio'] = None
        except:
            factors['ecosystem_cash_ratio'] = None
        
        # 5. 调整后ROCE (adjusted_roce)
        try:
            net_income = latest_data.get('Normalized Net Income - Bottom Line', 0) or 0
            # 尝试多个股东权益字段名
            total_equity = (latest_data.get('Shareholders\' Equity - Attributable to Parent Shareholders - Total', 0) or 
                          latest_data.get('Common Equity - Total', 0) or 
                          latest_data.get('Common Equity Attributable to Parent Shareholders', 0) or 0)
            
            if total_equity > 0:
                factors['adjusted_roce'] = (net_income / total_equity) * 100
            else:
                factors['adjusted_roce'] = None
        except:
            factors['adjusted_roce'] = None
        
        # 6. 自由现金流质量 (fcf_quality)
        try:
            fcf = latest_data.get('Free Cash Flow', 0) or 0
            net_income = latest_data.get('Normalized Net Income - Bottom Line', 0) or 0
            
            if net_income > 0:
                factors['fcf_quality'] = fcf / net_income
            else:
                factors['fcf_quality'] = None
        except:
            factors['fcf_quality'] = None
        
        # 7. 动态安全边际 (dynamic_safety_margin)
        try:
            # 需要tech_premium和ecosystem_cash_ratio都非空
            if factors.get('tech_premium') is not None and factors.get('ecosystem_cash_ratio') is not None:
                factors['dynamic_safety_margin'] = factors['tech_premium'] + factors['ecosystem_cash_ratio']
            else:
                factors['dynamic_safety_margin'] = None
        except:
            factors['dynamic_safety_margin'] = None
        
        # 8. 营收增长连续性 (revenue_growth_continuity)
        try:
            if listing_years >= 2:
                revenue_growth_rates = []
                for i in range(1, min(4, listing_years)):  # 最多看3年
                    current = stock_data.iloc[-i]['Revenue from Business Activities - Total'] or 0
                    previous = stock_data.iloc[-i-1]['Revenue from Business Activities - Total'] or 0
                    
                    if previous > 0:
                        growth_rate = (current - previous) / previous * 100
                        revenue_growth_rates.append(growth_rate)
                
                if len(revenue_growth_rates) >= 2:
                    factors['revenue_growth_continuity'] = np.std(revenue_growth_rates)
                else:
                    factors['revenue_growth_continuity'] = None
            else:
                factors['revenue_growth_continuity'] = None
        except:
            factors['revenue_growth_continuity'] = None
        
        # 9. 有效税率改善 (effective_tax_rate_improvement)
        try:
            tax_expense = latest_data.get('Income Taxes', 0) or 0
            pretax_income = latest_data.get('Income before Taxes', 0) or 0
            
            if pretax_income > 0:
                factors['effective_tax_rate_improvement'] = (tax_expense / pretax_income) * 100
            else:
                factors['effective_tax_rate_improvement'] = None
        except:
            factors['effective_tax_rate_improvement'] = None
        
        # 10. 财务健康度 (financial_health)
        try:
            current_assets = latest_data.get('Total Current Assets', 0) or 0
            current_liabilities = latest_data.get('Total Current Liabilities', 0) or 0
            
            if current_liabilities > 0:
                factors['financial_health'] = current_assets / current_liabilities
            else:
                factors['financial_health'] = None
        except:
            factors['financial_health'] = None
        
        # 11. 估值泡沫信号 (valuation_bubble_signal) - 使用最新季度数据
        try:
            close_price = latest_data.get('close_price', 0) or 0
            eps = latest_data.get('EPS - Diluted - including Extraordinary Items Applicable to Common - Total', 0) or 0
            
            if eps > 0:
                pe_ratio = close_price / eps
                factors['valuation_bubble_signal'] = pe_ratio
            else:
                factors['valuation_bubble_signal'] = None
        except:
            factors['valuation_bubble_signal'] = None
        
        # 12. 研发强度 (rd_intensity) - 已删除，与tech_premium重复
        
        # 13. ROCE稳定性 (roce_stability)
        try:
            if listing_years >= 3:
                roce_values = []
                for i in range(min(5, listing_years)):
                    year_data = stock_data.iloc[-i-1]
                    net_income = year_data.get('Normalized Net Income - Bottom Line', 0) or 0
                    # 尝试多个股东权益字段名
                    total_equity = (year_data.get('Shareholders\' Equity - Attributable to Parent Shareholders - Total', 0) or 
                                  year_data.get('Common Equity - Total', 0) or 
                                  year_data.get('Common Equity Attributable to Parent Shareholders', 0) or 0)
                    
                    if total_equity > 0:
                        roce = (net_income / total_equity) * 100
                        roce_values.append(roce)
                
                if len(roce_values) >= 3:
                    factors['roce_stability'] = np.std(roce_values)
                else:
                    factors['roce_stability'] = None
            else:
                factors['roce_stability'] = None
        except:
            factors['roce_stability'] = None
        
        # 14. 营收同比增长 (revenue_yoy)
        try:
            if listing_years >= 2:
                current_revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
                prev_revenue = stock_data.iloc[-2].get('Revenue from Business Activities - Total', 0) or 0
                
                if prev_revenue > 0:
                    factors['revenue_yoy'] = (current_revenue - prev_revenue) / prev_revenue * 100
                else:
                    factors['revenue_yoy'] = None
            else:
                factors['revenue_yoy'] = None
        except:
            factors['revenue_yoy'] = None
        
        # 15. 营收CAGR (revenue_cagr)
        try:
            if listing_years >= 3:
                years_to_use = min(8, listing_years)
                current_revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
                past_revenue = stock_data.iloc[-years_to_use].get('Revenue from Business Activities - Total', 0) or 0
                
                if past_revenue > 0 and current_revenue > 0:
                    cagr = ((current_revenue / past_revenue) ** (1/(years_to_use-1)) - 1) * 100
                    factors['revenue_cagr'] = cagr
                else:
                    factors['revenue_cagr'] = None
            else:
                factors['revenue_cagr'] = None
        except:
            factors['revenue_cagr'] = None
        
        # 16. 净利润同比增长 (net_income_yoy)
        try:
            if listing_years >= 2:
                current_ni = latest_data.get('Normalized Net Income - Bottom Line', 0) or 0
                prev_ni = stock_data.iloc[-2].get('Normalized Net Income - Bottom Line', 0) or 0
                
                if prev_ni != 0:
                    factors['net_income_yoy'] = (current_ni - prev_ni) / abs(prev_ni) * 100
                else:
                    factors['net_income_yoy'] = None
            else:
                factors['net_income_yoy'] = None
        except:
            factors['net_income_yoy'] = None
        
        # 17. 利润营收比 (profit_revenue_ratio)
        try:
            net_income = latest_data.get('Normalized Net Income - Bottom Line', 0) or 0
            revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
            
            if revenue > 0:
                factors['profit_revenue_ratio'] = (net_income / revenue) * 100
            else:
                factors['profit_revenue_ratio'] = None
        except:
            factors['profit_revenue_ratio'] = None
        
        # 18. 每股自由现金流 (fcf_per_share)
        try:
            fcf = latest_data.get('Free Cash Flow', 0) or 0
            shares = latest_data.get('Shares used to calculate Diluted EPS - Total', 0) or 0
            
            if shares > 0:
                factors['fcf_per_share'] = fcf / shares
            else:
                factors['fcf_per_share'] = None
        except:
            factors['fcf_per_share'] = None
        
        # 19. 自由现金流CAGR (fcf_cagr)
        try:
            if listing_years >= 3:
                years_to_use = min(8, listing_years)
                current_fcf = latest_data.get('Free Cash Flow', 0) or 0
                past_fcf = stock_data.iloc[-years_to_use].get('Free Cash Flow', 0) or 0
                
                if past_fcf > 0 and current_fcf > 0:
                    cagr = ((current_fcf / past_fcf) ** (1/(years_to_use-1)) - 1) * 100
                    factors['fcf_cagr'] = cagr
                else:
                    factors['fcf_cagr'] = None
            else:
                factors['fcf_cagr'] = None
        except:
            factors['fcf_cagr'] = None
        
        # 20. 自由现金流净利润比 (fcf_net_income_ratio) - 已删除，与fcf_quality重复
        
        # 21. 营业利润率 (operating_margin)
        try:
            operating_income = latest_data.get('Operating Income', 0) or 0
            revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
            
            if revenue > 0:
                factors['operating_margin'] = (operating_income / revenue) * 100
            else:
                factors['operating_margin'] = None
        except:
            factors['operating_margin'] = None
        
        # 22. 营业利润率标准差 (operating_margin_std)
        try:
            if listing_years >= 2:
                margins = []
                for i in range(min(4, listing_years)):
                    year_data = stock_data.iloc[-i-1]
                    operating_income = year_data.get('Operating Income', 0) or 0
                    revenue = year_data.get('Revenue from Business Activities - Total', 0) or 0
                    
                    if revenue > 0:
                        margin = (operating_income / revenue) * 100
                        margins.append(margin)
                
                if len(margins) >= 2:
                    factors['operating_margin_std'] = np.std(margins)
                else:
                    factors['operating_margin_std'] = None
            else:
                factors['operating_margin_std'] = None
        except:
            factors['operating_margin_std'] = None
        
        # 23. ROIC (roic)
        try:
            net_income = latest_data.get('Normalized Net Income - Bottom Line', 0) or 0
            total_debt = latest_data.get('Debt - Total', 0) or 0
            # 尝试多个股东权益字段名
            total_equity = (latest_data.get('Shareholders\' Equity - Attributable to Parent Shareholders - Total', 0) or 
                          latest_data.get('Common Equity - Total', 0) or 
                          latest_data.get('Common Equity Attributable to Parent Shareholders', 0) or 0)
            invested_capital = total_debt + total_equity
            
            if invested_capital > 0:
                factors['roic'] = (net_income / invested_capital) * 100
            else:
                factors['roic'] = None
        except:
            factors['roic'] = None
        
        # 24. ROIC CAGR (roic_cagr)
        try:
            if listing_years >= 3:
                years_to_use = min(8, listing_years)
                current_roic = factors.get('roic', 0) or 0
                
                # 计算过去的ROIC
                past_data = stock_data.iloc[-years_to_use]
                past_ni = past_data.get('Normalized Net Income - Bottom Line', 0) or 0
                past_debt = past_data.get('Debt - Total', 0) or 0
                # 尝试多个股东权益字段名
                past_equity = (past_data.get('Shareholders\' Equity - Attributable to Parent Shareholders - Total', 0) or 
                             past_data.get('Common Equity - Total', 0) or 
                             past_data.get('Common Equity Attributable to Parent Shareholders', 0) or 0)
                past_invested_capital = past_debt + past_equity
                
                if past_invested_capital > 0:
                    past_roic = (past_ni / past_invested_capital) * 100
                    
                    if past_roic > 0 and current_roic > 0:
                        cagr = ((current_roic / past_roic) ** (1/(years_to_use-1)) - 1) * 100
                        factors['roic_cagr'] = cagr
                    else:
                        factors['roic_cagr'] = None
                else:
                    factors['roic_cagr'] = None
            else:
                factors['roic_cagr'] = None
        except:
            factors['roic_cagr'] = None
        
        # 25. 有效税率 (effective_tax_rate)
        try:
            tax_expense = latest_data.get('Income Taxes', 0) or 0
            ebit = latest_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
            
            if ebit > 0:
                factors['effective_tax_rate'] = (tax_expense / ebit) * 100
            else:
                factors['effective_tax_rate'] = None
        except:
            factors['effective_tax_rate'] = None
        
        # 26. 有效税率标准差 (effective_tax_rate_std)
        try:
            if listing_years >= 2:
                tax_rates = []
                for i in range(min(4, listing_years)):
                    year_data = stock_data.iloc[-i-1]
                    tax_expense = year_data.get('Income Taxes', 0) or 0
                    ebit = year_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
                    if ebit > 0:
                        tax_rate = (tax_expense / ebit) * 100
                        tax_rates.append(tax_rate)
                
                if len(tax_rates) >= 2:
                    factors['effective_tax_rate_std'] = np.std(tax_rates)
                else:
                    factors['effective_tax_rate_std'] = None
            else:
                factors['effective_tax_rate_std'] = None
        except:
            factors['effective_tax_rate_std'] = None
        
    except Exception as e:
        print(f"   ❌ {stock_symbol}: 计算因子时出错 - {e}")
    
    return factors

def calculate_all_factors(client):
    """计算所有股票的24个因子"""
    print("\n=== 开始计算821只股票的24个因子 ===")
    
    # 1. 获取数据
    stock_list = get_stock_list(client)
    annual_df = get_fundamental_data(client)
    price_df = get_price_data(client)
    
    # 2. 准备数据
    merged_df = prepare_data_for_factors(annual_df, price_df)
    
    # 3. 计算因子
    print("\n5. 计算24个因子...")
    all_factors = []
    
    for i, stock_symbol in enumerate(stock_list, 1):
        if i % 100 == 0:
            print(f"   进度: {i}/{len(stock_list)} ({i/len(stock_list)*100:.1f}%)")
        
        stock_data = merged_df[merged_df['stock_symbol'] == stock_symbol]
        factors = calculate_26_factors_for_stock(stock_data, stock_symbol)
        all_factors.append(factors)
    
    # 转换为DataFrame
    factors_df = pd.DataFrame(all_factors)
    
    return factors_df

def analyze_factor_coverage(factors_df):
    """分析因子覆盖率"""
    print("\n6. 分析因子覆盖率...")
    
    factor_names = [col for col in factors_df.columns if col != 'stock_symbol']
    
    coverage_stats = []
    for factor in factor_names:
        non_null_count = factors_df[factor].notna().sum()
        total_count = len(factors_df)
        coverage_rate = non_null_count / total_count * 100
        
        coverage_stats.append({
            'factor': factor,
            'non_null_count': non_null_count,
            'total_count': total_count,
            'coverage_rate': coverage_rate
        })
    
    coverage_df = pd.DataFrame(coverage_stats)
    coverage_df = coverage_df.sort_values('coverage_rate', ascending=False)
    
    print(f"\n   📊 因子覆盖率统计:")
    for _, row in coverage_df.iterrows():
        print(f"      {row['factor']}: {row['non_null_count']}/{row['total_count']} ({row['coverage_rate']:.1f}%)")
    
    return coverage_df

def save_results(factors_df, coverage_df):
    """保存结果"""
    print("\n7. 保存结果...")
    
    # 保存因子数据
    factors_file = f"colin/factors_24_complete_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    factors_df.to_csv(factors_file, index=False, encoding='utf-8-sig')
    print(f"   ✅ 因子数据已保存到: {factors_file}")
    
    # 保存覆盖率统计
    coverage_file = f"colin/factor_coverage_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    coverage_df.to_csv(coverage_file, index=False, encoding='utf-8-sig')
    print(f"   ✅ 覆盖率统计已保存到: {coverage_file}")
    
    return factors_file, coverage_file

def main():
    """主函数"""
    print("=== Colin McLean成长价值优势投资法则因子实现（从数据库版）===")
    print("计算所有24个核心因子，分析821个股票的覆盖率")
    
    try:
        # 1. 连接数据库
        client = connect_to_ap_research()
        
        # 2. 计算所有因子
        factors_df = calculate_all_factors(client)
        
        # 3. 分析覆盖率
        coverage_df = analyze_factor_coverage(factors_df)
        
        # 4. 保存结果
        factors_file, coverage_file = save_results(factors_df, coverage_df)
        
        print(f"\n=== 计算完成 ===")
        print(f"✅ 计算了 {len(factors_df)} 只股票的 24 个因子")
        print(f"✅ 因子数据: {factors_file}")
        print(f"✅ 覆盖率统计: {coverage_file}")
        print(f"🎯 现在可以基于这些因子进行投资策略分析！")
        
        client.disconnect()
        
    except Exception as e:
        print(f"❌ 计算过程中出错: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
