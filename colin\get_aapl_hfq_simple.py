#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取AAPL最新行情数据脚本 - 简化版本
直接查询hfq表获取苹果公司的最新价格数据
"""

from clickhouse_driver import Client
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def connect_to_ap_research():
    """连接到ap_research数据库"""
    try:
        client = Client(
            host='************',
            port=9000,
            user='default',
            password='5ur2pK8VZQdy2',
            database='ap_research'
        )
        print("✅ 成功连接到ap_research数据库")
        return client
    except Exception as e:
        print(f"❌ 连接ap_research数据库失败: {e}")
        return None

def get_aapl_from_hfq_table(client):
    """从hfq表获取AAPL数据"""
    print("\n📈 尝试从hfq表获取AAPL数据...")
    
    # 尝试不同的表名
    possible_tables = [
        'priority_quality_stock_hfq',
        'hfq_intrday_1_day', 
        'stock_hfq',
        'hfq_data'
    ]
    
    for table_name in possible_tables:
        print(f"\n🔍 尝试查询表: {table_name}")
        
        try:
            # 简单查询检查表是否存在数据
            check_query = f"""
            SELECT COUNT(*) 
            FROM {table_name} 
            WHERE stock_symbol = 'AAPL' 
            LIMIT 1
            """
            
            result = client.execute(check_query)
            if result and result[0][0] > 0:
                print(f"✅ 表 {table_name} 中找到AAPL数据")
                
                # 获取最新数据
                latest_query = f"""
                SELECT 
                    trade_date,
                    stock_symbol,
                    open,
                    high,
                    low,
                    close,
                    volume,
                    turnover
                FROM {table_name}
                WHERE stock_symbol = 'AAPL'
                ORDER BY trade_date DESC
                LIMIT 10
                """
                
                latest_result = client.execute(latest_query)
                if latest_result:
                    print(f"✅ 成功获取到 {len(latest_result)} 条AAPL数据")
                    return latest_result, table_name
                else:
                    print(f"⚠️ 表 {table_name} 中未获取到AAPL数据")
            else:
                print(f"⚠️ 表 {table_name} 中没有AAPL数据")
                
        except Exception as e:
            print(f"❌ 查询表 {table_name} 失败: {e}")
            continue
    
    return None, None

def get_aapl_from_lseg_database():
    """尝试从lseg数据库获取AAPL数据"""
    print("\n🔄 尝试连接lseg数据库...")
    
    try:
        client = Client(
            host='************',
            port=9000,
            user='default',
            password='5ur2pK8VZQdy2',
            database='lseg'
        )
        print("✅ 成功连接到lseg数据库")
        
        # 查询hfq表
        query = """
        SELECT 
            trade_date,
            ric,
            open,
            high,
            low,
            close,
            volume,
            turnover
        FROM hfq_intrday_1_day
        WHERE splitByChar('.', ric)[1] = 'AAPL'
        ORDER BY trade_date DESC
        LIMIT 10
        """
        
        result = client.execute(query)
        if result:
            print(f"✅ 从lseg数据库成功获取到 {len(result)} 条AAPL数据")
            return result, 'lseg.hfq_intrday_1_day'
        else:
            print("⚠️ lseg数据库中未找到AAPL数据")
            return None, None
            
    except Exception as e:
        print(f"❌ 连接lseg数据库失败: {e}")
        return None, None

def process_aapl_data(data, table_name):
    """处理AAPL数据"""
    if not data:
        return None
        
    # 根据表名确定列名
    if 'lseg' in table_name:
        columns = ['trade_date', 'ric', 'open', 'high', 'low', 'close', 'volume', 'turnover']
    else:
        columns = ['trade_date', 'stock_symbol', 'open', 'high', 'low', 'close', 'volume', 'turnover']
    
    # 转换为DataFrame
    df = pd.DataFrame(data, columns=columns)
    
    # 转换日期格式
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    
    # 计算日收益率
    df['daily_return'] = df['close'].pct_change() * 100
    
    # 计算累计收益率（相对于最新价格）
    df['cumulative_return'] = ((df['close'] / df['close'].iloc[0]) - 1) * 100
    
    return df

def display_aapl_summary(df, table_name):
    """显示AAPL数据摘要"""
    if df is None or df.empty:
        return

    print("\n" + "="*80)
    print(f"🍎 AAPL最新行情数据摘要 (来源: {table_name})")
    print("="*80)

    # 基本信息
    print(f"📊 数据条数: {len(df):,}")
    print(f"📅 时间范围: {df['trade_date'].min().strftime('%Y-%m-%d')} 到 {df['trade_date'].max().strftime('%Y-%m-%d')}")

    # 最新价格信息
    latest_data = df.iloc[0]
    print(f"\n💰 最新价格信息 (日期: {latest_data['trade_date'].strftime('%Y-%m-%d')}):")
    print(f"  开盘价: ${latest_data['open']:.2f}")
    print(f"  最高价: ${latest_data['high']:.2f}")
    print(f"  最低价: ${latest_data['low']:.2f}")
    print(f"  收盘价: ${latest_data['close']:.2f}")
    print(f"  成交量: {latest_data['volume']:,.0f}")
    if 'turnover' in df.columns:
        print(f"  成交额: ${latest_data['turnover']:,.2f}")

    # 价格统计
    highest_price = df['high'].max()
    lowest_price = df['low'].min()
    avg_price = df['close'].mean()
    price_change = df['close'].iloc[0] - df['close'].iloc[-1]
    price_change_pct = (price_change / df['close'].iloc[-1]) * 100

    print(f"\n📈 价格统计:")
    print(f"  期间最高价: ${highest_price:.2f}")
    print(f"  期间最低价: ${lowest_price:.2f}")
    print(f"  期间平均价: ${avg_price:.2f}")
    print(f"  最新价格变化: ${price_change:.2f} ({price_change_pct:+.2f}%)")

    # 收益率统计
    total_return = df['daily_return'].sum()
    avg_daily_return = df['daily_return'].mean()
    volatility = df['daily_return'].std()

    print(f"\n📊 收益率统计:")
    print(f"  累计收益率: {total_return:.2f}%")
    print(f"  日均收益率: {avg_daily_return:.2f}%")
    print(f"  日收益率波动: {volatility:.2f}%")

    # 成交量统计
    total_volume = df['volume'].sum()
    avg_volume = df['volume'].mean()

    print(f"\n📊 成交量统计:")
    print(f"  总成交量: {total_volume:,.0f}")
    print(f"  日均成交量: {avg_volume:,.0f}")

    # 显示详细数据
    print(f"\n📋 详细数据:")
    print(df.to_string(index=False))

def main():
    """主函数"""
    print("🍎 AAPL最新行情数据查询 (使用hfq表)")
    print("="*60)

    # 首先尝试从ap_research数据库获取
    client = connect_to_ap_research()
    if client:
        data, table_name = get_aapl_from_hfq_table(client)
        if data:
            df = process_aapl_data(data, table_name)
            display_aapl_summary(df, table_name)
            
            # 保存到CSV文件
            if df is not None and not df.empty:
                filename = f"aapl_hfq_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                df.to_csv(filename, index=False)
                print(f"\n💾 数据已保存到: {filename}")
            
            print("\n✅ 查询完成!")
            return

    # 如果ap_research没有数据，尝试lseg数据库
    print("\n🔄 ap_research数据库未找到数据，尝试lseg数据库...")
    data, table_name = get_aapl_from_lseg_database()
    
    if data:
        df = process_aapl_data(data, table_name)
        display_aapl_summary(df, table_name)
        
        # 保存到CSV文件
        if df is not None and not df.empty:
            filename = f"aapl_lseg_hfq_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            df.to_csv(filename, index=False)
            print(f"\n💾 数据已保存到: {filename}")
        
        print("\n✅ 查询完成!")
    else:
        print("\n❌ 未能从任何数据库获取到AAPL数据")

if __name__ == "__main__":
    main()
