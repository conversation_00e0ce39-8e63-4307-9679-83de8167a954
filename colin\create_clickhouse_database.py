from clickhouse_driver import Client
import sys

def create_database(database_name):
    """
    在ClickHouse中创建新数据库
    """
    try:
        # 连接ClickHouse数据库
        client = Client(
            host='************',
            port=9000,
            user='default',
            password='5ur2pK8WZQdy2',
            database='default'  # 连接到default数据库来执行创建操作
        )
        
        print(f"正在连接ClickHouse服务器...")
        
        # 测试连接
        result = client.execute('SELECT 1')
        print("✅ ClickHouse连接成功!")
        
        # 检查数据库是否已存在
        existing_databases = client.execute("SHOW DATABASES")
        existing_db_names = [db[0] for db in existing_databases]
        
        print(f"\n当前存在的数据库:")
        for i, db_name in enumerate(existing_db_names, 1):
            print(f"  {i}. {db_name}")
        
        if database_name in existing_db_names:
            print(f"\n⚠️  数据库 '{database_name}' 已经存在!")
            choice = input("是否要继续？这不会覆盖现有数据库 (y/n): ")
            if choice.lower() != 'y':
                print("操作已取消")
                return
        
        # 创建数据库
        create_sql = f"CREATE DATABASE IF NOT EXISTS `{database_name}`"
        print(f"\n正在执行SQL: {create_sql}")
        
        client.execute(create_sql)
        print(f"✅ 数据库 '{database_name}' 创建成功!")
        
        # 验证数据库是否创建成功
        updated_databases = client.execute("SHOW DATABASES")
        updated_db_names = [db[0] for db in updated_databases]
        
        if database_name in updated_db_names:
            print(f"✅ 验证成功: 数据库 '{database_name}' 已存在于系统中")
        else:
            print(f"❌ 验证失败: 数据库 '{database_name}' 未找到")
            
        print(f"\n更新后的数据库列表:")
        for i, db_name in enumerate(updated_db_names, 1):
            status = "🆕 新建" if db_name == database_name and db_name not in existing_db_names else ""
            print(f"  {i}. {db_name} {status}")
            
        # 测试连接到新数据库
        try:
            test_client = Client(
                host='************',
                port=9000,
                user='default',
                password='5ur2pK8WZQdy2',
                database=database_name
            )
            test_result = test_client.execute('SELECT 1')
            print(f"✅ 成功连接到新数据库 '{database_name}'")
            
            # 显示新数据库中的表（应该为空）
            tables = test_client.execute("SHOW TABLES")
            print(f"📊 数据库 '{database_name}' 中的表数量: {len(tables)}")
            if tables:
                for table in tables:
                    print(f"  - {table[0]}")
            else:
                print("  数据库为空，没有表")
                
        except Exception as e:
            print(f"⚠️  无法连接到新数据库: {e}")
        
    except Exception as e:
        print(f"❌ 创建数据库时发生错误: {e}")
        print("请检查:")
        print("  1. ClickHouse服务是否正在运行")
        print("  2. 网络连接是否正常")
        print("  3. 用户权限是否足够")

if __name__ == "__main__":
    # 如果通过命令行参数指定数据库名称
    if len(sys.argv) > 1:
        database_name = sys.argv[1]
    else:
        # 交互式输入数据库名称
        database_name = input("请输入要创建的数据库名称: ").strip()
    
    if not database_name:
        print("❌ 数据库名称不能为空")
        sys.exit(1)
    
    # 验证数据库名称格式
    if not database_name.replace('_', '').replace('-', '').isalnum():
        print("❌ 数据库名称只能包含字母、数字、下划线和横线")
        sys.exit(1)
    
    print(f"准备创建数据库: {database_name}")
    create_database(database_name)