"""
检查ap_research数据库中的所有表
"""

from clickhouse_driver import Client

def connect_to_ap_research():
    """连接到ap_research数据库"""
    try:
        client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')
        print("✅ 连接ap_research数据库成功")
        return client
    except Exception as e:
        print(f"❌ 连接数据库失败: {e}")
        return None

def main():
    """主函数"""
    print("=== 检查ap_research数据库表 ===")
    
    try:
        client = connect_to_ap_research()
        if not client:
            return
        
        # 查看所有表
        print("\n📊 查看所有表:")
        tables_result = client.execute("SHOW TABLES")
        for table in tables_result:
            table_name = table[0]
            print(f"   - {table_name}")
            
            # 查看每个表的记录数
            try:
                count_result = client.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = count_result[0][0]
                print(f"     记录数: {count:,}")
            except Exception as e:
                print(f"     记录数: 查询失败 - {e}")
        
        # 检查财报相关的表
        print("\n🔍 检查可能的财报表:")
        possible_tables = []
        for table in tables_result:
            table_name = table[0].lower()
            if any(keyword in table_name for keyword in ['fundamental', 'financial', 'statement', 'report']):
                possible_tables.append(table[0])
        
        if possible_tables:
            for table_name in possible_tables:
                print(f"\n📋 表 {table_name} 的结构:")
                try:
                    desc_result = client.execute(f"DESCRIBE {table_name}")
                    for row in desc_result:
                        print(f"   {row[0]} - {row[1]}")
                except Exception as e:
                    print(f"   查询结构失败: {e}")
        else:
            print("   未找到明显的财报相关表名")
        
        client.disconnect()
        
    except Exception as e:
        print(f"❌ 检查过程出错: {e}")

if __name__ == "__main__":
    main()

