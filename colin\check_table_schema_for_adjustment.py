from clickhouse_driver import Client

def connect_to_ap_research():
    """连接到ap_research数据库"""
    client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')
    print("✅ 连接ap_research数据库成功")
    return client

def check_table_schema():
    """检查表结构"""
    
    client = connect_to_ap_research()
    
    # 检查表结构
    query = "DESCRIBE priority_quality_fundamental_data_complete_deduped"
    
    result = client.execute(query)
    
    print("📊 表结构:")
    print("=" * 60)
    for row in result:
        print(f"   {row[0]:<35} {row[1]}")
    
    print()
    
    # 检查几行示例数据
    sample_query = """
    SELECT *
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = 'NVDA'
      AND financial_period_absolute REGEXP '^FY[0-9]{4}$'
    ORDER BY financial_period_absolute DESC
    LIMIT 5
    """
    
    result = client.execute(sample_query)
    
    print("📋 示例数据 (NVDA):")
    print("=" * 100)
    
    if result:
        # 获取列名
        columns_query = "SELECT name FROM system.columns WHERE table = 'priority_quality_fundamental_data_complete_deduped' AND database = 'ap_research' ORDER BY position"
        columns_result = client.execute(columns_query)
        column_names = [row[0] for row in columns_result]
        
        print("   列名:", " | ".join(column_names[:8]))  # 显示前8列
        print("   " + "-" * 80)
        
        for i, row in enumerate(result[:3]):  # 只显示前3行
            row_str = " | ".join([str(cell)[:15] for cell in row[:8]])  # 前8列，每个单元格最多15字符
            print(f"   {i+1}: {row_str}")
    
    else:
        print("   无数据")

if __name__ == "__main__":
    check_table_schema()

