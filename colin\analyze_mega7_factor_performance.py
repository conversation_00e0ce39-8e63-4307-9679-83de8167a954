import pandas as pd
import numpy as np
from datetime import datetime
import glob
import os

def analyze_mega7_factor_performance():
    """分析Mega7股票的因子表现情况"""
    
    # 读取最新的因子计算结果
    csv_files = glob.glob('mega7_complete_field_mapping_factors_*.csv')
    if not csv_files:
        print("❌ 未找到因子计算结果文件")
        exit(1)

    latest_file = max(csv_files, key=os.path.getctime)
    print(f"📁 读取文件: {latest_file}")

    # 读取数据
    df = pd.read_csv(latest_file)

    # 删除第一列（Unnamed: 0）
    if 'Unnamed: 0' in df.columns:
        df = df.drop('Unnamed: 0', axis=1)

    # 设置要分析的因子列表（排除字段标识列）
    factor_columns = [col for col in df.columns if not col.endswith('_field_flag') and col not in ['stock_symbol', 'calculation_date', 'fiscal_year', 'period_end_date', 'effective_date']]
    
    print("🚀 Mega7股票24因子表现分析报告")
    print("=" * 60)
    print(f"📅 计算日期: {df['calculation_date'].iloc[0]}")
    print(f"📊 分析股票数: {len(df)}")
    print(f"🔢 总因子数: {len(df.columns) - 6}")  # 减去基础字段
    
    # 基础字段
    base_columns = ['stock_symbol', 'calculation_date', 'fiscal_year', 'period_end_date', 'effective_date']
    
    # 因子字段（排除基础字段和field_flag字段）
    # factor_columns = [col for col in df.columns if col not in base_columns and not col.endswith('_field_flag')]
    
    print(f"\n📈 核心因子列表 ({len(factor_columns)}个):")
    for i, factor in enumerate(factor_columns, 1):
        print(f"  {i:2d}. {factor}")
    
    print("\n" + "=" * 60)
    print("📊 各股票因子表现排名分析")
    print("=" * 60)
    
    # 分析每个因子的表现
    factor_analysis = {}
    
    for factor in factor_columns:
        values = pd.to_numeric(df[factor], errors='coerce')
        valid_values = values.dropna()
        
        if len(valid_values) > 0:
            # 计算统计信息
            factor_analysis[factor] = {
                'mean': valid_values.mean(),
                'std': valid_values.std(),
                'min': valid_values.min(),
                'max': valid_values.max(),
                'median': valid_values.median(),
                'coverage': len(valid_values) / len(df) * 100
            }
            
            # 找出表现最好和最差的股票
            best_stock = df.loc[values.idxmax(), 'stock_symbol'] if not values.isna().all() else 'N/A'
            worst_stock = df.loc[values.idxmin(), 'stock_symbol'] if not values.isna().all() else 'N/A'
            
            print(f"\n🔍 {factor}:")
            print(f"   平均值: {factor_analysis[factor]['mean']:.4f}")
            print(f"   中位数: {factor_analysis[factor]['median']:.4f}")
            print(f"   标准差: {factor_analysis[factor]['std']:.4f}")
            print(f"   范围: [{factor_analysis[factor]['min']:.4f}, {factor_analysis[factor]['max']:.4f}]")
            print(f"   覆盖率: {factor_analysis[factor]['coverage']:.1f}%")
            print(f"   最佳: {best_stock} ({factor_analysis[factor]['max']:.4f})")
            print(f"   最差: {worst_stock} ({factor_analysis[factor]['min']:.4f})")
    
    print("\n" + "=" * 60)
    print("🏆 各股票综合表现排名")
    print("=" * 60)
    
    # 计算每只股票的综合得分（标准化后的因子值）
    stock_scores = {}
    
    for _, row in df.iterrows():
        stock = row['stock_symbol']
        scores = []
        
        for factor in factor_columns:
            value = pd.to_numeric(row[factor], errors='coerce')
            if not pd.isna(value) and factor in factor_analysis:
                # 标准化得分 (z-score)
                if factor_analysis[factor]['std'] > 0:  # 避免除零错误
                    z_score = (value - factor_analysis[factor]['mean']) / factor_analysis[factor]['std']
                    scores.append(z_score)
        
        if scores:
            stock_scores[stock] = np.mean(scores)
    
    # 按综合得分排序
    sorted_stocks = sorted(stock_scores.items(), key=lambda x: x[1], reverse=True)
    
    print("🏆 综合表现排名:")
    for i, (stock, score) in enumerate(sorted_stocks, 1):
        print(f"  {i}. {stock}: {score:.4f}")
    
    print("\n" + "=" * 60)
    print("📊 因子覆盖率统计")
    print("=" * 60)
    
    # 统计每个因子的覆盖率
    coverage_stats = []
    for factor in factor_columns:
        values = pd.to_numeric(df[factor], errors='coerce')
        coverage = (1 - values.isna().sum() / len(df)) * 100
        coverage_stats.append({
            'factor': factor,
            'coverage': coverage,
            'missing_stocks': df[values.isna()]['stock_symbol'].tolist()
        })
    
    # 按覆盖率排序
    coverage_stats.sort(key=lambda x: x['coverage'], reverse=True)
    
    print("因子覆盖率排名:")
    for i, stat in enumerate(coverage_stats, 1):
        missing = ', '.join(stat['missing_stocks']) if stat['missing_stocks'] else '无'
        print(f"  {i:2d}. {stat['factor']:<25} {stat['coverage']:5.1f}%  缺失: {missing}")
    
    print("\n" + "=" * 60)
    print("🔍 特殊发现")
    print("=" * 60)
    
    # 分析异常值
    print("异常值分析:")
    for factor in factor_columns:
        values = pd.to_numeric(df[factor], errors='coerce')
        valid_values = values.dropna()
        
        if len(valid_values) > 0:
            Q1 = valid_values.quantile(0.25)
            Q3 = valid_values.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers = valid_values[(valid_values < lower_bound) | (valid_values > upper_bound)]
            if len(outliers) > 0:
                outlier_stocks = df[values.isin(outliers)]['stock_symbol'].tolist()
                print(f"  {factor}: {outlier_stocks} (异常值: {outliers.tolist()})")
    
    # 保存分析结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f'mega7_factor_analysis_{timestamp}.csv'
    
    # 创建详细的分析结果DataFrame
    analysis_df = pd.DataFrame()
    analysis_df['stock_symbol'] = df['stock_symbol']
    
    for factor in factor_columns:
        values = pd.to_numeric(df[factor], errors='coerce')
        analysis_df[factor] = values
    
    # 添加综合得分
    analysis_df['comprehensive_score'] = [stock_scores.get(stock, np.nan) for stock in df['stock_symbol']]
    
    analysis_df.to_csv(output_file, index=False)
    print(f"\n💾 详细分析结果已保存到: {output_file}")
    
    return df, factor_analysis, stock_scores

if __name__ == "__main__":
    df, factor_analysis, stock_scores = analyze_mega7_factor_performance()
