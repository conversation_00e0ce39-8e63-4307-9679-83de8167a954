#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单查看ap_research数据库表结构脚本
使用基础查询避免权限问题
"""

from clickhouse_driver import Client
import warnings
warnings.filterwarnings('ignore')

def connect_to_ap_research():
    """连接到ap_research数据库"""
    try:
        client = Client(
            host='************',
            port=9000,
            user='default',
            password='5ur2pK8WZQdy2',
            database='ap_research'
        )
        print("✅ 成功连接到ap_research数据库")
        return client
    except Exception as e:
        print(f"❌ 连接ap_research数据库失败: {e}")
        return None

def try_simple_queries(client):
    """尝试简单的查询来了解数据库结构"""
    print("\n🔍 尝试简单查询了解数据库结构...")
    
    # 尝试查询一些可能的表
    possible_tables = [
        'priority_quality_stock_hfq',
        'hfq_intrday_1_day',
        'priority_quality_fundamental_data_complete_deduped',
        'stock_hfq',
        'hfq_data',
        'daily_price',
        'stock_price'
    ]
    
    for table_name in possible_tables:
        print(f"\n📊 尝试查询表: {table_name}")
        
        try:
            # 简单计数查询
            count_query = f"SELECT COUNT(*) FROM {table_name}"
            result = client.execute(count_query)
            if result:
                count = result[0][0]
                print(f"  ✅ 表 {table_name} 存在，包含 {count:,} 行数据")
                
                # 尝试获取表结构
                try:
                    desc_query = f"DESCRIBE {table_name}"
                    desc_result = client.execute(desc_query)
                    if desc_result:
                        print(f"  📋 表结构 ({len(desc_result)} 列):")
                        for i, col_info in enumerate(desc_result[:3]):  # 只显示前3列
                            col_name = col_info[0] if col_info else "未知"
                            col_type = col_info[1] if len(col_info) > 1 else "未知"
                            print(f"    {col_name:<20} {col_type}")
                        if len(desc_result) > 3:
                            print(f"    ... 还有 {len(desc_result) - 3} 列")
                except Exception as e:
                    print(f"  ⚠️ 无法获取表结构: {e}")
                
                # 尝试查询AAPL数据
                try:
                    # 尝试不同的字段名
                    aapl_queries = [
                        f"SELECT COUNT(*) FROM {table_name} WHERE stock_symbol = 'AAPL'",
                        f"SELECT COUNT(*) FROM {table_name} WHERE ric LIKE '%AAPL%'",
                        f"SELECT COUNT(*) FROM {table_name} WHERE symbol = 'AAPL'"
                    ]
                    
                    for query in aapl_queries:
                        try:
                            aapl_result = client.execute(query)
                            if aapl_result and aapl_result[0][0] > 0:
                                print(f"  🍎 找到AAPL数据: {aapl_result[0][0]:,} 行")
                                break
                        except:
                            continue
                    else:
                        print(f"  ⚠️ 未找到AAPL数据")
                        
                except Exception as e:
                    print(f"  ⚠️ 查询AAPL数据失败: {e}")
                    
            else:
                print(f"  ⚠️ 表 {table_name} 查询无结果")
                
        except Exception as e:
            print(f"  ❌ 表 {table_name} 不存在或无法访问: {e}")

def try_show_tables(client):
    """尝试使用SHOW TABLES命令"""
    print("\n🔍 尝试使用SHOW TABLES命令...")
    
    try:
        show_query = "SHOW TABLES"
        result = client.execute(show_query)
        if result:
            print(f"✅ 找到 {len(result)} 个表:")
            for table in result:
                print(f"  - {table[0]}")
        else:
            print("⚠️ SHOW TABLES命令无结果")
    except Exception as e:
        print(f"❌ SHOW TABLES命令失败: {e}")

def try_system_tables(client):
    """尝试查询系统表"""
    print("\n🔍 尝试查询系统表...")
    
    try:
        # 尝试查询系统表
        system_query = "SELECT name FROM system.tables WHERE database = 'ap_research' LIMIT 10"
        result = client.execute(system_query)
        if result:
            print(f"✅ 从系统表找到 {len(result)} 个表:")
            for table in result:
                print(f"  - {table[0]}")
        else:
            print("⚠️ 系统表查询无结果")
    except Exception as e:
        print(f"❌ 系统表查询失败: {e}")

def main():
    """主函数"""
    print("🔍 简单查看ap_research数据库表结构")
    print("=" * 60)
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        return
    
    # 尝试不同的方法
    try_show_tables(client)
    try_system_tables(client)
    try_simple_queries(client)
    
    print("\n✅ 数据库检查完成!")

if __name__ == "__main__":
    main()
