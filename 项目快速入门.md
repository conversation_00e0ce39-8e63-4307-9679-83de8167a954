# 股票财务数据分析项目 - 快速入门

## 🎯 项目核心成果

### 数据规模
- **基础股票池**: 979只高质量股票（基于2020-2025年交易表现筛选）
- **完整数据股票**: 817只具有完整三大财务报表的股票
- **数据时间范围**: 2020年至今
- **匹配规则**: 按公告时间+1天生效，匹配每个交易日可用的最新财务期间

### 核心文件（按重要性排序）

#### 🏆 最重要的文件（★★★★★）
```
1. complete_stocks_original_codes.csv (5KB) - 817只完整股票代码列表
2. filtered_stocks_income_statement_matching.csv (33MB) - 利润表匹配数据
3. filtered_stocks_balance_sheet_matching.csv (12MB) - 资产负债表匹配数据  
4. filtered_stocks_cash_flow_matching.csv (42MB) - 现金流量表匹配数据
5. stock_performance_2020_2025_cumulative.csv (140KB) - 979只基础股票池
```

#### 🔧 核心脚本
```
1. match_filtered_stocks_with_financial_periods_final_correct.py - 最终匹配脚本
2. save_complete_stocks.py - 完整股票保存脚本
```

## 📊 数据结构说明

### 匹配数据文件结构
每个匹配文件包含以下核心字段：
- `ric`: 股票代码
- `base_code`: 基础代码（去掉后缀）
- `trade_date`: 交易日期
- `close`: 收盘价
- `turnover`: 换手率
- `Q/H/FY`: 财务期间类型标识
- `announcement_date`: 公告日期
- `effective_date`: 生效日期

### 数据匹配逻辑
- **匹配键**: 每个交易日匹配该日可用的最新财务期间
- **时间滞后**: 财务数据从公告日次日开始生效
- **优先级**: 季报(Q) > 半年报(H) > 年报(FY)

## 🚀 快速使用指南

### 1. 标准股票池
```python
# 读取817只完整股票
complete_stocks = pd.read_csv('complete_stocks_original_codes.csv')
stock_list = complete_stocks['股票代码'].tolist()
```

### 2. 财务数据分析
```python
# 读取三大报表匹配数据
income_data = pd.read_csv('filtered_stocks_income_statement_matching.csv')
balance_data = pd.read_csv('filtered_stocks_balance_sheet_matching.csv')
cashflow_data = pd.read_csv('filtered_stocks_cash_flow_matching.csv')
```

### 3. 重新生成数据
```bash
# 重新生成匹配数据
python match_filtered_stocks_with_financial_periods_final_correct.py

# 更新完整股票列表
python save_complete_stocks.py
```

## 📋 数据库信息

### ClickHouse连接
```python
from clickhouse_driver import Client

client = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='lseg'
)
```

### 核心表结构
- `hfq_intrday_1_day`: 行情数据表
- `income_statement`: 利润表（使用financial_period_absolute字段）
- `balance_sheet`: 资产负债表（使用fperiod字段）
- `cash_flow`: 现金流量表（使用financial_period_absolute字段）
- `statement_header`: 报表头信息（包含公告日期）

## 🔍 数据质量情况

### 覆盖率统计
- **利润表**: 979只股票中有数据
- **资产负债表**: 979只股票中有数据  
- **现金流量表**: 979只股票中有数据
- **完整三大表**: 817只股票（83.5%覆盖率）

### 缺失原因
- 162只股票缺失部分报表数据
- 主要原因：ETF/基金类产品、新上市股票、数据质量问题

## 📝 重要注意事项

1. **数据时效性**: 财务数据基于公告时间，使用时需注意时效性
2. **代码格式**: 不同数据源使用不同股票代码格式，已通过映射文件统一处理
3. **文件大小**: 匹配数据文件较大（87MB总大小），建议分批处理
4. **数据完整性**: 817只股票具有完整数据，其他股票可能存在数据缺失

## 🎯 项目价值

- ✅ 建立了完整的财务数据匹配体系
- ✅ 提供了标准化的数据输出格式
- ✅ 完成了数据去重和质量控制
- ✅ 为量化投资研究提供了坚实的数据基础

---

**如需详细文档，请参考：**
- `项目总结文档.md` - 完整的项目说明
- `文件索引表.csv` - 所有文件的详细索引 