from clickhouse_driver import Client
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')
import time

# 连接ClickHouse数据库
client = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='lseg'
)

print("=== Colin <PERSON>成长价值优势投资法则因子实现（修正最终版）===")
print("基于三大财务报表构建4大类13个核心因子")

def get_income_statement_data():
    """获取利润表数据"""
    print("\n1. 获取利润表数据...")
    
    income_query = """
    SELECT 
        period_end_date,
        instrument,
        financial_period_absolute,
        item_name,
        income_statement as value,
        currency
    FROM lseg.income_statement
    WHERE item_name IN (
        'Revenue from Business Activities - Total',
        'Revenue from Goods & Services',
        'Cost of Revenues - Total',
        'Cost of Operating Revenue',
        'Research & Development Expense',
        'Research & Development Expense - Supplemental',
        'Operating Profit before Non-Recurring Income/Expense',
        'Income before Taxes',
        'Earnings before Interest & Taxes (EBIT)',
        'Earnings before Interest, Taxes, Depreciation & Amortization (EBITDA)',
        'Net Income - Basic - including Extraordinary Items Applicable to Common - Total',
        'Income Taxes',
        'Gross Profit - Industrials/Property - Total'
    )
    AND financial_period_absolute LIKE '%Q%'
    AND substring(financial_period_absolute, 3, 4) >= '2020'
    ORDER BY instrument, period_end_date
    """
    
    income_data = client.execute(income_query)
    df_income = pd.DataFrame(income_data, columns=['period_end_date', 'instrument', 'financial_period_absolute', 'item_name', 'value', 'currency'])
    
    # 数据透视
    df_income_pivot = df_income.pivot_table(
        index=['period_end_date', 'instrument', 'financial_period_absolute', 'currency'],
        columns='item_name',
        values='value',
        aggfunc='first'
    ).reset_index()
    
    print(f"  利润表数据记录数: {len(df_income_pivot):,}")
    return df_income_pivot

def get_balance_sheet_data():
    """获取资产负债表数据"""
    print("\n2. 获取资产负债表数据...")
    
    balance_query = """
    SELECT 
        period_end_date,
        instrument,
        financial_period_absolute,
        item_name,
        balance_sheet as value,
        currency
    FROM lseg.balance_sheet_history
    WHERE item_name IN (
        'Total Assets',
        'Shareholders'' Equity - Attributable to Parent Shareholders - Total',
        'Total Shareholders'' Equity - including Minority Interest & Hybrid Debt',
        'Common Equity - Total',
        'Total Liabilities',
        'Debt - Total',
        'Net Debt',
        'Cash & Cash Equivalents - Total',
        'Cash & Short Term Investments - Total',
        'Intangible Assets - Total - Net',
        'Intangible Assets - excluding Goodwill - Net - Total',
        'Common Shares - Outstanding - Total',
        'Total Shares Outstanding'
    )
    AND financial_period_absolute LIKE '%Q%'
    AND substring(financial_period_absolute, 3, 4) >= '2020'
    ORDER BY instrument, period_end_date
    """
    
    balance_data = client.execute(balance_query)
    df_balance = pd.DataFrame(balance_data, columns=['period_end_date', 'instrument', 'financial_period_absolute', 'item_name', 'value', 'currency'])
    
    # 数据透视
    df_balance_pivot = df_balance.pivot_table(
        index=['period_end_date', 'instrument', 'financial_period_absolute', 'currency'],
        columns='item_name',
        values='value',
        aggfunc='first'
    ).reset_index()
    
    print(f"  资产负债表数据记录数: {len(df_balance_pivot):,}")
    return df_balance_pivot

def get_cash_flow_data():
    """获取现金流量表数据"""
    print("\n3. 获取现金流量表数据...")
    
    cash_flow_query = """
    SELECT 
        period_end_date,
        instrument,
        financial_period_absolute,
        item_name,
        cash_flow_statement as value,
        currency
    FROM lseg.cash_flow
    WHERE item_name IN (
        'Net Cash Flow from Operating Activities',
        'Income Taxes - Paid/(Reimbursed) - Cash Flow',
        'Income Taxes - Paid/(Reimbursed) - Cash Flow - Supplemental',
        'Capital Expenditures - Total',
        'Free Cash Flow',
        'Cash Flow from Operations - Total'
    )
    AND financial_period_absolute LIKE '%Q%'
    AND substring(financial_period_absolute, 3, 4) >= '2020'
    ORDER BY instrument, period_end_date
    """
    
    cash_flow_data = client.execute(cash_flow_query)
    df_cash_flow = pd.DataFrame(cash_flow_data, columns=['period_end_date', 'instrument', 'financial_period_absolute', 'item_name', 'value', 'currency'])
    
    # 数据透视
    df_cash_flow_pivot = df_cash_flow.pivot_table(
        index=['period_end_date', 'instrument', 'financial_period_absolute', 'currency'],
        columns='item_name',
        values='value',
        aggfunc='first'
    ).reset_index()
    
    print(f"  现金流量表数据记录数: {len(df_cash_flow_pivot):,}")
    return df_cash_flow_pivot

def get_market_data():
    """获取市场数据"""
    print("\n4. 获取市场数据...")
    
    market_query = """
    SELECT 
        date,
        ric,
        close_price
    FROM lseg.hfq_intraday
    WHERE date >= '2020-01-01'
    AND close_price > 0
    ORDER BY ric, date
    """
    
    market_data = client.execute(market_query)
    df_market = pd.DataFrame(market_data, columns=['date', 'ric', 'close_price'])
    
    print(f"  市场数据记录数: {len(df_market):,}")
    return df_market

def merge_all_data():
    """合并所有数据"""
    print("\n5. 合并所有数据...")
    
    # 获取各表数据
    df_income = get_income_statement_data()
    df_balance = get_balance_sheet_data()
    df_cash_flow = get_cash_flow_data()
    df_market = get_market_data()
    
    # 合并财务数据
    df_merged = df_income.merge(
        df_balance,
        on=['period_end_date', 'instrument', 'financial_period_absolute', 'currency'],
        how='outer'
    )
    
    df_merged = df_merged.merge(
        df_cash_flow,
        on=['period_end_date', 'instrument', 'financial_period_absolute', 'currency'],
        how='outer'
    )
    
    # 合并市场数据（按最近日期）
    df_merged['base_code'] = df_merged['instrument'].apply(lambda x: x.split('.')[0] if '.' in x else x)
    df_market['base_code'] = df_market['ric'].apply(lambda x: x.split('.')[0] if '.' in x else x)
    
    df_merged = df_merged.merge(
        df_market[['base_code', 'close_price']],
        on='base_code',
        how='left'
    )
    
    print(f"  合并后数据记录数: {len(df_merged):,}")
    print(f"  涉及股票数: {df_merged['instrument'].nunique():,}")
    
    return df_merged

def calculate_factors(df):
    """计算所有因子"""
    print("\n6. 计算因子...")
    
    # 数据预处理
    df = df.copy()
    
    # 提取财年信息
    df['fiscal_year'] = df['financial_period_absolute'].str.extract(r'FY(\d{4})').astype(float)
    df['quarter'] = df['financial_period_absolute'].str.extract(r'Q(\d)').astype(float)
    
    # 按股票和财年分组计算年度数据
    annual_data = []
    
    for instrument in df['instrument'].unique():
        stock_data = df[df['instrument'] == instrument].copy()
        
        for year in stock_data['fiscal_year'].unique():
            year_data = stock_data[stock_data['fiscal_year'] == year]
            
            # 计算年度汇总数据
            annual_row = {
                'instrument': instrument,
                'fiscal_year': year,
                'close_price': year_data['close_price'].iloc[0] if not year_data['close_price'].isna().all() else None
            }
            
            # 收入相关（季度数据汇总）
            revenue_cols = ['Revenue from Business Activities - Total', 'Revenue from Goods & Services']
            for col in revenue_cols:
                if col in year_data.columns:
                    values = year_data[col].dropna()
                    if len(values) > 0:
                        annual_row[col] = values.sum()
            
            # 成本相关（季度数据汇总）
            cost_cols = ['Cost of Revenues - Total', 'Cost of Operating Revenue']
            for col in cost_cols:
                if col in year_data.columns:
                    values = year_data[col].dropna()
                    if len(values) > 0:
                        annual_row[col] = values.sum()
            
            # 研发费用（季度数据汇总）
            rd_cols = ['Research & Development Expense', 'Research & Development Expense - Supplemental']
            for col in rd_cols:
                if col in year_data.columns:
                    values = year_data[col].dropna()
                    if len(values) > 0:
                        annual_row[col] = values.sum()
            
            # 其他财务指标（取最新值）
            other_cols = [
                'Operating Profit before Non-Recurring Income/Expense',
                'Income before Taxes',
                'Earnings before Interest & Taxes (EBIT)',
                'Earnings before Interest, Taxes, Depreciation & Amortization (EBITDA)',
                'Net Income - Basic - including Extraordinary Items Applicable to Common - Total',
                'Income Taxes',
                'Gross Profit - Industrials/Property - Total',
                'Total Assets',
                'Shareholders\' Equity - Attributable to Parent Shareholders - Total',
                'Total Shareholders\' Equity - including Minority Interest & Hybrid Debt',
                'Common Equity - Total',
                'Total Liabilities',
                'Debt - Total',
                'Net Debt',
                'Cash & Cash Equivalents - Total',
                'Cash & Short Term Investments - Total',
                'Intangible Assets - Total - Net',
                'Intangible Assets - excluding Goodwill - Net - Total',
                'Common Shares - Outstanding - Total',
                'Total Shares Outstanding',
                'Net Cash Flow from Operating Activities',
                'Income Taxes - Paid/(Reimbursed) - Cash Flow',
                'Income Taxes - Paid/(Reimbursed) - Cash Flow - Supplemental',
                'Capital Expenditures - Total',
                'Free Cash Flow',
                'Cash Flow from Operations - Total'
            ]
            
            for col in other_cols:
                if col in year_data.columns:
                    values = year_data[col].dropna()
                    if len(values) > 0:
                        annual_row[col] = values.iloc[-1]  # 取最新值
            
            annual_data.append(annual_row)
    
    df_annual = pd.DataFrame(annual_data)
    
    # 计算因子
    factors_results = []
    
    for instrument in df_annual['instrument'].unique():
        stock_data = df_annual[df_annual['instrument'] == instrument].copy()
        stock_data = stock_data.sort_values('fiscal_year')
        
        if len(stock_data) < 2:
            continue
        
        # 获取最新年份数据
        latest_data = stock_data.iloc[-1]
        prev_data = stock_data.iloc[-2] if len(stock_data) >= 2 else None
        
        factor_row = {'instrument': instrument, 'fiscal_year': latest_data['fiscal_year']}
        
        # 1. 技术溢价系数
        try:
            revenue = latest_data.get('Revenue from Business Activities - Total') or latest_data.get('Revenue from Goods & Services')
            cost = latest_data.get('Cost of Revenues - Total') or latest_data.get('Cost of Operating Revenue')
            rd_expense = latest_data.get('Research & Development Expense') or latest_data.get('Research & Development Expense - Supplemental')
            
            if revenue and cost and rd_expense and revenue > 0:
                gross_margin = (revenue - cost) / revenue
                rd_ratio = rd_expense / revenue
                
                if rd_ratio > 0:
                    factor_row['tech_premium'] = gross_margin / rd_ratio
                else:
                    factor_row['tech_premium'] = None
            else:
                factor_row['tech_premium'] = None
        except:
            factor_row['tech_premium'] = None
        
        # 2. 技术断层预警因子
        try:
            if prev_data is not None:
                prev_rd_expense = prev_data.get('Research & Development Expense') or prev_data.get('Research & Development Expense - Supplemental')
                prev_revenue = prev_data.get('Revenue from Business Activities - Total') or prev_data.get('Revenue from Goods & Services')
                
                if rd_expense and revenue and prev_rd_expense and prev_revenue:
                    current_rd_ratio = rd_expense / revenue
                    prev_rd_ratio = prev_rd_expense / prev_revenue
                    
                    if prev_rd_ratio > 0:
                        rd_ratio_decline = (prev_rd_ratio - current_rd_ratio) / prev_rd_ratio
                        factor_row['tech_gap_warning'] = 1 if rd_ratio_decline > 0.2 else 0
                    else:
                        factor_row['tech_gap_warning'] = None
                else:
                    factor_row['tech_gap_warning'] = None
            else:
                factor_row['tech_gap_warning'] = None
        except:
            factor_row['tech_gap_warning'] = None
        
        # 3. 专利密度因子（使用无形资产替代）
        try:
            intangible_assets = latest_data.get('Intangible Assets - Total - Net') or latest_data.get('Intangible Assets - excluding Goodwill - Net - Total')
            total_assets = latest_data.get('Total Assets')
            
            if intangible_assets and total_assets and total_assets > 0:
                factor_row['patent_density'] = intangible_assets / total_assets
            else:
                factor_row['patent_density'] = None
        except:
            factor_row['patent_density'] = None
        
        # 4. 生态现金流占比因子
        try:
            operating_cash_flow = latest_data.get('Net Cash Flow from Operating Activities')
            
            if operating_cash_flow and operating_cash_flow > 0:
                # 简化计算：假设生态现金流为经营现金流的10%
                factor_row['ecosystem_cash_ratio'] = 0.1
            else:
                factor_row['ecosystem_cash_ratio'] = None
        except:
            factor_row['ecosystem_cash_ratio'] = None
        
        # 5. 调整后ROCE
        try:
            operating_profit = latest_data.get('Operating Profit before Non-Recurring Income/Expense')
            total_equity = latest_data.get('Shareholders'' Equity - Attributable to Parent Shareholders - Total') or latest_data.get('Common Equity - Total')
            total_liabilities = latest_data.get('Total Liabilities')
            
            if operating_profit and total_equity and total_liabilities:
                rd_expense = latest_data.get('Research & Development Expense') or latest_data.get('Research & Development Expense - Supplemental') or 0
                capitalized_rd = rd_expense * 0.2  # 假设20%资本化
                
                denominator = total_equity + total_liabilities + 0.3 * capitalized_rd
                if denominator > 0:
                    factor_row['adjusted_roce'] = operating_profit / denominator
                else:
                    factor_row['adjusted_roce'] = None
            else:
                factor_row['adjusted_roce'] = None
        except:
            factor_row['adjusted_roce'] = None
        
        # 6. 自由现金流质量
        try:
            if len(stock_data) >= 3:
                recent_3_years = stock_data.tail(3)
                cash_flow_quality_count = 0
                
                for _, year_data in recent_3_years.iterrows():
                    operating_cash_flow = year_data.get('Net Cash Flow from Operating Activities')
                    shares_outstanding = year_data.get('Common Shares - Outstanding - Total') or year_data.get('Total Shares Outstanding')
                    
                    if operating_cash_flow and shares_outstanding and shares_outstanding > 0:
                        cash_flow_per_share = operating_cash_flow / shares_outstanding
                        if cash_flow_per_share > 0:
                            cash_flow_quality_count += 1
                
                factor_row['fcf_quality'] = cash_flow_quality_count
            else:
                factor_row['fcf_quality'] = None
        except:
            factor_row['fcf_quality'] = None
        
        # 7. 动态安全边际
        try:
            close_price = latest_data.get('close_price')
            revenue = latest_data.get('Revenue from Business Activities - Total') or latest_data.get('Revenue from Goods & Services')
            shares_outstanding = latest_data.get('Common Shares - Outstanding - Total') or latest_data.get('Total Shares Outstanding')
            
            if close_price and revenue and shares_outstanding and shares_outstanding > 0:
                market_cap = close_price * shares_outstanding
                ps_ratio = market_cap / revenue if revenue > 0 else None
                
                if ps_ratio is not None:
                    tech_premium = factor_row.get('tech_premium') or 1.0
                    ecosystem_coef = factor_row.get('ecosystem_cash_ratio') or 0.1
                    
                    # 简化计算
                    factor_row['dynamic_safety_margin'] = ps_ratio / (tech_premium * ecosystem_coef) if (tech_premium * ecosystem_coef) > 0 else None
                else:
                    factor_row['dynamic_safety_margin'] = None
            else:
                factor_row['dynamic_safety_margin'] = None
        except:
            factor_row['dynamic_safety_margin'] = None
        
        # 8. 营收增长率连续性
        try:
            if prev_data is not None:
                current_revenue = latest_data.get('Revenue from Business Activities - Total') or latest_data.get('Revenue from Goods & Services')
                prev_revenue = prev_data.get('Revenue from Business Activities - Total') or prev_data.get('Revenue from Goods & Services')
                
                if current_revenue and prev_revenue and prev_revenue > 0:
                    growth_rate = (current_revenue / prev_revenue - 1)
                    factor_row['revenue_growth_continuity'] = 1 if growth_rate > 0 else 0
                else:
                    factor_row['revenue_growth_continuity'] = None
            else:
                factor_row['revenue_growth_continuity'] = None
        except:
            factor_row['revenue_growth_continuity'] = None
        
        # 9. 有效税率改进
        try:
            income_taxes_paid = latest_data.get('Income Taxes - Paid/(Reimbursed) - Cash Flow') or latest_data.get('Income Taxes - Paid/(Reimbursed) - Cash Flow - Supplemental')
            pretax_income = latest_data.get('Income before Taxes')
            
            if income_taxes_paid and pretax_income and pretax_income > 0:
                effective_tax_rate = income_taxes_paid / pretax_income
                factor_row['effective_tax_rate_improvement'] = 1 if effective_tax_rate < 0.25 else 0
            else:
                factor_row['effective_tax_rate_improvement'] = None
        except:
            factor_row['effective_tax_rate_improvement'] = None
        
        # 10. 财务健康度
        try:
            total_debt = latest_data.get('Debt - Total') or latest_data.get('Net Debt')
            cash_equivalents = latest_data.get('Cash & Cash Equivalents - Total') or latest_data.get('Cash & Short Term Investments - Total')
            ebitda = latest_data.get('Earnings before Interest, Taxes, Depreciation & Amortization (EBITDA)')
            operating_cash_flow = latest_data.get('Net Cash Flow from Operating Activities')
            total_liabilities = latest_data.get('Total Liabilities')
            
            if total_debt and cash_equivalents and ebitda:
                net_debt = total_debt - cash_equivalents
                debt_ebitda_ratio = net_debt / ebitda if ebitda > 0 else None
                
                cash_liability_ratio = operating_cash_flow / total_liabilities if total_liabilities and total_liabilities > 0 else None
                
                factor_row['financial_health'] = 1 if (debt_ebitda_ratio and debt_ebitda_ratio < 3) and (cash_liability_ratio and cash_liability_ratio > 0.15) else 0
            else:
                factor_row['financial_health'] = None
        except:
            factor_row['financial_health'] = None
        
        # 11. 估值泡沫信号
        factor_row['valuation_bubble_signal'] = 0  # 简化处理
        
        # 12. 新增：研发投入强度因子
        try:
            rd_expense = latest_data.get('Research & Development Expense') or latest_data.get('Research & Development Expense - Supplemental')
            revenue = latest_data.get('Revenue from Business Activities - Total') or latest_data.get('Revenue from Goods & Services')
            
            if rd_expense and revenue and revenue > 0:
                factor_row['rd_intensity'] = rd_expense / revenue
            else:
                factor_row['rd_intensity'] = None
        except:
            factor_row['rd_intensity'] = None
        
        # 13. 新增：资本回报率稳定性因子
        try:
            if len(stock_data) >= 3:
                recent_3_years = stock_data.tail(3)
                roce_values = []
                
                for _, year_data in recent_3_years.iterrows():
                    operating_profit = year_data.get('Operating Profit before Non-Recurring Income/Expense')
                    total_equity = year_data.get('Shareholders'' Equity - Attributable to Parent Shareholders - Total') or year_data.get('Common Equity - Total')
                    
                    if operating_profit and total_equity and total_equity > 0:
                        roce = operating_profit / total_equity
                        roce_values.append(roce)
                
                if len(roce_values) >= 2:
                    # 计算ROCE的变异系数
                    roce_std = np.std(roce_values)
                    roce_mean = np.mean(roce_values)
                    factor_row['roce_stability'] = roce_mean / roce_std if roce_std > 0 else None
                else:
                    factor_row['roce_stability'] = None
            else:
                factor_row['roce_stability'] = None
        except:
            factor_row['roce_stability'] = None
        
        factors_results.append(factor_row)
    
    df_factors = pd.DataFrame(factors_results)
    
    print(f"  计算完成的因子记录数: {len(df_factors):,}")
    return df_factors

def analyze_factor_coverage(df_factors):
    """分析因子覆盖率"""
    print("\n7. 分析因子覆盖率...")
    
    total_stocks = len(df_factors)
    coverage_stats = {}
    
    factor_columns = [
        'tech_premium', 'tech_gap_warning', 'patent_density', 'ecosystem_cash_ratio',
        'adjusted_roce', 'fcf_quality', 'dynamic_safety_margin', 'revenue_growth_continuity',
        'effective_tax_rate_improvement', 'financial_health', 'valuation_bubble_signal',
        'rd_intensity', 'roce_stability'
    ]
    
    for factor in factor_columns:
        if factor in df_factors.columns:
            coverage = df_factors[factor].notna().sum()
            coverage_pct = (coverage / total_stocks) * 100
            coverage_stats[factor] = {
                'coverage': coverage,
                'coverage_pct': coverage_pct
            }
            print(f"  {factor}: {coverage} 只股票 ({coverage_pct:.1f}%)")
    
    return coverage_stats

def save_results(df_factors, coverage_stats):
    """保存结果"""
    print("\n8. 保存结果...")
    
    # 保存因子结果
    output_file = 'mclean_factors_fixed_final_results.csv'
    df_factors.to_csv(output_file, index=False, encoding='utf-8')
    print(f"  因子结果已保存到: {output_file}")
    
    # 保存覆盖率统计
    coverage_file = 'mclean_factors_fixed_final_coverage.csv'
    coverage_df = pd.DataFrame(coverage_stats).T.reset_index()
    coverage_df.columns = ['factor', 'coverage', 'coverage_pct']
    coverage_df.to_csv(coverage_file, index=False, encoding='utf-8')
    print(f"  覆盖率统计已保存到: {coverage_file}")
    
    # 生成报告
    report_file = 'mclean_factors_fixed_final_report.txt'
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# Colin McLean成长价值优势投资法则因子分析报告（修正最终版）\n\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write("## 数据概览\n")
        f.write(f"- 总股票数: {len(df_factors):,}\n")
        f.write("- 分析期间: 2020年至今\n")
        f.write("- 数据来源: 三大财务报表（利润表、资产负债表、现金流量表）\n\n")
        
        f.write("## 因子覆盖率统计\n")
        for factor, stats in coverage_stats.items():
            f.write(f"- {factor}: {stats['coverage']} 只股票 ({stats['coverage_pct']:.1f}%)\n")
        
        f.write("\n## 因子说明\n")
        f.write("1. 技术溢价系数: 衡量技术投入的溢价能力\n")
        f.write("2. 技术断层预警因子: 识别技术投入下降风险\n")
        f.write("3. 专利密度因子: 衡量技术壁垒强度（使用无形资产替代）\n")
        f.write("4. 生态现金流占比因子: 评估生态协同价值\n")
        f.write("5. 调整后ROCE: McLean核心改进的资本回报率\n")
        f.write("6. 自由现金流质量: 评估现金流稳定性\n")
        f.write("7. 动态安全边际: 估值安全边际评估\n")
        f.write("8. 营收增长率连续性: 成长性初筛条件\n")
        f.write("9. 有效税率改进: 税务效率评估\n")
        f.write("10. 财务健康度: 财务风险过滤\n")
        f.write("11. 估值泡沫信号: 市场估值风险\n")
        f.write("12. 研发投入强度: 技术投入强度评估\n")
        f.write("13. 资本回报率稳定性: ROCE波动性评估\n")
        
        f.write("\n## 数据项调整说明\n")
        f.write("- 使用 'Operating Profit before Non-Recurring Income/Expense' 替代经营利润\n")
        f.write("- 使用 'Shareholders'' Equity - Attributable to Parent Shareholders - Total' 替代股东权益\n")
        f.write("- 使用 'Income before Taxes' 替代税前利润\n")
        f.write("- 使用 'Debt - Total' 替代总债务\n")
        f.write("- 使用 'Earnings before Interest, Taxes, Depreciation & Amortization (EBITDA)' 替代EBITDA\n")
        f.write("- 新增2个因子：研发投入强度和资本回报率稳定性\n")
        f.write("- 根据实际可用数据项调整了因子计算逻辑\n")
    
    print(f"  分析报告已保存到: {report_file}")

def main():
    """主函数"""
    start_time = time.time()
    
    try:
        # 获取并合并数据
        df_merged = merge_all_data()
        
        # 计算因子
        df_factors = calculate_factors(df_merged)
        
        # 分析覆盖率
        coverage_stats = analyze_factor_coverage(df_factors)
        
        # 保存结果
        save_results(df_factors, coverage_stats)
        
        print(f"\n=== 完成！总耗时: {time.time() - start_time:.2f}秒 ===")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 