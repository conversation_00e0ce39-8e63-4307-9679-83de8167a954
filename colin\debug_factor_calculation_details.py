#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度调试因子计算细节，检查为什么有这么多0值
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from final_complete_24_factors_with_field_mapping import (
    connect_to_ap_research, 
    get_stock_data,
    get_field_with_priority
)
import pandas as pd

def debug_factor_calculation_details():
    """深度调试因子计算细节"""
    print("🔍 深度调试因子计算细节...")
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        print("❌ 无法连接到数据库")
        return
    
    try:
        test_stock = 'AAPL'
        calculation_date = '2024-01-15'
        
        print(f"📊 测试股票: {test_stock}")
        print(f"📅 计算日期: {calculation_date}")
        
        # 获取原始数据
        stock_data = get_stock_data(client, test_stock, calculation_date)
        
        if stock_data.empty:
            print("❌ 无法获取股票数据")
            return
        
        # 透视数据
        pivot_data = stock_data.pivot_table(
            index=['financial_period_absolute', 'period_end_date', 'effective_date'],
            columns='item_name',
            values='value',
            aggfunc='first'
        ).reset_index()
        
        # 获取最新数据
        latest_data = pivot_data.iloc[-1]
        
        print(f"\n📋 最新数据基本信息:")
        print(f"   财年: {latest_data['financial_period_absolute']}")
        print(f"   期间结束日期: {latest_data['period_end_date']}")
        print(f"   生效日期: {latest_data['effective_date']}")
        
        # 定义字段映射
        field_mappings = {
            'revenue': [
                'Revenue from Business Activities - Total',
                'Revenue from Goods & Services'
            ],
            'net_income': [
                'Normalized Net Income - Bottom Line',
                'Net Income - Basic - including Extraordinary Items Applicable to Common - Total',
                'Net Income - Diluted - including Extraordinary Items Applicable to Common - Total',
                'Net Income before Minority Interest',
                'Net Income after Minority Interest'
            ],
            'operating_income': [
                'Operating Profit before Non-Recurring Income/Expense',
                'Other Non-Operating Income/(Expense) - Total'
            ]
        }
        
        # 检查关键财务数据
        print(f"\n🔍 检查关键财务数据:")
        print("-" * 60)
        
        # 1. 检查收入
        revenue, revenue_priority = get_field_with_priority(latest_data, field_mappings['revenue'])
        print(f"收入 (Revenue):")
        print(f"   值: {revenue}")
        print(f"   优先级: {revenue_priority}")
        print(f"   字段: {field_mappings['revenue']}")
        
        # 2. 检查净利润
        net_income, ni_priority = get_field_with_priority(latest_data, field_mappings['net_income'])
        print(f"\n净利润 (Net Income):")
        print(f"   值: {net_income}")
        print(f"   优先级: {ni_priority}")
        print(f"   字段: {field_mappings['net_income']}")
        
        # 3. 检查营业利润
        operating_income, oi_priority = get_field_with_priority(latest_data, field_mappings['operating_income'])
        print(f"\n营业利润 (Operating Income):")
        print(f"   值: {operating_income}")
        print(f"   优先级: {oi_priority}")
        print(f"   字段: {field_mappings['operating_income']}")
        
        # 4. 计算关键比率
        print(f"\n📊 计算关键比率:")
        print("-" * 60)
        
        if revenue and revenue > 0:
            if net_income is not None:
                profit_margin = (net_income / revenue) * 100
                print(f"净利润率: {profit_margin:.2f}%")
            else:
                print(f"净利润率: 无法计算 (净利润为None)")
            
            if operating_income is not None:
                operating_margin = (operating_income / revenue) * 100
                print(f"营业利润率: {operating_margin:.2f}%")
            else:
                print(f"营业利润率: 无法计算 (营业利润为None)")
        else:
            print(f"无法计算比率 (收入为 {revenue})")
        
        # 5. 检查数据中所有可用的字段
        print(f"\n📋 数据中所有可用的字段 (前20个):")
        print("-" * 60)
        available_fields = [col for col in latest_data.index if pd.notna(latest_data[col]) and latest_data[col] != 0]
        for i, field in enumerate(available_fields[:20]):
            print(f"   {i+1:2d}. {field}: {latest_data[field]}")
        
        print(f"\n总共有 {len(available_fields)} 个非零字段")
        
        # 6. 搜索包含关键词的字段
        print(f"\n🔍 搜索包含关键词的字段:")
        print("-" * 60)
        
        keywords = ['Income', 'Revenue', 'Profit', 'Operating']
        for keyword in keywords:
            matching_fields = [field for field in latest_data.index if keyword.lower() in field.lower() and pd.notna(latest_data[field])]
            print(f"\n包含 '{keyword}' 的字段:")
            for field in matching_fields[:5]:  # 只显示前5个
                print(f"   {field}: {latest_data[field]}")
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            client.disconnect()
            print("🔌 数据库连接已关闭")

if __name__ == "__main__":
    debug_factor_calculation_details()
