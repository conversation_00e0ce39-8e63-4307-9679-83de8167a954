from datetime import datetime, date

print("=== 会计科目动态管理逻辑 ===")

def define_accounting_items_rules():
    """
    定义会计科目管理规则
    """
    print("\n1. 会计科目管理规则...")
    
    rules = {
        'estimation_rule': {
            'description': '估算时的科目规则',
            'principle': '完全按照历史上真实报过的会计科目来',
            'logic': [
                '1. 查找该股票历史上所有出现过的会计科目',
                '2. 对每个历史科目逐一进行估算',
                '3. 不添加历史上从未出现的科目',
                '4. 保持历史科目的完整性'
            ],
            'sql_example': '''
            -- 获取股票A历史上所有出现过的会计科目
            SELECT DISTINCT item_name, statement_type
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = 'STOCK_A'
              AND financial_period_absolute LIKE '%Q1'  -- 同季度历史科目
            ORDER BY statement_type, item_name
            '''
        },
        
        'correction_rule': {
            'description': '修正时的科目规则',
            'principle': '按照最新真实报告的会计科目来',
            'logic': [
                '1. 以最新真实报告（通常是年报）的科目为准',
                '2. 只修正最新报告中存在的科目',
                '3. 如果估算科目在最新报告中不存在，标记为历史科目',
                '4. 保持与最新报告科目结构的一致性'
            ],
            'sql_example': '''
            -- 获取最新真实报告的会计科目
            SELECT DISTINCT item_name, statement_type
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = 'STOCK_A'
              AND financial_period_absolute = 'FY2023'  -- 最新年报
            ORDER BY statement_type, item_name
            '''
        },
        
        'mismatch_handling': {
            'description': '科目不匹配的处理',
            'scenarios': [
                '历史科目在最新报告中消失',
                '最新报告中出现新科目',
                '科目名称发生变化',
                '科目归类发生调整'
            ],
            'marking_strategy': '添加标记列说明科目状态'
        }
    }
    
    for rule_name, rule_info in rules.items():
        print(f"\n  📋 {rule_name.upper()}:")
        print(f"     描述: {rule_info['description']}")
        if 'principle' in rule_info:
            print(f"     原则: {rule_info['principle']}")
        if 'logic' in rule_info:
            print(f"     逻辑:")
            for step in rule_info['logic']:
                print(f"       {step}")
        if 'scenarios' in rule_info:
            print(f"     场景:")
            for scenario in rule_info['scenarios']:
                print(f"       {scenario}")
        if 'sql_example' in rule_info:
            print(f"     SQL示例:")
            print(f"     {rule_info['sql_example'].strip()}")
    
    return rules

def define_item_status_tracking():
    """
    定义科目状态跟踪机制
    """
    print(f"\n2. 科目状态跟踪机制...")
    
    status_categories = {
        'active_item': {
            'description': '活跃科目',
            'criteria': '在历史和最新报告中都存在',
            'action': '正常估算和修正',
            'marking': 'active'
        },
        
        'historical_only_item': {
            'description': '历史专有科目',
            'criteria': '只在历史报告中存在，最新报告中消失',
            'action': '只估算，不修正',
            'marking': 'historical_only',
            'reasons': [
                '会计准则变更',
                '业务结构调整',
                '科目合并或重分类',
                '公司业务转型'
            ]
        },
        
        'current_only_item': {
            'description': '当前专有科目',
            'criteria': '只在最新报告中存在，历史中没有',
            'action': '无历史数据，无法估算',
            'marking': 'current_only',
            'handling': '标记为新增科目，需要其他方法处理'
        },
        
        'renamed_item': {
            'description': '重命名科目',
            'criteria': '科目实质相同但名称变更',
            'action': '建立映射关系，正常处理',
            'marking': 'renamed',
            'detection': '通过语义分析和数值相关性检测'
        }
    }
    
    for status_name, status_info in status_categories.items():
        print(f"\n  📊 {status_name.upper()}:")
        print(f"     描述: {status_info['description']}")
        print(f"     判断标准: {status_info['criteria']}")
        print(f"     处理动作: {status_info['action']}")
        print(f"     标记: {status_info['marking']}")
        if 'reasons' in status_info:
            print(f"     可能原因:")
            for reason in status_info['reasons']:
                print(f"       {reason}")
    
    return status_categories

def design_enhanced_table_structure():
    """
    设计增强的表结构以支持科目状态跟踪
    """
    print(f"\n3. 增强的表结构设计...")
    
    enhanced_structure = {
        'quarterly_data_filled': {
            'description': '增强的季报数据补充表',
            'new_fields': [
                'item_status String',  # 科目状态：active, historical_only, current_only, renamed
                'item_source String',  # 科目来源：historical_pattern, current_report, both
                'item_mapping String',  # 科目映射：如果是重命名，记录原科目名
                'correction_applicable String',  # 是否适用修正：yes, no, partial
                'historical_frequency Int32',  # 历史出现频次
                'last_historical_period String',  # 最后出现的历史期间
                'first_current_period String'  # 首次出现的当前期间
            ],
            'usage_examples': {
                'active_item': {
                    'item_status': 'active',
                    'item_source': 'both',
                    'correction_applicable': 'yes',
                    'description': '正常的活跃科目'
                },
                'historical_only': {
                    'item_status': 'historical_only',
                    'item_source': 'historical_pattern',
                    'correction_applicable': 'no',
                    'description': '只在历史中存在的科目'
                },
                'current_only': {
                    'item_status': 'current_only',
                    'item_source': 'current_report',
                    'correction_applicable': 'partial',
                    'description': '只在当前报告中存在的新科目'
                }
            }
        }
    }
    
    for table_name, table_info in enhanced_structure.items():
        print(f"\n  📋 {table_name.upper()}:")
        print(f"     描述: {table_info['description']}")
        print(f"     新增字段:")
        for field in table_info['new_fields']:
            print(f"       {field}")
        
        print(f"     使用示例:")
        for example_name, example_data in table_info['usage_examples'].items():
            print(f"       {example_name.upper()}:")
            for field, value in example_data.items():
                print(f"         {field}: {value}")
    
    return enhanced_structure

def provide_implementation_workflow():
    """
    提供具体的实施工作流程
    """
    print(f"\n4. 具体实施工作流程...")
    
    workflow_steps = [
        {
            'step': '1. 科目清单构建',
            'description': '为每个股票构建历史和当前的科目清单',
            'actions': [
                '提取历史科目清单（按季度类型分组）',
                '提取最新报告科目清单',
                '进行科目匹配分析',
                '识别科目状态类别'
            ],
            'sql_implementation': '''
            -- 构建科目对比表
            WITH historical_items AS (
                SELECT DISTINCT stock_symbol, item_name, statement_type,
                       COUNT(*) as frequency,
                       MAX(financial_period_absolute) as last_period
                FROM priority_quality_fundamental_data_complete_deduped
                WHERE stock_symbol = 'STOCK_A'
                  AND financial_period_absolute LIKE '%Q1'
                GROUP BY stock_symbol, item_name, statement_type
            ),
            current_items AS (
                SELECT DISTINCT stock_symbol, item_name, statement_type
                FROM priority_quality_fundamental_data_complete_deduped
                WHERE stock_symbol = 'STOCK_A'
                  AND financial_period_absolute = 'FY2023'
            )
            SELECT 
                COALESCE(h.stock_symbol, c.stock_symbol) as stock_symbol,
                COALESCE(h.item_name, c.item_name) as item_name,
                COALESCE(h.statement_type, c.statement_type) as statement_type,
                CASE 
                    WHEN h.item_name IS NOT NULL AND c.item_name IS NOT NULL THEN 'active'
                    WHEN h.item_name IS NOT NULL AND c.item_name IS NULL THEN 'historical_only'
                    WHEN h.item_name IS NULL AND c.item_name IS NOT NULL THEN 'current_only'
                END as item_status,
                h.frequency as historical_frequency,
                h.last_period as last_historical_period
            FROM historical_items h
            FULL OUTER JOIN current_items c 
              ON h.stock_symbol = c.stock_symbol 
              AND h.item_name = c.item_name 
              AND h.statement_type = c.statement_type
            '''
        },
        
        {
            'step': '2. 估算阶段处理',
            'description': '基于历史科目进行估算',
            'actions': [
                '只处理有历史数据的科目',
                '对每个历史科目逐一估算',
                '标记科目来源为历史模式',
                '记录估算方法和置信度'
            ],
            'logic_example': '''
            # 估算阶段逻辑
            for stock in stocks:
                historical_items = get_historical_items(stock, target_quarter)
                for item in historical_items:
                    if item.status in ['active', 'historical_only']:
                        estimated_value = calculate_estimation(stock, item, method)
                        record = {
                            'item_name': item.name,
                            'item_status': item.status,
                            'item_source': 'historical_pattern',
                            'filled_simple_estimates': estimated_value,
                            'correction_applicable': 'yes' if item.status == 'active' else 'no'
                        }
                        insert_record(record)
            '''
        },
        
        {
            'step': '3. 修正阶段处理',
            'description': '基于最新报告科目进行修正',
            'actions': [
                '只修正在最新报告中存在的科目',
                '跳过historical_only科目的修正',
                '更新correction_applicable标记',
                '记录修正来源和日期'
            ],
            'logic_example': '''
            # 修正阶段逻辑
            for stock in stocks:
                current_items = get_current_items(stock, latest_report)
                estimated_records = get_estimated_records(stock, target_quarter)
                
                for record in estimated_records:
                    if record.item_name in current_items:
                        # 可以修正
                        corrected_value = calculate_correction(stock, record.item_name, method)
                        update_record(record.id, {
                            'corrected_filled_simple_estimates': corrected_value,
                            'corrected_effective_date': latest_report.announcement_date,
                            'correction_applicable': 'yes'
                        })
                    else:
                        # 不能修正，标记为历史专有
                        update_record(record.id, {
                            'item_status': 'historical_only',
                            'correction_applicable': 'no',
                            'notes': '科目在最新报告中不存在，无法修正'
                        })
            '''
        },
        
        {
            'step': '4. 科目映射处理',
            'description': '处理科目重命名和映射关系',
            'actions': [
                '检测可能的科目重命名',
                '建立科目映射关系',
                '更新映射信息',
                '验证映射的准确性'
            ],
            'detection_algorithm': '''
            # 科目重命名检测算法
            def detect_renamed_items(stock, historical_items, current_items):
                potential_mappings = []
                
                for hist_item in historical_items:
                    if hist_item.status == 'historical_only':
                        for curr_item in current_items:
                            if curr_item.status == 'current_only':
                                # 语义相似度检测
                                semantic_score = calculate_semantic_similarity(
                                    hist_item.name, curr_item.name)
                                
                                # 数值相关性检测
                                correlation_score = calculate_value_correlation(
                                    hist_item.values, curr_item.values)
                                
                                if semantic_score > 0.8 or correlation_score > 0.9:
                                    potential_mappings.append({
                                        'historical_name': hist_item.name,
                                        'current_name': curr_item.name,
                                        'confidence': max(semantic_score, correlation_score)
                                    })
                
                return potential_mappings
            '''
        }
    ]
    
    for step_info in workflow_steps:
        print(f"\n  {step_info['step']}:")
        print(f"    描述: {step_info['description']}")
        print(f"    操作:")
        for action in step_info['actions']:
            print(f"      {action}")
        
        if 'sql_implementation' in step_info:
            print(f"    SQL实现:")
            print(f"    {step_info['sql_implementation'].strip()}")
        
        if 'logic_example' in step_info:
            print(f"    逻辑示例:")
            print(f"    {step_info['logic_example'].strip()}")
        
        if 'detection_algorithm' in step_info:
            print(f"    检测算法:")
            print(f"    {step_info['detection_algorithm'].strip()}")
    
    return workflow_steps

def provide_practical_examples():
    """
    提供实际应用示例
    """
    print(f"\n5. 实际应用示例...")
    
    examples = {
        'scenario_1': {
            'description': 'CCEP FY2023Q1估算和修正',
            'historical_items': [
                'Total Revenue', 'Cost of Sales', 'Gross Profit', 
                'Operating Expenses', 'Depreciation', 'Interest Expense'
            ],
            'current_items': [
                'Total Revenue', 'Cost of Sales', 'Gross Profit',
                'Operating Expenses', 'EBITDA'  # 新增科目
                # 注意：Depreciation, Interest Expense 在最新报告中消失
            ],
            'processing_result': {
                'Total Revenue': {
                    'item_status': 'active',
                    'estimation': '基于历史Q1数据估算',
                    'correction': '基于FY2023年报修正',
                    'correction_applicable': 'yes'
                },
                'Depreciation': {
                    'item_status': 'historical_only',
                    'estimation': '基于历史Q1数据估算',
                    'correction': '无法修正（最新报告中不存在）',
                    'correction_applicable': 'no'
                },
                'EBITDA': {
                    'item_status': 'current_only',
                    'estimation': '无历史数据，无法估算',
                    'correction': '不适用',
                    'correction_applicable': 'no'
                }
            }
        },
        
        'scenario_2': {
            'description': 'MRP科目重命名处理',
            'historical_items': ['Research and Development'],
            'current_items': ['R&D Expenses'],
            'mapping_analysis': {
                'semantic_similarity': 0.85,
                'value_correlation': 0.92,
                'conclusion': '很可能是同一科目的重命名'
            },
            'processing_result': {
                'Research and Development': {
                    'item_status': 'renamed',
                    'item_mapping': 'R&D Expenses',
                    'estimation': '基于历史"Research and Development"数据估算',
                    'correction': '基于最新"R&D Expenses"数据修正',
                    'correction_applicable': 'yes'
                }
            }
        }
    }
    
    for scenario_name, scenario_data in examples.items():
        print(f"\n  📊 {scenario_name.upper()}:")
        print(f"     描述: {scenario_data['description']}")
        print(f"     历史科目: {scenario_data['historical_items']}")
        print(f"     当前科目: {scenario_data['current_items']}")
        
        if 'mapping_analysis' in scenario_data:
            print(f"     映射分析:")
            for key, value in scenario_data['mapping_analysis'].items():
                print(f"       {key}: {value}")
        
        print(f"     处理结果:")
        for item_name, item_result in scenario_data['processing_result'].items():
            print(f"       {item_name}:")
            for field, value in item_result.items():
                print(f"         {field}: {value}")
    
    return examples

def main():
    """
    主函数
    """
    print("开始说明会计科目动态管理逻辑...")
    
    try:
        # 1. 基本规则
        rules = define_accounting_items_rules()
        
        # 2. 状态跟踪
        status_categories = define_item_status_tracking()
        
        # 3. 表结构设计
        enhanced_structure = design_enhanced_table_structure()
        
        # 4. 实施流程
        workflow = provide_implementation_workflow()
        
        # 5. 实际示例
        examples = provide_practical_examples()
        
        print(f"\n=== 总结 ===")
        print(f"✅ 建立了完整的会计科目动态管理机制")
        print(f"✅ 区分估算阶段和修正阶段的科目处理逻辑")
        print(f"✅ 设计了科目状态跟踪和标记系统")
        print(f"✅ 提供了科目重命名检测和映射机制")
        print(f"✅ 增强了表结构以支持科目管理")
        
        print(f"\n=== 关键原则 ===")
        print(f"1. 估算阶段：严格按照历史科目，逐一估算")
        print(f"2. 修正阶段：严格按照最新报告科目，选择性修正")
        print(f"3. 状态标记：清楚标识每个科目的来源和状态")
        print(f"4. 映射处理：智能检测科目重命名和变更")
        print(f"5. 透明度：所有科目变化都有明确的记录和说明")
        
    except Exception as e:
        print(f"❌ 说明过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
