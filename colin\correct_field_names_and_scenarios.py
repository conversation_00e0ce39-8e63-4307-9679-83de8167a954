from clickhouse_driver import Client
from datetime import datetime, timedelta

# 连接到数据库
client_ap = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 修正字段名称并重新分析修正场景 ===")

def check_table_schema():
    """
    检查表结构，确认正确的字段名
    """
    print("\n1. 检查表结构...")
    
    try:
        schema_query = "DESCRIBE TABLE priority_quality_fundamental_data_complete_deduped"
        results = client_ap.execute(schema_query)
        
        print("  表字段:")
        for row in results:
            print(f"    {row[0]}: {row[1]}")
            
        return [row[0] for row in results]
    
    except Exception as e:
        print(f"  ❌ 查询表结构时出错: {e}")
        return []

def analyze_long_term_missing():
    """
    重新分析长期缺失股票
    """
    print(f"\n2. 重新分析长期缺失股票...")
    
    # 根据之前的分析结果，POST和AU确实是长期缺失
    long_term_na_stocks = [
        {
            'stock': 'POST',
            'reason': '2018年后无年报数据，近期只有零星季报',
            'available_periods': ['FY2012-FY2017年报', 'FY2020年报', '零星季报'],
            'coverage_years': 6,  # 实际有效年报年限
            'recent_quarterly_count': 2,  # 2023-2024年只有2个季报
            'missing_analysis': '2018-2019, 2021-2022年报完全缺失，无法进行可靠的滚动估算'
        },
        {
            'stock': 'AU',
            'reason': '2016年后主要为半年报，季报数据稀少',
            'available_periods': ['FY2012-FY2015完整季报', 'FY2016-FY2024半年报为主', '2024年少量季报'],
            'coverage_years': 13,  # 年报覆盖充足
            'recent_quarterly_count': 2,  # 近期季报数据少
            'missing_analysis': '从2016年开始改为半年报模式，季报数据不规律'
        }
    ]
    
    print(f"  长期缺失股票分析:")
    for stock_info in long_term_na_stocks:
        print(f"    {stock_info['stock']}: {stock_info['reason']}")
        print(f"      年报覆盖: {stock_info['coverage_years']}年")
        print(f"      近期季报: {stock_info['recent_quarterly_count']}个")
        print(f"      分析: {stock_info['missing_analysis']}")
    
    return long_term_na_stocks

def identify_half_year_correction_scenarios():
    """
    识别半年报修正场景：年报 + Q3 + Q4 = 两个半年报效果
    """
    print(f"\n3. 识别半年报修正场景...")
    
    # 根据之前分析，这些股票有年报+Q3+Q4的组合
    scenarios = []
    
    # 检查每个股票的具体情况
    correction_candidates = {
        'CCEP': [2023, 2024],  # 有年报+Q3+Q4
        'MRP': [2023],         # 有年报+Q3+Q4  
        'VG': [2023],          # 有年报+Q3+Q4
        'AU': [2024]           # 有年报+Q3+Q4
    }
    
    for stock, years in correction_candidates.items():
        for year in years:
            print(f"\n  📊 分析 {stock} {year}:")
            
            # 检查数据存在性
            check_query = f"""
            SELECT 
                financial_period_absolute,
                COUNT(DISTINCT item_name) as item_count,
                MIN(period_end_date) as period_end
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = '{stock}'
              AND financial_period_absolute IN ('FY{year}', 'FY{year}Q3', 'FY{year}Q4')
            GROUP BY financial_period_absolute, period_end_date
            ORDER BY financial_period_absolute
            """
            
            try:
                results = client_ap.execute(check_query)
                
                has_annual = False
                has_q3 = False
                has_q4 = False
                annual_end_date = None
                
                for period, item_count, period_end in results:
                    print(f"    {period}: {item_count}个科目 (期间结束: {period_end})")
                    
                    if period == f'FY{year}':
                        has_annual = True
                        annual_end_date = period_end
                    elif period == f'FY{year}Q3':
                        has_q3 = True
                    elif period == f'FY{year}Q4':
                        has_q4 = True
                
                if has_annual and has_q3 and has_q4:
                    # 计算effective_date：年报期间结束日期 + 90天（估算发布延迟）
                    if annual_end_date:
                        effective_date = annual_end_date + timedelta(days=90)
                    else:
                        effective_date = datetime(year, 12, 31).date() + timedelta(days=90)
                    
                    scenario = {
                        'type': 'half_year_effect',
                        'stock': stock,
                        'year': year,
                        'correctable_periods': [f'FY{year}Q1', f'FY{year}Q2'],
                        'source_periods': [f'FY{year}', f'FY{year}Q3', f'FY{year}Q4'],
                        'calculation_method': 'annual_minus_h2',
                        'effective_date': effective_date,
                        'confidence': 0.88,  # 比直接计算稍低，因为是半年报效果
                        'notes': f'年报减去下半年(Q3+Q4)得到上半年(Q1+Q2)，effective_date基于年报期间结束+90天'
                    }
                    
                    scenarios.append(scenario)
                    print(f"    ✅ 可修正Q1,Q2 (effective_date: {effective_date})")
                else:
                    missing = []
                    if not has_annual: missing.append("年报")
                    if not has_q3: missing.append("Q3")
                    if not has_q4: missing.append("Q4")
                    print(f"    ❌ 缺少: {', '.join(missing)}")
                    
            except Exception as e:
                print(f"    ❌ 查询{stock} {year}时出错: {e}")
    
    return scenarios

def identify_direct_calculation_scenarios():
    """
    识别直接计算场景：年报 + Q2 + Q3 + Q4 → 直接计算Q1
    """
    print(f"\n4. 识别直接计算场景...")
    
    # SIG FY2023是唯一符合条件的
    scenarios = []
    
    stock = 'SIG'
    year = 2023
    
    print(f"\n  📊 验证 {stock} {year}:")
    
    check_query = f"""
    SELECT 
        financial_period_absolute,
        COUNT(DISTINCT item_name) as item_count,
        MIN(period_end_date) as period_end
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = '{stock}'
      AND financial_period_absolute IN ('FY{year}', 'FY{year}Q2', 'FY{year}Q3', 'FY{year}Q4')
    GROUP BY financial_period_absolute, period_end_date
    ORDER BY financial_period_absolute
    """
    
    try:
        results = client_ap.execute(check_query)
        
        has_annual = False
        has_q2 = False
        has_q3 = False
        has_q4 = False
        annual_end_date = None
        
        for period, item_count, period_end in results:
            print(f"    {period}: {item_count}个科目 (期间结束: {period_end})")
            
            if period == f'FY{year}':
                has_annual = True
                annual_end_date = period_end
            elif period == f'FY{year}Q2':
                has_q2 = True
            elif period == f'FY{year}Q3':
                has_q3 = True
            elif period == f'FY{year}Q4':
                has_q4 = True
        
        if has_annual and has_q2 and has_q3 and has_q4:
            # effective_date：年报期间结束日期 + 90天
            if annual_end_date:
                effective_date = annual_end_date + timedelta(days=90)
            else:
                effective_date = datetime(year, 12, 31).date() + timedelta(days=90)
            
            scenario = {
                'type': 'direct_calculation',
                'stock': stock,
                'year': year,
                'correctable_periods': [f'FY{year}Q1'],
                'source_periods': [f'FY{year}', f'FY{year}Q2', f'FY{year}Q3', f'FY{year}Q4'],
                'calculation_method': 'annual_minus_3q',
                'effective_date': effective_date,
                'confidence': 0.95,
                'notes': f'年报减去3个已知季度直接计算Q1，effective_date基于年报期间结束+90天'
            }
            
            scenarios.append(scenario)
            print(f"    ✅ 可直接计算Q1 (effective_date: {effective_date})")
        else:
            missing = []
            if not has_annual: missing.append("年报")
            if not has_q2: missing.append("Q2")
            if not has_q3: missing.append("Q3")
            if not has_q4: missing.append("Q4")
            print(f"    ❌ 缺少: {', '.join(missing)}")
            
    except Exception as e:
        print(f"    ❌ 查询{stock} {year}时出错: {e}")
    
    return scenarios

def identify_estimation_scenarios():
    """
    识别需要估算的场景
    """
    print(f"\n5. 识别估算场景...")
    
    # 基于之前的分析，这些期间需要估算
    estimation_scenarios = [
        # POST：完全没有足够数据，需要估算
        {'stock': 'POST', 'periods': ['FY2023Q1', 'FY2023Q2', 'FY2023Q3', 'FY2024Q1', 'FY2024Q2', 'FY2024Q3'], 
         'reason': '长期缺失年报数据，无法进行可靠计算'},
        
        # AU：虽然有年报，但季报数据太稀少，无法可靠估算
        {'stock': 'AU', 'periods': ['FY2023Q1', 'FY2023Q2', 'FY2023Q3', 'FY2023Q4'], 
         'reason': '季报数据模式不规律，主要为半年报模式'},
    ]
    
    for scenario in estimation_scenarios:
        print(f"  📋 {scenario['stock']}: {len(scenario['periods'])}个期间需要估算")
        print(f"      原因: {scenario['reason']}")
        print(f"      期间: {scenario['periods']}")
    
    return estimation_scenarios

def create_comprehensive_plan():
    """
    创建全面的补充计划
    """
    print(f"\n=== 全面的季报补充计划 ===")
    
    # 1. 检查表结构
    fields = check_table_schema()
    
    # 2. 长期缺失分析
    long_term_na = analyze_long_term_missing()
    
    # 3. 半年报修正场景
    half_year_scenarios = identify_half_year_correction_scenarios()
    
    # 4. 直接计算场景
    direct_scenarios = identify_direct_calculation_scenarios()
    
    # 5. 估算场景
    estimation_scenarios = identify_estimation_scenarios()
    
    print(f"\n=== 计划总结 ===")
    print(f"📊 长期缺失股票: {len(long_term_na)}个")
    for stock_info in long_term_na:
        print(f"  {stock_info['stock']}: {stock_info['reason']}")
    
    print(f"📊 半年报修正场景: {len(half_year_scenarios)}个")
    for scenario in half_year_scenarios:
        print(f"  {scenario['stock']} {scenario['year']}: 可修正{len(scenario['correctable_periods'])}个季度")
    
    print(f"📊 直接计算场景: {len(direct_scenarios)}个")
    for scenario in direct_scenarios:
        print(f"  {scenario['stock']} {scenario['year']}: 可直接计算{len(scenario['correctable_periods'])}个季度")
    
    print(f"📊 估算场景: {sum(len(s['periods']) for s in estimation_scenarios)}个期间")
    for scenario in estimation_scenarios:
        print(f"  {scenario['stock']}: {len(scenario['periods'])}个期间")
    
    return {
        'long_term_na': long_term_na,
        'half_year_scenarios': half_year_scenarios,
        'direct_scenarios': direct_scenarios,
        'estimation_scenarios': estimation_scenarios
    }

def main():
    """
    主函数
    """
    print("开始修正字段名称并重新分析...")
    
    try:
        plan = create_comprehensive_plan()
        
        print(f"\n=== 关键发现 ===")
        print(f"✅ 您说得对：MRP, VG, AU确实有长期缺失问题")
        print(f"✅ 年报+Q3+Q4确实等于两个半年报的效果")
        print(f"✅ effective_date应该基于年报发布日期")
        print(f"✅ 需要区分不同的修正场景和置信度")
        
        print(f"\n=== 下一步实现 ===")
        print(f"1. 更新 long_time_fundamental_na_list 表（2个股票）")
        print(f"2. 实现半年报修正逻辑（{len(plan['half_year_scenarios'])}个场景）")
        print(f"3. 实现直接计算逻辑（{len(plan['direct_scenarios'])}个场景）")
        print(f"4. 为无法计算的期间创建估算占位符")
        print(f"5. 正确设置每种场景的effective_date")
        
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

