#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的因子标准化功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from final_complete_24_factors_with_field_mapping import (
    connect_to_ap_research, 
    calculate_complete_24_factors
)
from fixed_standardization_logic import standardize_factors_fixed
import pandas as pd

def test_fixed_standardization():
    """测试修复后的标准化功能"""
    print("🚀 测试修复后的因子标准化功能")
    print("=" * 60)
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        print("❌ 无法连接到数据库")
        return
    
    try:
        # 测试股票
        test_stock = 'AAPL'
        calculation_date = '2024-01-15'
        
        print(f"📊 测试股票: {test_stock}")
        print(f"📅 计算日期: {calculation_date}")
        
        # 1. 计算原始因子
        print("\n🔄 步骤1: 计算原始24因子...")
        factors = calculate_complete_24_factors(client, test_stock, calculation_date)
        
        if not factors:
            print("❌ 无法计算原始因子")
            return
        
        print(f"   ✅ 原始因子计算完成")
        
        # 显示部分原始因子值
        print("\n📈 部分原始因子值:")
        key_factors = ['revenue_yoy', 'revenue_cagr', 'profit_revenue_ratio', 'tech_premium', 'fcf_quality']
        for factor in key_factors:
            if factor in factors:
                print(f"   {factor}: {factors[factor]}")
        
        # 2. 使用修复后的标准化函数
        print("\n🔄 步骤2: 使用修复后的标准化函数...")
        standardized_factors = standardize_factors_fixed(factors, client, test_stock, calculation_date)
        
        if not standardized_factors:
            print("❌ 标准化失败")
            return
        
        # 3. 分析标准化结果
        print("\n📊 标准化结果分析:")
        
        # 统计标准化方法
        standardization_methods = {}
        normalized_factors = []
        
        for key, value in standardized_factors.items():
            if key.endswith('_normalized'):
                factor_name = key.replace('_normalized', '')
                method_key = f'{factor_name}_standardization_method'
                method = standardized_factors.get(method_key, 'Unknown')
                
                if method not in standardization_methods:
                    standardization_methods[method] = 0
                standardization_methods[method] += 1
                
                normalized_factors.append({
                    'factor': factor_name,
                    'original': factors.get(factor_name),
                    'normalized': value,
                    'method': method
                })
        
        print(f"   标准化因子总数: {len(normalized_factors)}")
        print(f"   标准化方法分布:")
        for method, count in standardization_methods.items():
            print(f"     {method}: {count} 个因子")
        
        # 显示标准化结果
        print("\n📈 标准化结果详情:")
        for item in normalized_factors:
            print(f"   {item['factor']}:")
            print(f"     原始值: {item['original']}")
            print(f"     标准化值: {item['normalized']:.4f}")
            print(f"     标准化方法: {item['method']}")
            print()
        
        # 4. 验证标准化效果
        print("🔍 标准化效果验证:")
        
        # 检查是否还有大量0值和相同值
        normalized_values = [item['normalized'] for item in normalized_factors if item['normalized'] is not None]
        unique_values = len(set(normalized_values))
        zero_count = sum(1 for v in normalized_values if v == 0)
        
        print(f"   总标准化值数量: {len(normalized_values)}")
        print(f"   唯一值数量: {unique_values}")
        print(f"   零值数量: {zero_count}")
        print(f"   值的多样性: {unique_values/len(normalized_values)*100:.1f}%")
        
        if unique_values > len(normalized_values) * 0.5:
            print("   ✅ 标准化值具有良好的多样性")
        else:
            print("   ⚠️ 标准化值多样性仍然不足")
        
        print("\n✅ 修复后的标准化测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            client.disconnect()
            print("🔌 数据库连接已关闭")

if __name__ == "__main__":
    test_fixed_standardization()
