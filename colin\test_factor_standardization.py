#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试因子标准化功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from final_complete_24_factors_with_field_mapping import (
    connect_to_ap_research, 
    calculate_complete_24_factors,
    standardize_factors
)

def test_single_stock_standardization():
    """测试单只股票的因子标准化"""
    print("🧪 开始测试因子标准化功能...")
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        print("❌ 无法连接到数据库")
        return
    
    try:
        # 测试股票
        test_stock = 'AAPL'
        calculation_date = '2024-01-15'  # 使用历史日期避免未来数据
        
        print(f"📊 测试股票: {test_stock}")
        print(f"📅 计算日期: {calculation_date}")
        
        # 1. 计算原始因子
        print("\n🔄 步骤1: 计算原始24因子...")
        factors = calculate_complete_24_factors(client, test_stock, calculation_date)
        
        if not factors:
            print("❌ 无法计算原始因子")
            return
        
        print(f"   ✅ 原始因子计算完成，共 {len([k for k in factors.keys() if not k.endswith('_field_flag')])} 个因子")
        
        # 显示部分原始因子值
        print("\n📈 部分原始因子值:")
        for i, (key, value) in enumerate(factors.items()):
            if not key.endswith('_field_flag') and value is not None:
                print(f"   {key}: {value}")
                if i >= 5:  # 只显示前6个
                    break
        
        # 2. 进行标准化
        print("\n🔄 步骤2: 进行因子标准化...")
        standardized_factors = standardize_factors(factors, client, test_stock, calculation_date)
        
        if not standardized_factors:
            print("❌ 标准化失败")
            return
        
        # 3. 显示标准化结果
        print("\n📊 标准化结果统计:")
        
        # 统计标准化方法
        standardization_methods = {}
        normalized_factors = []
        
        for key, value in standardized_factors.items():
            if key.endswith('_normalized'):
                factor_name = key.replace('_normalized', '')
                method_key = f'{factor_name}_standardization_method'
                method = standardized_factors.get(method_key, 'Unknown')
                
                if method not in standardization_methods:
                    standardization_methods[method] = 0
                standardization_methods[method] += 1
                
                normalized_factors.append({
                    'factor': factor_name,
                    'original': factors.get(factor_name),
                    'normalized': value,
                    'method': method
                })
        
        print(f"   标准化因子总数: {len(normalized_factors)}")
        print(f"   标准化方法分布:")
        for method, count in standardization_methods.items():
            print(f"     {method}: {count} 个因子")
        
        # 显示部分标准化结果
        print("\n📈 部分标准化结果:")
        for i, item in enumerate(normalized_factors[:10]):  # 显示前10个
            print(f"   {item['factor']}:")
            print(f"     原始值: {item['original']}")
            print(f"     标准化值: {item['normalized']:.4f}")
            print(f"     标准化方法: {item['method']}")
        
        # 4. 验证标准化效果
        print("\n🔍 标准化效果验证:")
        
        # 检查Min-Max标准化是否在[0,1]区间
        min_max_factors = [item for item in normalized_factors if 'Min-Max' in item['method']]
        if min_max_factors:
            min_max_values = [item['normalized'] for item in min_max_factors if item['normalized'] is not None]
            if min_max_values:
                print(f"   Min-Max标准化范围: [{min(min_max_values):.4f}, {max(min_max_values):.4f}]")
                if min(min_max_values) >= 0 and max(min_max_values) <= 1:
                    print("   ✅ Min-Max标准化在[0,1]区间内")
                else:
                    print("   ⚠️ Min-Max标准化超出[0,1]区间")
        
        # 检查Z-Score标准化
        zscore_factors = [item for item in normalized_factors if 'Z-Score' in item['method']]
        if zscore_factors:
            zscore_values = [item['normalized'] for item in zscore_factors if item['normalized'] is not None]
            if zscore_values:
                mean_val = sum(zscore_values) / len(zscore_values)
                print(f"   Z-Score标准化均值: {mean_val:.4f}")
                print(f"   Z-Score标准化范围: [{min(zscore_values):.4f}, {max(zscore_values):.4f}]")
        
        print("\n✅ 因子标准化测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            client.disconnect()
            print("🔌 数据库连接已关闭")

def test_standardization_methods():
    """测试不同的标准化方法"""
    print("\n🧪 测试标准化方法...")
    
    # 测试Min-Max标准化
    print("📊 Min-Max标准化测试:")
    test_values = [10, 25, 50, 75, 100]
    rolling_min = min(test_values)
    rolling_max = max(test_values)
    
    for value in test_values:
        normalized = (value - rolling_min) / (rolling_max - rolling_min)
        print(f"   原始值: {value} → 标准化值: {normalized:.4f}")
    
    # 测试Z-Score标准化
    print("\n📊 Z-Score标准化测试:")
    test_values = [10, 25, 50, 75, 100]
    rolling_mean = sum(test_values) / len(test_values)
    rolling_std = (sum((x - rolling_mean) ** 2 for x in test_values) / len(test_values)) ** 0.5
    
    for value in test_values:
        normalized = (value - rolling_mean) / rolling_std
        print(f"   原始值: {value} → 标准化值: {normalized:.4f}")
    
    # 测试Robust Scaling
    print("\n📊 Robust Scaling测试:")
    test_values = [10, 25, 50, 75, 100]
    rolling_median = sorted(test_values)[len(test_values) // 2]
    rolling_q75 = sorted(test_values)[int(len(test_values) * 0.75)]
    rolling_q25 = sorted(test_values)[int(len(test_values) * 0.25)]
    rolling_iqr = rolling_q75 - rolling_q25
    
    for value in test_values:
        normalized = (value - rolling_median) / rolling_iqr
        print(f"   原始值: {value} → 标准化值: {normalized:.4f}")

if __name__ == "__main__":
    print("🚀 因子标准化功能测试")
    print("=" * 50)
    
    # 测试标准化方法
    test_standardization_methods()
    
    # 测试实际因子标准化
    test_single_stock_standardization()
