from datetime import datetime, date
import itertools

print("=== 所有缺失情况的完整处理矩阵 ===")

def define_data_types():
    """
    定义所有数据类型
    """
    print("\n1. 数据类型定义...")
    
    data_types = {
        'Q1': '第一季度报告',
        'Q2': '第二季度报告', 
        'Q3': '第三季度报告',
        'Q4': '第四季度报告',
        'H1': '上半年报告',
        'H2': '下半年报告',
        'Annual': '年度报告'
    }
    
    logical_relationships = [
        'Annual = Q1 + Q2 + Q3 + Q4',
        'H1 = Q1 + Q2',
        'H2 = Q3 + Q4',
        'Annual = H1 + H2'
    ]
    
    print("  数据类型:")
    for code, desc in data_types.items():
        print(f"    {code}: {desc}")
    
    print("  逻辑关系:")
    for relation in logical_relationships:
        print(f"    {relation}")
    
    return data_types, logical_relationships

def generate_all_meaningful_scenarios():
    """
    生成所有有意义的缺失场景
    """
    print("\n2. 所有有意义的缺失场景...")
    
    # 定义所有可能的组合，但只保留有意义的
    scenarios = []
    
    # 类别1: 完整数据 - 无需处理
    scenarios.append({
        'category': 'COMPLETE_DATA',
        'available': ['Annual', 'Q1', 'Q2', 'Q3', 'Q4'],
        'missing': [],
        'action': 'no_action',
        'table': 'none',
        'description': '数据完整，无需处理'
    })
    
    # 类别2: 直接计算 - 缺失1个季度
    direct_calc_scenarios = [
        (['Annual', 'Q2', 'Q3', 'Q4'], ['Q1']),
        (['Annual', 'Q1', 'Q3', 'Q4'], ['Q2']),
        (['Annual', 'Q1', 'Q2', 'Q4'], ['Q3']),
        (['Annual', 'Q1', 'Q2', 'Q3'], ['Q4'])
    ]
    
    for available, missing in direct_calc_scenarios:
        scenarios.append({
            'category': 'DIRECT_CALCULATION',
            'available': available,
            'missing': missing,
            'action': 'direct_calculate',
            'table': 'quarterly_data_filled',
            'description': f'年报减去3个已知季度直接计算{missing[0]}'
        })
    
    # 类别3: 半年报效果 - 缺失一个半年的季度
    half_year_scenarios = [
        (['Annual', 'Q3', 'Q4'], ['Q1', 'Q2']),
        (['Annual', 'Q1', 'Q2'], ['Q3', 'Q4']),
        (['Annual', 'H1'], ['Q1', 'Q2', 'Q3', 'Q4']),
        (['Annual', 'H2'], ['Q1', 'Q2', 'Q3', 'Q4'])
    ]
    
    for available, missing in half_year_scenarios:
        scenarios.append({
            'category': 'HALF_YEAR_EFFECT',
            'available': available,
            'missing': missing,
            'action': 'half_year_calculate_estimate',
            'table': 'quarterly_data_filled',
            'description': f'年报减去半年得到另一半年总和，估算分配到{missing}'
        })
    
    # 类别4: 约束计算 - 年报+上下半年各一个季度
    constraint_scenarios = [
        (['Annual', 'Q1', 'Q3'], ['Q2', 'Q4']),
        (['Annual', 'Q1', 'Q4'], ['Q2', 'Q3']),
        (['Annual', 'Q2', 'Q3'], ['Q1', 'Q4']),
        (['Annual', 'Q2', 'Q4'], ['Q1', 'Q3'])
    ]
    
    for available, missing in constraint_scenarios:
        scenarios.append({
            'category': 'CONSTRAINT_CALCULATION',
            'available': available,
            'missing': missing,
            'action': 'constraint_calculate',
            'table': 'quarterly_data_filled',
            'description': f'年报+2个已知季度，用历史比例计算{missing}'
        })
    
    # 类别5: 部分估算 - 年报+不规则季度组合
    partial_scenarios = [
        (['Annual', 'Q1'], ['Q2', 'Q3', 'Q4']),
        (['Annual', 'Q2'], ['Q1', 'Q3', 'Q4']),
        (['Annual', 'Q3'], ['Q1', 'Q2', 'Q4']),
        (['Annual', 'Q4'], ['Q1', 'Q2', 'Q3']),
        (['Annual', 'Q1', 'Q4'], ['Q2', 'Q3']),
        (['Annual', 'Q2', 'Q3'], ['Q1', 'Q4'])
    ]
    
    for available, missing in partial_scenarios:
        if len(missing) > 2:  # 只处理缺失3个季度的情况
            scenarios.append({
                'category': 'PARTIAL_ESTIMATION',
                'available': available,
                'missing': missing,
                'action': 'partial_estimate',
                'table': 'quarterly_data_filled',
                'description': f'年报+1个季度，估算其余{len(missing)}个季度'
            })
    
    # 类别6: 滚动估算 - 只有部分季度，无年报
    rolling_scenarios = [
        (['Q1', 'Q2'], ['Q3', 'Q4', 'Annual']),
        (['Q3', 'Q4'], ['Q1', 'Q2', 'Annual']),
        (['Q1', 'Q3'], ['Q2', 'Q4', 'Annual']),
        (['Q2', 'Q4'], ['Q1', 'Q3', 'Annual']),
        (['Q1'], ['Q2', 'Q3', 'Q4', 'Annual']),
        (['Q2'], ['Q1', 'Q3', 'Q4', 'Annual']),
        (['Q3'], ['Q1', 'Q2', 'Q4', 'Annual']),
        (['Q4'], ['Q1', 'Q2', 'Q3', 'Annual'])
    ]
    
    for available, missing in rolling_scenarios:
        scenarios.append({
            'category': 'ROLLING_ESTIMATION',
            'available': available,
            'missing': missing,
            'action': 'rolling_estimate',
            'table': 'quarterly_data_filled',
            'description': f'基于{available}用历史趋势估算{missing}'
        })
    
    # 类别7: 数据不足 - 无法处理
    scenarios.append({
        'category': 'INSUFFICIENT_DATA',
        'available': [],
        'missing': ['Q1', 'Q2', 'Q3', 'Q4', 'Annual'],
        'action': 'mark_as_na',
        'table': 'long_time_fundamental_na_list',
        'description': '完全没有数据，标记为长期缺失'
    })
    
    return scenarios

def define_processing_details():
    """
    定义每种场景的详细处理方式
    """
    print("\n3. 详细处理方式...")
    
    processing_details = {
        'DIRECT_CALCULATION': {
            'data_status': 'corrected_real',
            'fill_method': 'direct_calculation',
            'filled_simple_estimates': None,
            'corrected_filled_simple_estimates': '精确计算值',
            'estimated_effective_date': None,
            'corrected_effective_date': '年报实际发布日期',
            'confidence_score': 0.95,
            'table': 'quarterly_data_filled',
            'calculation_logic': '缺失季度 = 年报 - 其他3个季度',
            'date_source': '年报announcement_date'
        },
        
        'HALF_YEAR_EFFECT': {
            'data_status': 'corrected_estimated',
            'fill_method': 'half_year_calculation',
            'filled_simple_estimates': '基于历史比例的初始分配',
            'corrected_filled_simple_estimates': '年报约束下的修正值',
            'estimated_effective_date': '历史该季度月-日+目标年份',
            'corrected_effective_date': '年报实际发布日期',
            'confidence_score': 0.88,
            'table': 'quarterly_data_filled',
            'calculation_logic': '半年总和 = 年报 - 另一半年，然后按历史比例分配',
            'date_source': '估算：历史模式，修正：年报announcement_date'
        },
        
        'CONSTRAINT_CALCULATION': {
            'data_status': 'calculated_with_constraints',
            'fill_method': 'annual_constraint_calculation',
            'filled_simple_estimates': '基于历史比例的初始估算',
            'corrected_filled_simple_estimates': '年报约束优化后的值',
            'estimated_effective_date': '历史该季度月-日+目标年份',
            'corrected_effective_date': '年报实际发布日期',
            'confidence_score': 0.75,
            'table': 'quarterly_data_filled',
            'calculation_logic': '历史比例关系 + 年报总和约束的联立求解',
            'date_source': '估算：历史模式，修正：年报announcement_date'
        },
        
        'PARTIAL_ESTIMATION': {
            'data_status': 'mixed_estimation',
            'fill_method': 'partial_calculation_estimation',
            'filled_simple_estimates': '部分计算+部分估算的混合值',
            'corrected_filled_simple_estimates': '年报约束下的调整值',
            'estimated_effective_date': '历史该季度月-日+目标年份',
            'corrected_effective_date': '年报实际发布日期',
            'confidence_score': 0.65,
            'table': 'quarterly_data_filled',
            'calculation_logic': '年报约束 + 历史趋势估算的混合方法',
            'date_source': '估算：历史模式，修正：年报announcement_date'
        },
        
        'ROLLING_ESTIMATION': {
            'data_status': 'estimated',
            'fill_method': 'rolling_average',
            'filled_simple_estimates': '滚动平均估算值',
            'corrected_filled_simple_estimates': None,
            'estimated_effective_date': '历史该季度月-日+目标年份',
            'corrected_effective_date': None,
            'confidence_score': 0.70,
            'table': 'quarterly_data_filled',
            'calculation_logic': '基于历史3-4个季度的滚动平均',
            'date_source': '估算：历史模式'
        },
        
        'INSUFFICIENT_DATA': {
            'data_status': 'missing',
            'fill_method': None,
            'filled_simple_estimates': None,
            'corrected_filled_simple_estimates': None,
            'estimated_effective_date': None,
            'corrected_effective_date': None,
            'confidence_score': 0.0,
            'table': 'long_time_fundamental_na_list',
            'calculation_logic': '无法计算，记录缺失原因',
            'date_source': '不适用'
        }
    }
    
    return processing_details

def show_complete_matrix():
    """
    显示完整的处理矩阵
    """
    print("\n4. 完整处理矩阵...")
    
    scenarios = generate_all_meaningful_scenarios()
    processing_details = define_processing_details()
    
    # 按类别分组显示
    categories = {}
    for scenario in scenarios:
        category = scenario['category']
        if category not in categories:
            categories[category] = []
        categories[category].append(scenario)
    
    for category, category_scenarios in categories.items():
        print(f"\n  📊 {category}:")
        
        if category in processing_details:
            details = processing_details[category]
            print(f"     数据状态: {details['data_status']}")
            print(f"     填充方法: {details['fill_method']}")
            print(f"     置信度: {details['confidence_score']}")
            print(f"     目标表: {details['table']}")
            print(f"     计算逻辑: {details['calculation_logic']}")
            print(f"     日期来源: {details['date_source']}")
        
        print(f"     具体场景:")
        for i, scenario in enumerate(category_scenarios, 1):
            print(f"       {i}. 可用: {scenario['available']}")
            print(f"          缺失: {scenario['missing']}")
            print(f"          描述: {scenario['description']}")
    
    return categories, processing_details

def provide_table_structure():
    """
    提供表结构定义
    """
    print("\n5. 表结构定义...")
    
    table_structures = {
        'quarterly_data_filled': {
            'description': '季报数据补充表 - 存储所有补充的季报数据',
            'fields': {
                'core_fields': [
                    'id String',
                    'stock_symbol String',
                    'financial_period_absolute String',
                    'statement_type String',
                    'item_name String',
                    'original_value Nullable(Float64)'
                ],
                'status_fields': [
                    'data_status String',  # corrected_real, corrected_estimated, calculated_with_constraints, mixed_estimation, estimated, missing
                    'fill_method String',  # direct_calculation, half_year_calculation, annual_constraint_calculation, etc.
                    'source_periods String'  # 用于计算的源期间
                ],
                'value_fields': [
                    'filled_simple_estimates Nullable(Float64)',      # 初始估算值
                    'corrected_filled_simple_estimates Nullable(Float64)'  # 修正后的值
                ],
                'date_fields': [
                    'original_effective_date Nullable(Date)',      # 原始数据的生效日期
                    'estimated_effective_date Nullable(Date)',     # 估算数据的生效日期（历史月-日+目标年份）
                    'corrected_effective_date Nullable(Date)'      # 修正数据的生效日期（源数据实际发布日期）
                ],
                'metadata_fields': [
                    'created_at DateTime',
                    'updated_at DateTime',
                    'confidence_score Float64',
                    'notes String'
                ]
            }
        },
        
        'long_time_fundamental_na_list': {
            'description': '长期缺失数据分析表 - 记录无法处理的长期缺失情况',
            'fields': {
                'identification_fields': [
                    'stock_symbol String',
                    'analysis_date Date'
                ],
                'availability_fields': [
                    'total_available_periods Int32',
                    'earliest_period String',
                    'latest_period String',
                    'available_periods Array(String)',
                    'available_periods_dates Array(Date)'
                ],
                'missing_analysis_fields': [
                    'missing_periods Array(String)',
                    'missing_periods_types Array(String)',
                    'missing_periods_reasons Array(String)'
                ],
                'quality_fields': [
                    'data_completeness_score Float64',
                    'historical_coverage_years Int32',
                    'recent_data_availability String'
                ],
                'metadata_fields': [
                    'created_at DateTime',
                    'notes String'
                ]
            }
        }
    }
    
    for table_name, table_info in table_structures.items():
        print(f"\n  📋 {table_name.upper()}:")
        print(f"     描述: {table_info['description']}")
        print(f"     字段分类:")
        
        for field_category, fields in table_info['fields'].items():
            print(f"       {field_category}:")
            for field in fields:
                print(f"         {field}")
    
    return table_structures

def provide_implementation_workflow():
    """
    提供实施工作流程
    """
    print("\n6. 实施工作流程...")
    
    workflow_steps = [
        {
            'step': '1. 数据可用性分析',
            'action': '分析每个股票每个年份的数据可用性',
            'output': '确定每个股票属于哪种缺失场景',
            'sql_example': '''
            SELECT stock_symbol, 
                   SUM(CASE WHEN financial_period_absolute LIKE '%Q1' THEN 1 ELSE 0 END) as has_q1,
                   SUM(CASE WHEN financial_period_absolute LIKE '%Q2' THEN 1 ELSE 0 END) as has_q2,
                   SUM(CASE WHEN financial_period_absolute LIKE '%Q3' THEN 1 ELSE 0 END) as has_q3,
                   SUM(CASE WHEN financial_period_absolute LIKE '%Q4' THEN 1 ELSE 0 END) as has_q4,
                   SUM(CASE WHEN financial_period_absolute LIKE 'FY%' AND NOT financial_period_absolute LIKE '%Q%' THEN 1 ELSE 0 END) as has_annual
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE financial_period_absolute LIKE 'FY2023%'
            GROUP BY stock_symbol
            '''
        },
        
        {
            'step': '2. 场景分类',
            'action': '根据数据可用性将每个股票分类到对应场景',
            'output': '每个股票的处理场景清单',
            'logic': '''
            if has_annual and (has_q1 + has_q2 + has_q3 + has_q4) == 3:
                scenario = "DIRECT_CALCULATION"
            elif has_annual and ((has_q1 and has_q2) or (has_q3 and has_q4)):
                scenario = "HALF_YEAR_EFFECT"
            elif has_annual and (has_q1 + has_q2 + has_q3 + has_q4) == 2:
                scenario = "CONSTRAINT_CALCULATION"
            # ... 其他场景判断
            '''
        },
        
        {
            'step': '3. 历史数据查找',
            'action': '为每个需要估算的股票查找历史发布模式',
            'output': '每个股票每个季度的历史月-日模式',
            'sql_example': '''
            SELECT stock_symbol, 
                   SUBSTRING(financial_period_absolute, -2) as quarter,
                   MONTH(announcement_date) as announce_month,
                   DAY(announcement_date) as announce_day
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE announcement_date IS NOT NULL
              AND financial_period_absolute LIKE '%Q%'
            '''
        },
        
        {
            'step': '4. 按场景处理',
            'action': '根据场景类型执行相应的计算逻辑',
            'output': '每个缺失期间的补充数据',
            'priority_order': [
                'DIRECT_CALCULATION (最高优先级)',
                'HALF_YEAR_EFFECT',
                'CONSTRAINT_CALCULATION', 
                'PARTIAL_ESTIMATION',
                'ROLLING_ESTIMATION',
                'INSUFFICIENT_DATA (标记处理)'
            ]
        },
        
        {
            'step': '5. 数据插入',
            'action': '将计算结果插入到相应的表中',
            'output': '完整的补充数据表',
            'table_mapping': {
                'quarterly_data_filled': '所有可计算的补充数据',
                'long_time_fundamental_na_list': '无法处理的长期缺失情况'
            }
        },
        
        {
            'step': '6. 质量验证',
            'action': '验证补充数据的质量和一致性',
            'output': '数据质量报告',
            'validation_checks': [
                '年报约束验证：Q1+Q2+Q3+Q4 = Annual',
                '半年报约束验证：Q1+Q2 = H1, Q3+Q4 = H2',
                '置信度分布检查',
                '日期逻辑检查'
            ]
        }
    ]
    
    for step_info in workflow_steps:
        print(f"\n  {step_info['step']}:")
        print(f"    操作: {step_info['action']}")
        print(f"    输出: {step_info['output']}")
        
        if 'sql_example' in step_info:
            print(f"    SQL示例:")
            print(f"    {step_info['sql_example'].strip()}")
        
        if 'logic' in step_info:
            print(f"    逻辑:")
            print(f"    {step_info['logic'].strip()}")
        
        if 'priority_order' in step_info:
            print(f"    优先级顺序:")
            for priority in step_info['priority_order']:
                print(f"      {priority}")
        
        if 'table_mapping' in step_info:
            print(f"    表映射:")
            for table, desc in step_info['table_mapping'].items():
                print(f"      {table}: {desc}")
        
        if 'validation_checks' in step_info:
            print(f"    验证检查:")
            for check in step_info['validation_checks']:
                print(f"      {check}")
    
    return workflow_steps

def main():
    """
    主函数
    """
    print("开始梳理所有缺失情况的完整处理策略...")
    
    try:
        # 1. 定义数据类型
        data_types, relationships = define_data_types()
        
        # 2. 生成场景
        scenarios = generate_all_meaningful_scenarios()
        
        # 3. 处理细节
        processing_details = define_processing_details()
        
        # 4. 完整矩阵
        categories, details = show_complete_matrix()
        
        # 5. 表结构
        table_structures = provide_table_structure()
        
        # 6. 实施流程
        workflow = provide_implementation_workflow()
        
        print(f"\n=== 总结 ===")
        print(f"✅ 总计 {len(scenarios)} 种有意义的场景")
        print(f"✅ 6 大处理类别，每种都有明确的字段设置")
        print(f"✅ 2 个目标表：quarterly_data_filled + long_time_fundamental_na_list")
        print(f"✅ 完整的双重effective_date逻辑")
        print(f"✅ 6步实施工作流程")
        
        print(f"\n=== 关键原则 ===")
        print(f"1. 估算effective_date：历史月-日+目标年份")
        print(f"2. 修正effective_date：源数据实际发布日期")
        print(f"3. 按置信度优先级处理：直接计算 > 约束计算 > 估算")
        print(f"4. 所有可计算数据进quarterly_data_filled表")
        print(f"5. 无法处理的进long_time_fundamental_na_list表")
        
    except Exception as e:
        print(f"❌ 梳理过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

