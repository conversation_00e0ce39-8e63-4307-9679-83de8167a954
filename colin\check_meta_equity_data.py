from clickhouse_driver import Client

client = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print('=== 详细检查META的股东权益数据 ===')
print()

# 1. 检查META有哪些年份的数据
years_query = '''
SELECT DISTINCT substring(financial_period_absolute, 3, 4) as year
FROM priority_quality_fundamental_data_complete_deduped
WHERE stock_symbol = 'META'
  AND substring(financial_period_absolute, 1, 2) = 'FY'
ORDER BY year
'''

try:
    result = client.execute(years_query)
    years = [row[0] for row in result]
    print(f'📅 META有数据的年份: {years}')
except Exception as e:
    print(f'❌ 查询年份失败: {e}')

print()

# 2. 尝试不同的股东权益字段
equity_fields = [
    "Shareholders' Equity - Attributable to Parent Shareholders - Total",
    "Total Shareholders' Equity - including Minority Interest & Hybrid Debt",
    "Common Equity - Total",
    "Common Equity Attributable to Parent Shareholders"
]

for field in equity_fields:
    print(f'🔍 检查字段: {field}')
    
    field_query = f'''
    SELECT 
        substring(financial_period_absolute, 3, 4) as year,
        value
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = 'META'
      AND substring(financial_period_absolute, 1, 2) = 'FY'
      AND item_name = '{field}'
      AND value IS NOT NULL
    ORDER BY year DESC
    LIMIT 5
    '''
    
    try:
        result = client.execute(field_query)
        if result:
            print(f'  ✅ 找到数据:')
            for year, value in result:
                print(f'    {year}: {value:,.0f}')
        else:
            print(f'  ❌ 无数据')
    except Exception as e:
        print(f'  ❌ 查询失败: {e}')
    print()

# 3. 检查我们在因子计算中实际使用的字段
print('=== 检查因子计算中使用的具体字段 ===')

# 从因子计算脚本中看到使用的字段名
used_field = "Shareholders' Equity - Attributable to Parent Shareholders - Total"

used_field_query = f'''
SELECT 
    substring(financial_period_absolute, 3, 4) as year,
    value,
    statement_type
FROM priority_quality_fundamental_data_complete_deduped
WHERE stock_symbol = 'META'
  AND substring(financial_period_absolute, 1, 2) = 'FY'
  AND item_name = '{used_field}'
ORDER BY year DESC
'''

try:
    result = client.execute(used_field_query)
    print(f'📊 因子计算使用的字段数据 ({used_field}):')
    if result:
        for year, value, stmt_type in result:
            value_str = f'{value:,.0f}' if value is not None else 'NULL'
            print(f'  {year}: {value_str} ({stmt_type})')
    else:
        print('  ❌ 完全没有数据！')
except Exception as e:
    print(f'❌ 查询失败: {e}')

print()

# 4. 对比检查AAPL的同字段数据（确认字段名是否正确）
print('=== 对比检查AAPL的股东权益数据 ===')

aapl_query = f'''
SELECT 
    substring(financial_period_absolute, 3, 4) as year,
    value
FROM priority_quality_fundamental_data_complete_deduped
WHERE stock_symbol = 'AAPL'
  AND substring(financial_period_absolute, 1, 2) = 'FY'
  AND item_name = '{used_field}'
  AND value IS NOT NULL
ORDER BY year DESC
LIMIT 5
'''

try:
    result = client.execute(aapl_query)
    print(f'📊 AAPL的股东权益数据 (对比):')
    if result:
        for year, value in result:
            print(f'  {year}: {value:,.0f}')
        print('  ✅ AAPL有完整的股东权益数据')
    else:
        print('  ❌ AAPL也没有数据 - 字段名可能有问题')
except Exception as e:
    print(f'❌ 查询AAPL失败: {e}')

client.disconnect()

print()
print('=== 分析结论 ===')
print('如果AAPL有数据而META没有，说明是数据源问题')
print('如果都没有数据，说明是字段名问题')
print('需要进一步检查正确的股东权益字段名')

