#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看ap_research数据库表结构脚本
列出所有表并检查hfq相关表的数据情况
"""

from clickhouse_driver import Client
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

def connect_to_ap_research():
    """连接到ap_research数据库"""
    try:
        client = Client(
            host='************',
            port=9000,
            user='default',
            password='5ur2pK8VZQdy2',
            database='ap_research'
        )
        print("✅ 成功连接到ap_research数据库")
        return client
    except Exception as e:
        print(f"❌ 连接ap_research数据库失败: {e}")
        return None

def list_all_tables(client):
    """列出ap_research数据库中的所有表"""
    print("\n📋 正在获取ap_research数据库中的所有表...")
    
    try:
        # 查询系统表获取所有表名
        query = """
        SELECT name, engine, total_rows, total_bytes
        FROM system.tables 
        WHERE database = 'ap_research'
        ORDER BY name
        """
        
        result = client.execute(query)
        
        if result:
            print(f"✅ 找到 {len(result)} 个表:")
            print("-" * 80)
            print(f"{'表名':<40} {'引擎':<15} {'行数':<15} {'大小(MB)':<10}")
            print("-" * 80)
            
            tables_info = []
            for table_name, engine, rows, bytes_size in result:
                size_mb = bytes_size / (1024 * 1024) if bytes_size else 0
                print(f"{table_name:<40} {engine:<15} {rows:<15} {size_mb:<10.2f}")
                tables_info.append({
                    'name': table_name,
                    'engine': engine,
                    'rows': rows,
                    'size_mb': size_mb
                })
            
            return tables_info
        else:
            print("⚠️ 未找到任何表")
            return []
            
    except Exception as e:
        print(f"❌ 查询表列表失败: {e}")
        return []

def check_hfq_tables(client, tables_info):
    """检查所有hfq相关的表"""
    print("\n🔍 检查hfq相关表...")
    
    hfq_tables = [table for table in tables_info if 'hfq' in table['name'].lower()]
    
    if not hfq_tables:
        print("⚠️ 未找到任何hfq相关的表")
        return
    
    print(f"✅ 找到 {len(hfq_tables)} 个hfq相关表:")
    for table in hfq_tables:
        print(f"  - {table['name']} ({table['engine']}, {table['rows']:,} 行, {table['size_mb']:.2f} MB)")
    
    # 检查每个hfq表的结构和数据
    for table in hfq_tables:
        print(f"\n📊 检查表 {table['name']} 的详细信息...")
        check_table_structure(client, table['name'])

def check_table_structure(client, table_name):
    """检查表的结构"""
    try:
        # 获取表结构
        structure_query = f"DESCRIBE {table_name}"
        structure_result = client.execute(structure_query)
        
        if structure_result:
            print(f"  📋 表结构 ({len(structure_result)} 列):")
            for col_name, col_type, default_type, default_expression, comment, codec_expression, ttl_expression in structure_result:
                print(f"    {col_name:<20} {col_type:<30} {comment or ''}")
        
        # 检查是否有AAPL数据
        aapl_query = f"""
        SELECT COUNT(*) as total_count,
               COUNT(CASE WHEN stock_symbol = 'AAPL' THEN 1 END) as aapl_count,
               COUNT(CASE WHEN ric LIKE '%AAPL%' THEN 1 END) as ric_aapl_count
        FROM {table_name}
        """
        
        aapl_result = client.execute(aapl_query)
        if aapl_result:
            total, aapl_count, ric_aapl_count = aapl_result[0]
            print(f"  📊 数据统计:")
            print(f"    总行数: {total:,}")
            print(f"    AAPL行数 (stock_symbol): {aapl_count:,}")
            print(f"    AAPL行数 (ric): {ric_aapl_count:,}")
            
            if aapl_count > 0 or ric_aapl_count > 0:
                print(f"  ✅ 表 {table_name} 包含AAPL数据!")
                
                # 获取最新的AAPL数据样本
                sample_query = f"""
                SELECT *
                FROM {table_name}
                WHERE stock_symbol = 'AAPL' OR ric LIKE '%AAPL%'
                ORDER BY trade_date DESC
                LIMIT 3
                """
                
                sample_result = client.execute(sample_query)
                if sample_result:
                    print(f"  📈 AAPL数据样本:")
                    for row in sample_result:
                        print(f"    {row}")
        
    except Exception as e:
        print(f"  ❌ 检查表 {table_name} 失败: {e}")

def check_priority_quality_tables(client, tables_info):
    """检查priority_quality相关表"""
    print("\n🔍 检查priority_quality相关表...")
    
    pq_tables = [table for table in tables_info if 'priority_quality' in table['name'].lower()]
    
    if not pq_tables:
        print("⚠️ 未找到任何priority_quality相关的表")
        return
    
    print(f"✅ 找到 {len(pq_tables)} 个priority_quality相关表:")
    for table in pq_tables:
        print(f"  - {table['name']} ({table['engine']}, {table['rows']:,} 行, {table['size_mb']:.2f} MB)")
        
        # 检查表结构
        try:
            structure_query = f"DESCRIBE {table['name']}"
            structure_result = client.execute(structure_query)
            
            if structure_result:
                print(f"    📋 列数: {len(structure_result)}")
                # 显示前几列
                for i, (col_name, col_type, default_type, default_expression, comment, codec_expression, ttl_expression) in enumerate(structure_result[:5]):
                    print(f"      {col_name:<20} {col_type:<30}")
                if len(structure_result) > 5:
                    print(f"      ... 还有 {len(structure_result) - 5} 列")
        except Exception as e:
            print(f"    ❌ 获取表结构失败: {e}")

def main():
    """主函数"""
    print("🔍 查看ap_research数据库表结构")
    print("=" * 60)
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        return
    
    # 列出所有表
    tables_info = list_all_tables(client)
    
    if not tables_info:
        print("❌ 无法获取表信息")
        return
    
    # 检查hfq相关表
    check_hfq_tables(client, tables_info)
    
    # 检查priority_quality相关表
    check_priority_quality_tables(client, tables_info)
    
    print("\n✅ 数据库表结构检查完成!")

if __name__ == "__main__":
    main()
