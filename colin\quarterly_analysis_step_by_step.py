from clickhouse_driver import Client

# 连接ClickHouse数据库
client = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 季报数据覆盖情况分析（分步版）===")

# 1. 基本统计
print("\n1. 基本统计...")
print("  正在查询季报数据总量...")

query_basic = """
SELECT 
    COUNT(*) as quarterly_records,
    COUNT(DISTINCT stock_symbol) as quarterly_stocks
FROM priority_quality_fundamental_data_complete_deduped
WHERE financial_period_absolute REGEXP 'FY[0-9]{4}Q[1-4]'
"""

result_basic = client.execute(query_basic)
records, stocks = result_basic[0]
print(f"  季报数据记录数: {records:,}")
print(f"  有季报数据的股票数: {stocks:,}")

# 2. 简单的年份分布
print("\n2. 年份分布（最近5年）...")
years_to_check = [2024, 2023, 2022, 2021, 2020]

for year in years_to_check:
    print(f"  正在查询{year}年数据...")
    query_year = f"""
    SELECT COUNT(DISTINCT stock_symbol) as stocks
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE financial_period_absolute LIKE 'FY{year}Q%'
    """
    
    result_year = client.execute(query_year)
    year_stocks = result_year[0][0]
    print(f"    {year}年: {year_stocks}只股票有季报数据")

# 3. 季度分布
print("\n3. 季度分布...")
quarters = ['Q1', 'Q2', 'Q3', 'Q4']

for quarter in quarters:
    print(f"  正在查询{quarter}数据...")
    query_quarter = f"""
    SELECT COUNT(DISTINCT stock_symbol) as stocks
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE financial_period_absolute LIKE '%{quarter}'
    """
    
    result_quarter = client.execute(query_quarter)
    quarter_stocks = result_quarter[0][0]
    print(f"    {quarter}: {quarter_stocks}只股票")

# 4. 2023年季报完整性（简化查询）
print("\n4. 2023年季报完整性分析...")
print("  正在查询2023年各季度股票数...")

quarters_2023 = {}
for q in ['Q1', 'Q2', 'Q3', 'Q4']:
    query_2023_q = f"""
    SELECT COUNT(DISTINCT stock_symbol) as stocks
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE financial_period_absolute = 'FY2023{q}'
    """
    
    result_2023_q = client.execute(query_2023_q)
    quarters_2023[q] = result_2023_q[0][0]
    print(f"    FY2023{q}: {quarters_2023[q]}只股票")

# 计算2023年的完整性
print(f"\n  2023年季报完整性:")
print(f"    Q1: {quarters_2023['Q1']}只股票")
print(f"    Q2: {quarters_2023['Q2']}只股票") 
print(f"    Q3: {quarters_2023['Q3']}只股票")
print(f"    Q4: {quarters_2023['Q4']}只股票")

min_quarters = min(quarters_2023.values())
max_quarters = max(quarters_2023.values())
print(f"    最少季度股票数: {min_quarters}")
print(f"    最多季度股票数: {max_quarters}")
print(f"    季度间差异: {max_quarters - min_quarters}只股票")

# 5. 找出缺失季报的具体股票（2023年）
print("\n5. 查找2023年季报不完整的股票...")

# 先找有Q4但没有其他季度的股票
for missing_quarter in ['Q1', 'Q2', 'Q3']:
    print(f"  查找缺失{missing_quarter}的股票...")
    query_missing = f"""
    SELECT stock_symbol
    FROM (
        SELECT DISTINCT stock_symbol
        FROM priority_quality_fundamental_data_complete_deduped
        WHERE financial_period_absolute = 'FY2023Q4'
    ) q4_stocks
    WHERE stock_symbol NOT IN (
        SELECT DISTINCT stock_symbol
        FROM priority_quality_fundamental_data_complete_deduped
        WHERE financial_period_absolute = 'FY2023{missing_quarter}'
    )
    ORDER BY stock_symbol
    LIMIT 10
    """
    
    result_missing = client.execute(query_missing)
    if result_missing:
        missing_stocks = [stock for (stock,) in result_missing]
        print(f"    缺失FY2023{missing_quarter}的股票: {', '.join(missing_stocks)}")
    else:
        print(f"    没有股票缺失FY2023{missing_quarter}")

print("\n=== 分析完成 ===")

