from clickhouse_driver import Client
import pandas as pd
import numpy as np
from datetime import datetime

def connect_to_ap_research():
    """连接到ap_research数据库"""
    client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')
    print("✅ 连接ap_research数据库成功")
    return client

def calculate_robust_cagr(values, years, method='geometric'):
    """健壮的CAGR计算"""
    if len(values) < 2:
        return None
    
    # 寻找最长连续正值区间
    if method == 'geometric':
        positive_runs = []
        current_run = []
        
        for i, val in enumerate(values):
            if val > 0:
                current_run.append((i, val))
            else:
                if len(current_run) >= 2:
                    positive_runs.append(current_run)
                current_run = []
        
        if len(current_run) >= 2:
            positive_runs.append(current_run)
        
        if positive_runs:
            # 选择最长区间
            longest_run = max(positive_runs, key=len)
            if len(longest_run) >= 2:
                first_val = longest_run[0][1]
                last_val = longest_run[-1][1]
                years_span = len(longest_run) - 1
                
                if first_val > 0 and last_val > 0:
                    return ((last_val / first_val) ** (1/years_span) - 1) * 100
    
    # 算术平均增长率作为备选
    growth_rates = []
    for i in range(1, len(values)):
        if values[i-1] != 0:
            growth_rate = (values[i] - values[i-1]) / abs(values[i-1]) * 100
            growth_rates.append(growth_rate)
    
    if growth_rates:
        return np.mean(growth_rates)
    
    return None

def calculate_mega7_improved_factors(stock_symbol, stock_data):
    """为Mega7计算改进版因子"""
    
    factors = {}
    
    # 按年份排序
    stock_data = stock_data.sort_values('fiscal_year')
    latest_data = stock_data.iloc[-1]
    
    # 计算上市年限
    listing_years = len(stock_data)
    
    print(f"   📊 {stock_symbol}: {listing_years}年数据")
    
    # 【1】tech_premium（技术溢价）
    try:
        rd_expense = latest_data.get('Research & Development Expense', 0) or 0
        rd_supplemental = latest_data.get('Research & Development Expense - Supplemental', 0) or 0
        total_rd = rd_expense + rd_supplemental
        revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
        
        if revenue > 0:
            tech_premium_raw = (total_rd / revenue) * 100
            factors['tech_premium'] = min(tech_premium_raw, 50)
        else:
            factors['tech_premium'] = None
    except:
        factors['tech_premium'] = None
    
    # 【2】tech_gap_warning（技术差距警告）
    try:
        if listing_years >= 2 and factors.get('tech_premium') is not None:
            prev_data = stock_data.iloc[-2]
            prev_rd = (prev_data.get('Research & Development Expense', 0) or 0) + \
                     (prev_data.get('Research & Development Expense - Supplemental', 0) or 0)
            prev_revenue = prev_data.get('Revenue from Business Activities - Total', 0) or 0
            
            if prev_revenue > 0:
                prev_tech_premium = (prev_rd / prev_revenue) * 100
                if prev_tech_premium > 0:
                    change_rate = (factors['tech_premium'] - prev_tech_premium) / prev_tech_premium * 100
                    factors['tech_gap_warning'] = np.clip(change_rate, -100, 200)
                else:
                    factors['tech_gap_warning'] = None
            else:
                factors['tech_gap_warning'] = None
        else:
            factors['tech_gap_warning'] = None
    except:
        factors['tech_gap_warning'] = None
    
    # 【5】adjusted_roe（修正为ROE）
    try:
        net_income = latest_data.get('Normalized Net Income - Bottom Line', 0) or 0
        total_equity = (latest_data.get('Shareholders\' Equity - Attributable to Parent Shareholders - Total', 0) or 
                       latest_data.get('Common Equity - Total', 0) or 
                       latest_data.get('Common Equity Attributable to Parent Shareholders', 0) or 0)
        
        if total_equity > 0:
            roe_raw = (net_income / total_equity) * 100
            factors['adjusted_roe'] = np.clip(roe_raw, -50, 50)
        else:
            factors['adjusted_roe'] = None
    except:
        factors['adjusted_roe'] = None
    
    # 【6】fcf_quality（自由现金流质量）
    try:
        fcf = latest_data.get('Free Cash Flow', 0) or 0
        net_income = latest_data.get('Normalized Net Income - Bottom Line', 0) or 0
        
        if net_income != 0:
            fcf_quality_raw = fcf / net_income
            factors['fcf_quality'] = np.clip(fcf_quality_raw, -5, 5)
        else:
            factors['fcf_quality'] = None
    except:
        factors['fcf_quality'] = None
    
    # 【13】revenue_yoy（营收同比增长）
    try:
        if listing_years >= 2:
            current_revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
            prev_revenue = stock_data.iloc[-2].get('Revenue from Business Activities - Total', 0) or 0
            
            if prev_revenue > 0:
                yoy_raw = (current_revenue - prev_revenue) / prev_revenue * 100
                factors['revenue_yoy'] = np.clip(yoy_raw, -100, 200)
            else:
                factors['revenue_yoy'] = None
        else:
            factors['revenue_yoy'] = None
    except:
        factors['revenue_yoy'] = None
    
    # 【14】revenue_cagr（营收CAGR）
    try:
        years_for_cagr = min(5, listing_years)
        if years_for_cagr >= 3:
            revenue_values = []
            for i in range(years_for_cagr):
                revenue = stock_data.iloc[-years_for_cagr+i].get('Revenue from Business Activities - Total', 0) or 0
                revenue_values.append(revenue)
            
            factors['revenue_cagr'] = calculate_robust_cagr(revenue_values, years_for_cagr, 'geometric')
        else:
            factors['revenue_cagr'] = None
    except:
        factors['revenue_cagr'] = None
    
    # 【15】net_income_yoy（净利润同比增长）
    try:
        if listing_years >= 2:
            current_ni = latest_data.get('Normalized Net Income - Bottom Line', 0) or 0
            prev_ni = stock_data.iloc[-2].get('Normalized Net Income - Bottom Line', 0) or 0
            
            if prev_ni != 0:
                factors['net_income_yoy'] = (current_ni - prev_ni) / abs(prev_ni) * 100
            else:
                factors['net_income_yoy'] = None
        else:
            factors['net_income_yoy'] = None
    except:
        factors['net_income_yoy'] = None
    
    # 【16】profit_revenue_ratio（利润营收比）
    try:
        net_income = latest_data.get('Normalized Net Income - Bottom Line', 0) or 0
        revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
        
        if revenue > 0:
            ratio_raw = (net_income / revenue) * 100
            factors['profit_revenue_ratio'] = np.clip(ratio_raw, -50, 50)
        else:
            factors['profit_revenue_ratio'] = None
    except:
        factors['profit_revenue_ratio'] = None
    
    # 【18】fcf_cagr（自由现金流CAGR）
    try:
        years_for_cagr = min(5, listing_years)
        if years_for_cagr >= 3:
            fcf_values = []
            for i in range(years_for_cagr):
                fcf = stock_data.iloc[-years_for_cagr+i].get('Free Cash Flow', 0) or 0
                fcf_values.append(fcf)
            
            factors['fcf_cagr'] = calculate_robust_cagr(fcf_values, years_for_cagr, 'geometric')
        else:
            factors['fcf_cagr'] = None
    except:
        factors['fcf_cagr'] = None
    
    # 【21】roic（资本回报率）
    try:
        net_income = latest_data.get('Normalized Net Income - Bottom Line', 0) or 0
        total_debt = latest_data.get('Debt - Total', 0) or 0
        total_equity = (latest_data.get('Shareholders\' Equity - Attributable to Parent Shareholders - Total', 0) or 
                       latest_data.get('Common Equity - Total', 0) or 
                       latest_data.get('Common Equity Attributable to Parent Shareholders', 0) or 0)
        
        invested_capital = total_debt + total_equity
        
        if invested_capital > 0:
            roic_raw = (net_income / invested_capital) * 100
            factors['roic'] = np.clip(roic_raw, -30, 50)
        else:
            factors['roic'] = None
    except:
        factors['roic'] = None
    
    # 【22】roic_cagr（ROIC CAGR）
    try:
        years_for_cagr = min(5, listing_years)
        if years_for_cagr >= 3:
            roic_values = []
            
            for i in range(years_for_cagr):
                year_data = stock_data.iloc[-years_for_cagr+i]
                net_income = year_data.get('Normalized Net Income - Bottom Line', 0) or 0
                total_debt = year_data.get('Debt - Total', 0) or 0
                total_equity = (year_data.get('Shareholders\' Equity - Attributable to Parent Shareholders - Total', 0) or 
                               year_data.get('Common Equity - Total', 0) or 
                               year_data.get('Common Equity Attributable to Parent Shareholders', 0) or 0)
                
                invested_capital = total_debt + total_equity
                
                if invested_capital > 0 and net_income > 0:  # 只考虑正净利润
                    roic = (net_income / invested_capital) * 100
                    roic_values.append(roic)
            
            factors['roic_cagr'] = calculate_robust_cagr(roic_values, len(roic_values), 'geometric')
        else:
            factors['roic_cagr'] = None
    except:
        factors['roic_cagr'] = None
    
    # 【23】effective_tax_rate（有效税率）
    try:
        tax_expense = latest_data.get('Income Taxes', 0) or 0
        ebit = latest_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
        
        if ebit > 0:
            tax_rate_raw = (tax_expense / ebit) * 100
            factors['effective_tax_rate'] = np.clip(tax_rate_raw, -50, 50)
        else:
            factors['effective_tax_rate'] = None
    except:
        factors['effective_tax_rate'] = None
    
    return factors

def analyze_mega7_improved_factors():
    """分析Mega7在改进版因子系统中的表现"""
    
    print("=== Mega7改进版因子分析 ===")
    print()
    
    # Mega7股票列表
    mega7_stocks = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META']
    
    # 连接数据库
    client = connect_to_ap_research()
    
    # 获取基本面数据
    annual_query = """
    SELECT 
        stock_symbol,
        financial_period_absolute,
        statement_type,
        item_name,
        value,
        period_end_date
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE financial_period_absolute REGEXP '^FY[0-9]{4}$'
      AND stock_symbol IN ('AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META')
    ORDER BY stock_symbol, financial_period_absolute, statement_type, item_name
    """
    
    annual_result = client.execute(annual_query)
    annual_df = pd.DataFrame(annual_result, columns=[
        'stock_symbol', 'financial_period_absolute', 'statement_type', 'item_name', 'value', 'period_end_date'
    ])
    
    print(f"📊 获取到 {len(annual_result)} 条Mega7年报记录")
    
    # 透视数据
    annual_pivot = annual_df.pivot_table(
        index=['stock_symbol', 'financial_period_absolute', 'period_end_date'],
        columns='item_name',
        values='value',
        aggfunc='first'
    ).reset_index()
    
    annual_pivot['fiscal_year'] = annual_pivot['financial_period_absolute'].str.extract(r'FY(\d{4})').astype(int)
    
    # 计算因子
    print(f"\n🔄 开始计算Mega7改进版因子...")
    print("=" * 80)
    
    mega7_factors = []
    
    for stock_symbol in mega7_stocks:
        try:
            stock_data = annual_pivot[annual_pivot['stock_symbol'] == stock_symbol]
            
            if len(stock_data) == 0:
                print(f"   ❌ {stock_symbol}: 无数据")
                continue
            
            factors = calculate_mega7_improved_factors(stock_symbol, stock_data)
            factors['stock_symbol'] = stock_symbol
            mega7_factors.append(factors)
            
        except Exception as e:
            print(f"   ❌ {stock_symbol}: 计算失败 - {str(e)}")
    
    print("=" * 80)
    
    if not mega7_factors:
        print("❌ 未能计算任何Mega7因子")
        return
    
    # 转换为DataFrame
    mega7_df = pd.DataFrame(mega7_factors)
    
    # 重新排列列顺序
    columns_order = ['stock_symbol'] + [col for col in mega7_df.columns if col != 'stock_symbol']
    mega7_df = mega7_df[columns_order]
    
    print(f"✅ 成功计算 {len(mega7_df)} 只Mega7股票的因子")
    
    # 详细分析结果
    print(f"\n📊 Mega7改进版因子详细结果:")
    print("-" * 120)
    
    # 选择关键因子展示
    key_factors = [
        'tech_premium', 'adjusted_roe', 'fcf_quality', 
        'revenue_yoy', 'revenue_cagr', 'net_income_yoy', 
        'profit_revenue_ratio', 'fcf_cagr', 'roic', 'roic_cagr', 'effective_tax_rate'
    ]
    
    print(f"{'股票':<8}", end='')
    for factor in key_factors:
        print(f"{factor:<12}", end='')
    print()
    print("-" * 120)
    
    for _, row in mega7_df.iterrows():
        print(f"{row['stock_symbol']:<8}", end='')
        for factor in key_factors:
            value = row.get(factor, None)
            if value is not None:
                if abs(value) >= 1000:
                    print(f"{value:>10.0f}% ", end='')
                elif abs(value) >= 100:
                    print(f"{value:>10.1f}% ", end='')
                else:
                    print(f"{value:>10.2f}% ", end='')
            else:
                print(f"{'N/A':>10}% ", end='')
        print()
    
    print("-" * 120)
    
    # 因子覆盖率统计
    print(f"\n📈 Mega7因子覆盖率统计:")
    print("-" * 60)
    print(f"{'因子名称':<30} {'覆盖数量':<10} {'覆盖率'}")
    print("-" * 60)
    
    for col in mega7_df.columns:
        if col != 'stock_symbol':
            valid_count = mega7_df[col].notna().sum()
            coverage = (valid_count / len(mega7_df)) * 100
            print(f"{col:<30} {valid_count}/{len(mega7_df):<8} {coverage:>6.1f}%")
    
    print("-" * 60)
    
    # 关键发现
    print(f"\n🎯 Mega7关键发现:")
    
    # 技术投入分析
    tech_companies = mega7_df[mega7_df['tech_premium'].notna()]
    if len(tech_companies) > 0:
        print(f"   🔬 技术投入强度 (R&D/营收):")
        for _, row in tech_companies.iterrows():
            print(f"      • {row['stock_symbol']}: {row['tech_premium']:.2f}%")
    
    # CAGR表现分析
    print(f"   📈 CAGR表现:")
    cagr_factors = ['revenue_cagr', 'fcf_cagr', 'roic_cagr']
    for factor in cagr_factors:
        valid_data = mega7_df[mega7_df[factor].notna()]
        if len(valid_data) > 0:
            avg_cagr = valid_data[factor].mean()
            print(f"      • {factor}: 平均{avg_cagr:.1f}% ({len(valid_data)}只股票)")
    
    # 盈利能力分析
    profitability_factors = ['adjusted_roe', 'roic', 'profit_revenue_ratio']
    print(f"   💰 盈利能力:")
    for factor in profitability_factors:
        valid_data = mega7_df[mega7_df[factor].notna()]
        if len(valid_data) > 0:
            avg_val = valid_data[factor].mean()
            print(f"      • {factor}: 平均{avg_val:.1f}% ({len(valid_data)}只股票)")
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"colin/mega7_improved_factors_{timestamp}.csv"
    mega7_df.to_csv(filename, index=False, encoding='utf-8-sig')
    print(f"\n✅ Mega7改进版因子数据已保存到: {filename}")
    
    return mega7_df

if __name__ == "__main__":
    result = analyze_mega7_improved_factors()

