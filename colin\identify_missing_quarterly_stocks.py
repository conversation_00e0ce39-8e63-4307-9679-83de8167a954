from clickhouse_driver import Client

# 连接ClickHouse数据库
client = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 识别季报缺失的具体股票 ===")

# 分析2023年和2024年的缺失情况
years_to_check = [2024, 2023]

for year in years_to_check:
    print(f"\n{year}年季报缺失分析:")
    
    # 获取该年有Q4数据的所有股票（作为基准）
    query_base_stocks = f"""
    SELECT DISTINCT stock_symbol
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE financial_period_absolute = 'FY{year}Q4'
    ORDER BY stock_symbol
    """
    
    result_base = client.execute(query_base_stocks)
    base_stocks = set(stock for (stock,) in result_base)
    print(f"  有FY{year}Q4数据的股票数: {len(base_stocks)}")
    
    # 检查每个季度的缺失情况
    for quarter in ['Q1', 'Q2', 'Q3']:
        query_quarter_stocks = f"""
        SELECT DISTINCT stock_symbol
        FROM priority_quality_fundamental_data_complete_deduped
        WHERE financial_period_absolute = 'FY{year}{quarter}'
        ORDER BY stock_symbol
        """
        
        result_quarter = client.execute(query_quarter_stocks)
        quarter_stocks = set(stock for (stock,) in result_quarter)
        
        missing_stocks = base_stocks - quarter_stocks
        
        print(f"  FY{year}{quarter}:")
        print(f"    有数据的股票数: {len(quarter_stocks)}")
        print(f"    缺失的股票数: {len(missing_stocks)}")
        
        if missing_stocks:
            missing_list = sorted(list(missing_stocks))
            if len(missing_list) <= 10:
                print(f"    缺失的股票: {', '.join(missing_list)}")
            else:
                print(f"    缺失的股票（前10只）: {', '.join(missing_list[:10])}")
                print(f"    ... 还有{len(missing_list) - 10}只")

# 分析整体季报模式
print(f"\n整体季报数据模式分析:")

# 统计每只股票有多少个季报期间
query_stock_quarters = """
SELECT 
    stock_symbol,
    COUNT(DISTINCT financial_period_absolute) as quarter_count
FROM priority_quality_fundamental_data_complete_deduped
WHERE financial_period_absolute REGEXP 'FY[0-9]{4}Q[1-4]'
  AND financial_period_absolute LIKE 'FY2023%'
GROUP BY stock_symbol
ORDER BY quarter_count, stock_symbol
"""

result_stock_quarters = client.execute(query_stock_quarters)

# 统计分布
quarter_distribution = {}
for stock, count in result_stock_quarters:
    if count not in quarter_distribution:
        quarter_distribution[count] = []
    quarter_distribution[count].append(stock)

print("2023年每只股票的季报数量分布:")
for count in sorted(quarter_distribution.keys()):
    stocks = quarter_distribution[count]
    print(f"  {count}个季度: {len(stocks)}只股票")
    if len(stocks) <= 10:
        print(f"    股票: {', '.join(stocks)}")
    else:
        print(f"    股票（前10只）: {', '.join(stocks[:10])}")
        print(f"    ... 还有{len(stocks) - 10}只")

print("\n=== 分析完成 ===")

