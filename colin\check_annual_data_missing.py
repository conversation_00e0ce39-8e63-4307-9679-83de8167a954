from clickhouse_driver import Client
import pandas as pd

# 连接ClickHouse数据库
client = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 年报数据缺失情况检查 ===")

# 1. 获取所有财务数据
print("\n1. 获取所有财务数据...")
query_all = """
SELECT 
    stock_symbol,
    financial_period_absolute,
    statement_type,
    item_name,
    value,
    announcement_date,
    effective_date
FROM priority_quality_fundamental_data_complete_deduped
WHERE effective_date IS NOT NULL
ORDER BY stock_symbol, financial_period_absolute, statement_type, item_name
"""

result_all = client.execute(query_all)
df_all = pd.DataFrame(result_all, columns=['stock_symbol', 'financial_period_absolute', 'statement_type', 'item_name', 'value', 'announcement_date', 'effective_date'])

print(f"  总财务数据记录数: {len(df_all):,}")
print(f"  唯一股票数: {df_all['stock_symbol'].nunique():,}")

# 2. 筛选年报数据
print("\n2. 筛选年报数据...")
df_annual = df_all[
    (df_all['financial_period_absolute'].str.contains('Q4')) |
    (df_all['financial_period_absolute'].str.contains('FY\d{4}$'))
].copy()

print(f"  年报数据记录数: {len(df_annual):,}")
print(f"  有年报数据的股票数: {df_annual['stock_symbol'].nunique():,}")

# 3. 分析缺失情况
print("\n3. 分析缺失情况...")
all_stocks = set(df_all['stock_symbol'].unique())
annual_stocks = set(df_annual['stock_symbol'].unique())
missing_stocks = all_stocks - annual_stocks

print(f"  总股票数: {len(all_stocks):,}")
print(f"  有年报数据的股票数: {len(annual_stocks):,}")
print(f"  缺失年报数据的股票数: {len(missing_stocks):,}")
print(f"  年报数据覆盖率: {len(annual_stocks)/len(all_stocks)*100:.1f}%")

# 4. 显示缺失的股票（前20只）
if len(missing_stocks) > 0:
    print(f"\n4. 缺失年报数据的股票列表（前20只）:")
    for i, stock in enumerate(sorted(list(missing_stocks))[:20]):
        print(f"  {i+1:2d}. {stock}")
    if len(missing_stocks) > 20:
        print(f"  ... 还有 {len(missing_stocks) - 20} 只股票")

# 5. 分析年报数据的年份分布
print("\n5. 年报数据年份分布...")
if len(df_annual) > 0:
    # 提取财年信息
    df_annual['fiscal_year'] = df_annual['financial_period_absolute'].str.extract(r'FY(\d{4})').astype(float)
    
    year_distribution = df_annual.groupby('stock_symbol')['fiscal_year'].agg(['count', 'min', 'max']).reset_index()
    year_distribution.columns = ['stock_symbol', 'year_count', 'min_year', 'max_year']
    
    print(f"  平均年份数: {year_distribution['year_count'].mean():.1f}")
    print(f"  最少年份数: {year_distribution['year_count'].min()}")
    print(f"  最多年份数: {year_distribution['year_count'].max()}")
    
    # 统计不同年份数的股票数量
    year_count_stats = year_distribution['year_count'].value_counts().sort_index()
    print(f"  年份数分布:")
    for year_count, stock_count in year_count_stats.items():
        print(f"    {year_count}年: {stock_count}只股票")

# 6. 分析effective_date的分布
print("\n6. Effective Date分布...")
if len(df_annual) > 0:
    df_annual['effective_date'] = pd.to_datetime(df_annual['effective_date'])
    print(f"  Effective Date范围: {df_annual['effective_date'].min()} 到 {df_annual['effective_date'].max()}")
    print(f"  唯一Effective Date数量: {df_annual['effective_date'].nunique():,}")

print("\n=== 检查完成 ===")




