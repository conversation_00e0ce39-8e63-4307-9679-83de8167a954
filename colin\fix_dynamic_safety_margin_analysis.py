def analyze_dynamic_safety_margin():
    """专门分析dynamic_safety_margin因子"""
    
    print("=== 分析dynamic_safety_margin因子 ===")
    print()
    
    # 读取因子计算文件
    try:
        with open('colin/calculate_26_factors_from_database.py', 'r', encoding='utf-8') as f:
            content = f.read()
    except:
        print("❌ 无法读取因子计算文件")
        return
    
    # 查找dynamic_safety_margin的计算逻辑
    import re
    pattern = r"# 7\. 动态安全边际 \(dynamic_safety_margin\)(.*?)# 8\."
    matches = re.findall(pattern, content, re.DOTALL)
    
    if matches:
        code_block = matches[0]
        print("📊 dynamic_safety_margin 计算逻辑:")
        print("-" * 60)
        print(code_block.strip())
        print("-" * 60)
        print()
        
        print("🔍 逻辑分析:")
        print("   📋 计算方式: 组合因子")
        print("   📊 公式: tech_premium + ecosystem_cash_ratio")
        print("   🔗 依赖因子: tech_premium（技术溢价）+ ecosystem_cash_ratio（生态现金比率）")
        print("   ⏱️  时间窗口: 当前年（继承依赖因子的时间窗口）")
        print("   📈 数据要求: 1年（需要两个依赖因子都非空）")
        print("   🛡️  负值处理: 继承依赖因子的处理方式")
        print()
        
        print("✅ 因子特点:")
        print("   • 这是一个组合因子，不直接使用原始财务数据")
        print("   • 结合了研发投入强度和现金储备能力")
        print("   • 反映公司在技术投资和财务安全之间的平衡")
        print("   • 数值越高表示公司既有技术投入又有现金保障")
        print()
        
        print("🎯 经济含义:")
        print("   • tech_premium: R&D支出/营收，反映技术投入强度")
        print("   • ecosystem_cash_ratio: 现金/营收，反映现金储备能力")
        print("   • dynamic_safety_margin: 技术投入+现金储备的综合安全边际")
        print("   • 适用于评估科技公司的长期竞争力和风险承受能力")
        
    else:
        print("❌ 未找到dynamic_safety_margin的计算逻辑")
    
    print()
    print("🔧 为什么之前显示'未知':")
    print("   • 分析脚本的正则表达式模式不够精确")
    print("   • 没有识别出组合因子的特殊计算方式")
    print("   • dynamic_safety_margin不直接使用latest_data，而是使用其他因子的结果")

if __name__ == "__main__":
    analyze_dynamic_safety_margin()

