from clickhouse_driver import Client
from datetime import datetime

def create_priority_quality_stock_hfq_table():
    """
    创建priority_quality_stock_hfq表，包含821个同时有基本面和价格数据的优质资产
    """
    print("="*80)
    print("创建priority_quality_stock_hfq表 - 优质资产价格数据")
    print("="*80)
    
    try:
        # 连接到两个数据库
        lseg_client = Client(
            host='************',
            port=9000,
            user='default',
            password='5ur2pK8WZQdy2',
            database='lseg'
        )
        
        ap_client = Client(
            host='************',
            port=9000,
            user='default',
            password='5ur2pK8WZQdy2',
            database='ap_research'
        )
        
        print("✅ 成功连接到两个数据库!")
        
        # 1. 获取821个优质资产列表
        print(f"\n📋 获取优质资产列表...")
        
        quality_assets = ap_client.execute("""
            SELECT stock_symbol, turnover_type, original_stock_code, 
                   cumulative_return, annualized_return, sharpe_ratio,
                   annualized_volatility, max_drawdown, avg_trading_volume,
                   start_price, end_price, trading_days
            FROM stock_performance_2020_2025_cumulative
            ORDER BY cumulative_return DESC
        """)
        
        print(f"✅ 获取到 {len(quality_assets)} 个优质资产")
        
        # 2. 创建新表
        table_name = 'priority_quality_stock_hfq'
        print(f"\n🏗️ 创建表: {table_name}")
        
        # 删除表如果存在
        try:
            ap_client.execute(f"DROP TABLE IF EXISTS {table_name}")
            print(f"✅ 清理现有表")
        except Exception as e:
            print(f"⚠️ 清理表时警告: {e}")
        
        # 创建表结构
        create_table_sql = f"""
        CREATE TABLE {table_name} (
            -- 股票基本信息
            original_stock_code String,
            stock_symbol String,
            turnover_type String,
            ric String,
            exchange String,
            
            -- 价格数据
            trade_date String,
            open Float64,
            high Float64,
            low Float64,
            close Float64,
            volume Float64,
            turnover Float64,
            
            -- 表现指标
            cumulative_return Float64,
            annualized_return Float64,
            sharpe_ratio Float64,
            annualized_volatility Float64,
            max_drawdown Float64,
            avg_trading_volume Float64,
            start_price Float64,
            end_price Float64,
            trading_days UInt32,
            
            -- 元数据
            has_fundamental_data UInt8 DEFAULT 1,
            has_hfq_data UInt8 DEFAULT 1,
            create_date DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        PARTITION BY toYYYYMM(toDate(trade_date))
        ORDER BY (stock_symbol, trade_date)
        """
        
        ap_client.execute(create_table_sql)
        print(f"✅ 表创建成功!")
        
        # 3. 准备数据插入
        print(f"\n📤 开始数据插入...")
        
        # 插入SQL
        insert_sql = f"""
        INSERT INTO {table_name} (
            original_stock_code, stock_symbol, turnover_type, ric, exchange,
            trade_date, open, high, low, close, volume, turnover,
            cumulative_return, annualized_return, sharpe_ratio,
            annualized_volatility, max_drawdown, avg_trading_volume,
            start_price, end_price, trading_days
        ) VALUES
        """
        
        # 批量处理资产
        total_records = 0
        batch_size = 1000  # 每批处理1000条记录
        processed_assets = 0
        
        for asset_info in quality_assets:
            stock_symbol = asset_info[0]
            turnover_type = asset_info[1]
            original_stock_code = asset_info[2]
            
            # 基本指标
            cumulative_return = asset_info[3]
            annualized_return = asset_info[4]
            sharpe_ratio = asset_info[5]
            annualized_volatility = asset_info[6]
            max_drawdown = asset_info[7]
            avg_trading_volume = asset_info[8]
            start_price = asset_info[9]
            end_price = asset_info[10]
            trading_days = asset_info[11]
            
            # 获取该资产的价格数据
            hfq_data = lseg_client.execute(f"""
                SELECT ric, exchange, trade_date, open, high, low, close, volume, turnover
                FROM hfq_intrday_1_day
                WHERE splitByChar('.', ric)[1] = '{stock_symbol}'
                ORDER BY trade_date
            """)
            
            if not hfq_data:
                print(f"  ⚠️ {original_stock_code} ({stock_symbol}): 未找到价格数据")
                continue
            
            # 准备批量插入数据
            batch_data = []
            for ric, exchange, trade_date, open_p, high, low, close, volume, turnover in hfq_data:
                record = (
                    original_stock_code, stock_symbol, turnover_type, ric, exchange,
                    trade_date, open_p, high, low, close, volume, turnover,
                    cumulative_return, annualized_return, sharpe_ratio,
                    annualized_volatility, max_drawdown, avg_trading_volume,
                    start_price, end_price, trading_days
                )
                batch_data.append(record)
                
                # 批量插入
                if len(batch_data) >= batch_size:
                    ap_client.execute(insert_sql, batch_data)
                    total_records += len(batch_data)
                    batch_data = []
            
            # 插入剩余数据
            if batch_data:
                ap_client.execute(insert_sql, batch_data)
                total_records += len(batch_data)
            
            processed_assets += 1
            
            if processed_assets % 50 == 0:  # 每处理50个资产显示进度
                print(f"  ✅ 已处理 {processed_assets}/{len(quality_assets)} 个资产，插入 {total_records:,} 条记录")
        
        print(f"🎉 数据插入完成!")
        print(f"📊 总计插入 {total_records:,} 条价格记录，覆盖 {processed_assets} 个资产")
        
        # 4. 验证插入结果
        print(f"\n🔍 验证插入结果...")
        
        # 总记录数
        count_result = ap_client.execute(f"SELECT COUNT(*) FROM {table_name}")
        inserted_count = count_result[0][0]
        print(f"📊 表中总记录数: {inserted_count:,}")
        
        # 资产数量
        asset_count = ap_client.execute(f"SELECT COUNT(DISTINCT stock_symbol) FROM {table_name}")
        asset_num = asset_count[0][0]
        print(f"📈 覆盖资产数: {asset_num}")
        
        # 时间范围
        time_range = ap_client.execute(f"""
            SELECT MIN(trade_date) as start_date, MAX(trade_date) as end_date
            FROM {table_name}
        """)
        start_date, end_date = time_range[0]
        print(f"📅 数据时间范围: {start_date} 到 {end_date}")
        
        # 按资产类型统计
        type_stats = ap_client.execute(f"""
            SELECT turnover_type, 
                   COUNT(DISTINCT stock_symbol) as assets,
                   COUNT(*) as records,
                   AVG(cumulative_return) as avg_return
            FROM {table_name}
            GROUP BY turnover_type
            ORDER BY assets DESC
        """)
        
        print(f"\n📈 按资产类型统计:")
        print(f"{'类型':<10} {'资产数':<8} {'记录数':<10} {'平均收益(%)':<15}")
        print("-" * 50)
        
        for t_type, assets, records, avg_return in type_stats:
            print(f"{t_type:<10} {assets:<8} {records:<10} {avg_return:<15.2f}")
        
        # 显示表现最佳的资产及其最新价格
        top_performers = ap_client.execute(f"""
            SELECT original_stock_code, stock_symbol, turnover_type,
                   cumulative_return, COUNT(*) as price_records,
                   MIN(trade_date) as first_date, MAX(trade_date) as last_date
            FROM {table_name}
            GROUP BY original_stock_code, stock_symbol, turnover_type, cumulative_return
            ORDER BY cumulative_return DESC
            LIMIT 10
        """)
        
        print(f"\n🏆 表现最佳的10个资产及其价格数据:")
        print(f"{'原始代码':<12} {'股票代码':<8} {'类型':<8} {'收益率(%)':<12} {'价格记录数':<12} {'数据起始':<12} {'数据结束'}")
        print("-" * 90)
        
        for orig_code, symbol, t_type, cum_ret, price_count, first_date, last_date in top_performers:
            print(f"{orig_code:<12} {symbol:<8} {t_type:<8} {cum_ret:<12.2f} {price_count:<12} {first_date:<12} {last_date}")
        
        # 检查最新价格数据
        latest_prices = ap_client.execute(f"""
            SELECT stock_symbol, original_stock_code, trade_date, close, volume
            FROM {table_name}
            WHERE trade_date = (SELECT MAX(trade_date) FROM {table_name})
            ORDER BY cumulative_return DESC
            LIMIT 10
        """)
        
        print(f"\n📊 最新交易日价格数据样例:")
        print(f"{'股票代码':<8} {'原始代码':<12} {'交易日':<12} {'收盘价':<10} {'成交量'}")
        print("-" * 60)
        
        for symbol, orig_code, trade_date, close, volume in latest_prices:
            print(f"{symbol:<8} {orig_code:<12} {trade_date:<12} {close:<10.2f} {volume:.0f}")
        
        # 5. 保存创建日志
        print(f"\n💾 保存创建日志...")
        
        with open('priority_quality_stock_hfq_creation_log.txt', 'w', encoding='utf-8') as f:
            f.write("priority_quality_stock_hfq表创建日志\n")
            f.write("="*50 + "\n\n")
            f.write(f"创建时间: {datetime.now()}\n")
            f.write(f"表名: {table_name}\n")
            f.write(f"数据库: ap_research\n\n")
            
            f.write(f"数据统计:\n")
            f.write(f"- 总记录数: {inserted_count:,}\n")
            f.write(f"- 覆盖资产数: {asset_num}\n")
            f.write(f"- 数据时间范围: {start_date} 到 {end_date}\n\n")
            
            f.write(f"按类型统计:\n")
            for t_type, assets, records, avg_return in type_stats:
                f.write(f"- {t_type}: {assets}个资产, {records}条记录, 平均收益{avg_return:.2f}%\n")
            
            f.write(f"\n表结构特点:\n")
            f.write(f"- 按月分区存储（基于trade_date）\n")
            f.write(f"- 主键：(stock_symbol, trade_date)\n")
            f.write(f"- 包含完整的股票基本信息和价格数据\n")
            f.write(f"- 所有资产都有基本面和价格数据\n")
        
        print(f"✅ 创建日志已保存: priority_quality_stock_hfq_creation_log.txt")
        
        print(f"\n" + "="*80)
        print(f"✅ priority_quality_stock_hfq表创建完成!")
        print(f"📊 表中包含 {asset_num} 个优质资产的 {inserted_count:,} 条价格记录")
        print(f"🎯 所有资产都具备完整的基本面和价格数据")
        print(f"📅 数据覆盖 {start_date} 到 {end_date}")
        print("="*80)
        
    except Exception as e:
        print(f"❌ 创建过程中发生错误: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    create_priority_quality_stock_hfq_table()