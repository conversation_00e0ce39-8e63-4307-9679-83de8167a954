import re

def check_annual_factor_logic():
    """检查所有年报因子计算逻辑是否有数据混合问题"""
    
    print("=== 检查所有年报因子计算逻辑 ===")
    print()
    
    # 读取主要因子计算文件
    try:
        with open('colin/calculate_26_factors_from_database.py', 'r', encoding='utf-8') as f:
            content = f.read()
    except:
        print("❌ 无法读取因子计算文件")
        return
    
    # 1. 检查数据获取逻辑
    print("1. 检查数据获取逻辑:")
    
    # 查找年报数据获取的SQL
    annual_queries = re.findall(r'annual_query\s*=\s*"""(.*?)"""', content, re.DOTALL)
    if annual_queries:
        for i, query in enumerate(annual_queries, 1):
            print(f"   年报查询 {i}:")
            lines = query.strip().split('\n')
            for line in lines:
                if 'WHERE' in line or 'financial_period_absolute' in line:
                    print(f"     {line.strip()}")
            
            # 检查是否正确过滤年报
            if "REGEXP '^FY[0-9]{4}$'" in query:
                print("     ✅ 正确：严格匹配年报格式")
            elif "substring(financial_period_absolute, 1, 2) = 'FY'" in query:
                print("     ⚠️  警告：可能包含季报数据")
            else:
                print("     ❌ 错误：未发现年报过滤逻辑")
    else:
        print("   ❌ 未找到年报查询逻辑")
    
    print()
    
    # 2. 检查涉及历史数据比较的因子
    print("2. 检查涉及历史数据比较的因子:")
    
    historical_factors = [
        'revenue_yoy',      # 营收同比
        'revenue_cagr',     # 营收CAGR  
        'net_income_yoy',   # 净利润同比
        'fcf_cagr',         # FCF CAGR
        'roic_cagr',        # ROIC CAGR
        'roce_stability',   # ROCE稳定性
        'operating_margin_std',  # 营业利润率标准差
        'effective_tax_rate_std',  # 有效税率标准差
        'revenue_growth_continuity'  # 营收增长连续性
    ]
    
    for factor in historical_factors:
        print(f"   🔍 检查 {factor}:")
        
        # 查找因子计算逻辑
        pattern = rf'# .*{factor}.*\n(.*?)except:'
        matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
        
        if matches:
            factor_code = matches[0]
            
            # 检查是否有循环遍历历史数据
            if 'for i in range' in factor_code or 'stock_data.iloc[' in factor_code:
                print("     ✅ 发现历史数据遍历")
                
                # 检查是否使用了正确的数据源
                if 'stock_data' in factor_code:
                    print("     ✅ 使用stock_data（应该是年报数据）")
                else:
                    print("     ⚠️  未明确数据源")
                
                # 检查是否有数据类型验证
                if 'FY' in factor_code and 'Q' in factor_code:
                    print("     ❌ 警告：代码中同时出现FY和Q，可能混合年报季报")
                elif 'FY' in factor_code:
                    print("     ✅ 只涉及年报数据")
                else:
                    print("     ⚠️  未明确数据周期")
            else:
                print("     ⚠️  未发现明显的历史数据遍历")
        else:
            print("     ❌ 未找到因子计算逻辑")
    
    print()
    
    # 3. 检查具体的问题代码段
    print("3. 检查具体的潜在问题:")
    
    # 查找可能的问题模式
    problem_patterns = [
        (r'substring\(.*1.*2.*FY', "使用substring(1,2)='FY'可能包含季报"),
        (r'financial_period_absolute.*LIKE.*FY', "使用LIKE '%FY%'可能包含季报"),
        (r'Q[1-4]', "代码中出现季度标识"),
    ]
    
    for pattern, description in problem_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        if matches:
            print(f"   ❌ 发现潜在问题: {description}")
            print(f"     匹配内容: {matches[:3]}")  # 只显示前3个匹配
        else:
            print(f"   ✅ 未发现问题: {description}")
    
    print()
    
    # 4. 提出修正建议
    print("4. 修正建议:")
    print("   📋 数据获取层面:")
    print("     ✅ 确保年报查询使用: REGEXP '^FY[0-9]{4}$'")
    print("     ✅ 完全排除季报数据: 不能包含Q1,Q2,Q3,Q4")
    print()
    print("   📋 因子计算层面:")
    print("     ✅ 所有历史比较只在年报数据内进行")
    print("     ✅ CAGR计算只使用年报时间序列")
    print("     ✅ 同比增长只比较相同财年类型")
    print()
    print("   📋 验证要求:")
    print("     ✅ 每个涉及历史数据的因子都需要验证数据源纯度")
    print("     ✅ 确保effective_date匹配trade_date的逻辑正确")

if __name__ == "__main__":
    check_annual_factor_logic()

