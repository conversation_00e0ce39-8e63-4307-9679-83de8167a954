from clickhouse_driver import Client
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')
import time

# 连接ClickHouse数据库
client = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='lseg'
)

print("=== <PERSON>成长价值优势投资法则因子实现（817个股票完整版）===")
print("计算所有26个核心因子，分析817个股票的覆盖率")

def get_817_stocks():
    """获取817个股票列表并正确处理股票代码"""
    print("\n1. 获取817个股票列表...")
    
    try:
        # 从CSV文件读取817个股票列表
        df_filtered = pd.read_csv('stock_performance_2020_2025_cumulative.csv')
        filtered_stocks = df_filtered['股票代码'].tolist()
        
        print(f"  从CSV文件读取到 {len(filtered_stocks)} 只股票")
        
        # 显示前10只股票作为示例
        print(f"  前10只股票示例:")
        for i, stock in enumerate(filtered_stocks[:10]):
            print(f"    {i+1:2d}. {stock}")
        
        # 检查Mega7股票
        mega7_stocks = ['AAPL.O', 'MSFT.O', 'GOOGL.O', 'GOOG.O', 'AMZN.O', 'NVDA.O', 'META.O', 'TSLA.O']
        print(f"\n  Mega7股票检查:")
        for stock in mega7_stocks:
            if stock in filtered_stocks:
                print(f"    ✓ {stock} 在列表中")
            else:
                print(f"    ✗ {stock} 不在列表中")
        
        return filtered_stocks
        
    except FileNotFoundError:
        print("  错误：找不到股票列表文件 stock_performance_2020_2025_cumulative.csv")
        return []
    except Exception as e:
        print(f"  读取股票列表时出错: {e}")
        return []

def get_income_statement_data(stocks):
    """获取利润表数据 - 只获取817个股票的数据，去掉后缀匹配"""
    print("\n2. 获取利润表数据...")
    
    # 构建股票代码条件 - 去掉后缀进行匹配
    stock_conditions = []
    for stock in stocks:
        # 去掉后缀，只保留基础代码
        base_code = stock.split('.')[0] if '.' in stock else stock
        stock_conditions.append(f"i.instrument LIKE '{base_code}%'")
    
    stock_filter = " OR ".join(stock_conditions)
    
    income_query = f"""
    SELECT 
        i.instrument,
        i.financial_period_absolute,
        i.item_name,
        i.income_statement as value,
        sh.original_announcement_date_time
    FROM lseg.income_statement i
    LEFT JOIN lseg.statement_header sh
        ON i.instrument = sh.instrument
        AND i.financial_period_absolute = sh.financial_period_absolute
        AND sh.statement_type = 'Income Statement'
    WHERE ({stock_filter})
    AND i.item_name IN (
        'Revenue from Business Activities - Total',
        'Revenue from Goods & Services',
        'Cost of Revenues - Total',
        'Cost of Operating Revenue',
        'Research & Development Expense',
        'Research & Development Expense - Supplemental',
        'Operating Profit before Non-Recurring Income/Expense',
        'Income before Taxes',
        'Earnings before Interest, Taxes, Depreciation & Amortization (EBITDA)',
        'Net Income - Basic - including Extraordinary Items Applicable to Common - Total',
        'Normalized Net Income - Bottom Line',
        'Earnings before Interest & Taxes (EBIT)',
        'Income Taxes'
    )
    AND i.financial_period_absolute >= 'FY2018Q1'
    AND sh.original_announcement_date_time IS NOT NULL
    """
    
    income_data = client.execute(income_query)
    df_income = pd.DataFrame(income_data, columns=['instrument', 'financial_period_absolute', 'item_name', 'value', 'original_announcement_date_time'])
    
    print(f"  利润表数据记录数: {len(df_income):,}")
    
    # 显示找到的股票数量
    unique_stocks = df_income['instrument'].nunique()
    print(f"  找到 {unique_stocks} 只股票的利润表数据")
    
    # 检查Mega7股票是否找到
    mega7_base_codes = ['AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'NVDA', 'META', 'TSLA']
    found_mega7 = []
    for base_code in mega7_base_codes:
        matching_stocks = df_income[df_income['instrument'].str.startswith(base_code)]
        if len(matching_stocks) > 0:
            found_mega7.append(base_code)
            print(f"    ✓ 找到 {base_code} 相关股票: {matching_stocks['instrument'].unique()}")
        else:
            print(f"    ✗ 未找到 {base_code} 相关股票")
    
    print(f"  找到 {len(found_mega7)} 只Mega7相关股票")
    
    return df_income

def get_balance_sheet_data(stocks):
    """获取资产负债表数据 - 只获取817个股票的数据，去掉后缀匹配"""
    print("\n3. 获取资产负债表数据...")
    
    # 构建股票代码条件 - 去掉后缀进行匹配
    stock_conditions = []
    for stock in stocks:
        # 去掉后缀，只保留基础代码
        base_code = stock.split('.')[0] if '.' in stock else stock
        stock_conditions.append(f"b.instrument LIKE '{base_code}%'")
    
    stock_filter = " OR ".join(stock_conditions)
    
    balance_query = f"""
    SELECT 
        b.instrument,
        b.financial_period_absolute,
        b.item_name,
        b.balance_sheet as value,
        sh.original_announcement_date_time
    FROM lseg.balance_sheet_history b
    LEFT JOIN lseg.statement_header sh
        ON b.instrument = sh.instrument
        AND b.financial_period_absolute = sh.financial_period_absolute
        AND sh.statement_type = 'Balance Sheet'
    WHERE ({stock_filter})
    AND b.item_name IN (
        'Total Assets',
        'Common Equity - Total',
        'Total Liabilities',
        'Debt - Total',
        'Cash & Cash Equivalents - Total',
        'Intangible Assets - Total - Net',
        'Common Shares - Outstanding - Total'
    )
    AND b.financial_period_absolute >= 'FY2018Q1'
    AND sh.original_announcement_date_time IS NOT NULL
    """
    
    balance_data = client.execute(balance_query)
    df_balance = pd.DataFrame(balance_data, columns=['instrument', 'financial_period_absolute', 'item_name', 'value', 'original_announcement_date_time'])
    
    print(f"  资产负债表数据记录数: {len(df_balance):,}")
    
    # 显示找到的股票数量
    unique_stocks = df_balance['instrument'].nunique()
    print(f"  找到 {unique_stocks} 只股票的资产负债表数据")
    
    return df_balance

def get_cash_flow_data(stocks):
    """获取现金流量表数据 - 只获取817个股票的数据，去掉后缀匹配"""
    print("\n4. 获取现金流量表数据...")
    
    # 构建股票代码条件 - 去掉后缀进行匹配
    stock_conditions = []
    for stock in stocks:
        # 去掉后缀，只保留基础代码
        base_code = stock.split('.')[0] if '.' in stock else stock
        stock_conditions.append(f"c.instrument LIKE '{base_code}%'")
    
    stock_filter = " OR ".join(stock_conditions)
    
    cash_flow_query = f"""
    SELECT 
        c.instrument,
        c.financial_period_absolute,
        c.item_name,
        c.cash_flow_statement as value,
        sh.original_announcement_date_time
    FROM lseg.cash_flow c
    LEFT JOIN lseg.statement_header sh
        ON c.instrument = sh.instrument
        AND c.financial_period_absolute = sh.financial_period_absolute
        AND sh.statement_type = 'Statement of Cash Flows'
    WHERE ({stock_filter})
    AND c.item_name IN (
        'Net Cash Flow from Operating Activities',
        'Capital Expenditures - Total',
        'Income Taxes - Paid/(Reimbursed) - Cash Flow',
        'Income Taxes - Paid/(Reimbursed) - Cash Flow - Supplemental'
    )
    AND c.financial_period_absolute >= 'FY2018Q1'
    AND sh.original_announcement_date_time IS NOT NULL
    """
    
    cash_flow_data = client.execute(cash_flow_query)
    df_cash_flow = pd.DataFrame(cash_flow_data, columns=['instrument', 'financial_period_absolute', 'item_name', 'value', 'original_announcement_date_time'])
    
    print(f"  现金流量表数据记录数: {len(df_cash_flow):,}")
    
    # 显示找到的股票数量
    unique_stocks = df_cash_flow['instrument'].nunique()
    print(f"  找到 {unique_stocks} 只股票的现金流量表数据")
    
    return df_cash_flow

def merge_all_data(df_income, df_balance, df_cash_flow):
    """合并所有财务数据"""
    print("\n5. 合并财务数据...")
    
    # 转换日期格式
    for df in [df_income, df_balance, df_cash_flow]:
        df['original_announcement_date_time'] = pd.to_datetime(df['original_announcement_date_time'])
        df['effective_date'] = df['original_announcement_date_time'] + timedelta(days=1)
    
    # 数据透视
    df_income_pivot = df_income.pivot_table(
        index=['instrument', 'financial_period_absolute', 'original_announcement_date_time', 'effective_date'],
        columns='item_name',
        values='value',
        aggfunc='first'
    ).reset_index()
    
    df_balance_pivot = df_balance.pivot_table(
        index=['instrument', 'financial_period_absolute', 'original_announcement_date_time', 'effective_date'],
        columns='item_name',
        values='value',
        aggfunc='first'
    ).reset_index()
    
    df_cash_flow_pivot = df_cash_flow.pivot_table(
        index=['instrument', 'financial_period_absolute', 'original_announcement_date_time', 'effective_date'],
        columns='item_name',
        values='value',
        aggfunc='first'
    ).reset_index()
    
    # 合并数据
    df_merged = df_income_pivot.merge(
        df_balance_pivot, 
        on=['instrument', 'financial_period_absolute', 'original_announcement_date_time', 'effective_date'], 
        how='outer'
    ).merge(
        df_cash_flow_pivot, 
        on=['instrument', 'financial_period_absolute', 'original_announcement_date_time', 'effective_date'], 
        how='outer'
    )
    
    print(f"  合并后数据记录数: {len(df_merged):,}")
    print(f"  涉及股票数: {df_merged['instrument'].nunique()}")
    
    # 检查Mega7股票在合并数据中的情况
    mega7_base_codes = ['AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'NVDA', 'META', 'TSLA']
    print(f"\n  Mega7股票在合并数据中的情况:")
    for base_code in mega7_base_codes:
        matching_stocks = df_merged[df_merged['instrument'].str.startswith(base_code)]
        if len(matching_stocks) > 0:
            print(f"    ✓ {base_code}: {matching_stocks['instrument'].unique()}")
        else:
            print(f"    ✗ {base_code}: 未找到")
    
    return df_merged

def calculate_all_26_factors(df):
    """计算所有26个因子"""
    print("\n6. 计算26个因子...")
    
    # 数据预处理
    df = df.copy()
    
    # 提取财年信息
    df['fiscal_year'] = df['financial_period_absolute'].str.extract(r'FY(\d{4})').astype(float)
    df['quarter'] = df['financial_period_absolute'].str.extract(r'Q(\d)').astype(float)
    
    # 按股票和财年分组计算年度数据
    annual_data = []
    
    for instrument in df['instrument'].unique():
        stock_data = df[df['instrument'] == instrument].copy()
        
        for year in stock_data['fiscal_year'].unique():
            year_data = stock_data[stock_data['fiscal_year'] == year]
            
            # 计算年度汇总数据
            annual_row = {
                'instrument': instrument,
                'fiscal_year': year
            }
            
            # 收入相关（季度数据汇总）
            revenue_cols = ['Revenue from Business Activities - Total', 'Revenue from Goods & Services']
            for col in revenue_cols:
                if col in year_data.columns:
                    values = year_data[col].dropna()
                    if len(values) > 0:
                        annual_row[col] = values.sum()
            
            # 成本相关（季度数据汇总）
            cost_cols = ['Cost of Revenues - Total', 'Cost of Operating Revenue']
            for col in cost_cols:
                if col in year_data.columns:
                    values = year_data[col].dropna()
                    if len(values) > 0:
                        annual_row[col] = values.sum()
            
            # 研发费用（季度数据汇总）
            rd_cols = ['Research & Development Expense', 'Research & Development Expense - Supplemental']
            for col in rd_cols:
                if col in year_data.columns:
                    values = year_data[col].dropna()
                    if len(values) > 0:
                        annual_row[col] = values.sum()
            
            # 其他财务指标（取最新值）
            other_cols = [
                'Operating Profit before Non-Recurring Income/Expense',
                'Income before Taxes',
                'Earnings before Interest, Taxes, Depreciation & Amortization (EBITDA)',
                'Net Income - Basic - including Extraordinary Items Applicable to Common - Total',
                'Normalized Net Income - Bottom Line',
                'Earnings before Interest & Taxes (EBIT)',
                'Income Taxes',
                'Total Assets',
                'Common Equity - Total',
                'Total Liabilities',
                'Debt - Total',
                'Cash & Cash Equivalents - Total',
                'Intangible Assets - Total - Net',
                'Common Shares - Outstanding - Total',
                'Net Cash Flow from Operating Activities',
                'Capital Expenditures - Total',
                'Income Taxes - Paid/(Reimbursed) - Cash Flow',
                'Income Taxes - Paid/(Reimbursed) - Cash Flow - Supplemental'
            ]
            
            for col in other_cols:
                if col in year_data.columns:
                    values = year_data[col].dropna()
                    if len(values) > 0:
                        annual_row[col] = values.iloc[-1]  # 取最新值
            
            annual_data.append(annual_row)
    
    # 转换为DataFrame
    df_annual = pd.DataFrame(annual_data)
    
    # 按股票分组计算因子
    factor_data = []
    
    for instrument in df_annual['instrument'].unique():
        stock_data = df_annual[df_annual['instrument'] == instrument].copy()
        stock_data = stock_data.sort_values('fiscal_year')
        
        if len(stock_data) < 2:
            continue
        
        # 计算因子
        factor_row = {'instrument': instrument}
        
        # 计算所有26个因子
        factor_row.update(calculate_26_factors(stock_data))
        
        factor_data.append(factor_row)
    
    df_factors = pd.DataFrame(factor_data)
    
    print(f"  计算完成，共 {len(df_factors)} 只股票")
    
    # 检查Mega7股票在因子结果中的情况
    mega7_base_codes = ['AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'NVDA', 'META', 'TSLA']
    print(f"\n  Mega7股票在因子结果中的情况:")
    for base_code in mega7_base_codes:
        matching_stocks = df_factors[df_factors['instrument'].str.startswith(base_code)]
        if len(matching_stocks) > 0:
            print(f"    ✓ {base_code}: {matching_stocks['instrument'].unique()}")
        else:
            print(f"    ✗ {base_code}: 未找到")
    
    return df_factors

def calculate_26_factors(stock_data):
    """计算26个因子"""
    factors = {}
    
    try:
        # 获取最新数据
        latest_data = stock_data.iloc[-1]
        prev_data = stock_data.iloc[-2] if len(stock_data) >= 2 else None
        
        # 1. 技术溢价 (tech_premium)
        try:
            rd_expense = latest_data.get('Research & Development Expense', 0) or 0
            rd_supplemental = latest_data.get('Research & Development Expense - Supplemental', 0) or 0
            total_rd = rd_expense + rd_supplemental
            revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
            
            if revenue > 0:
                factors['tech_premium'] = (total_rd / revenue) * 100
            else:
                factors['tech_premium'] = None
        except:
            factors['tech_premium'] = None
        
        # 2. 技术差距警告 (tech_gap_warning)
        try:
            if factors.get('tech_premium') is not None and factors['tech_premium'] < 5:
                factors['tech_gap_warning'] = 1
            else:
                factors['tech_gap_warning'] = 0
        except:
            factors['tech_gap_warning'] = 0
        
        # 3. 专利密度 (patent_density)
        try:
            intangible_assets = latest_data.get('Intangible Assets - Total - Net', 0) or 0
            total_assets = latest_data.get('Total Assets', 0) or 0
            
            if total_assets > 0:
                factors['patent_density'] = intangible_assets / total_assets
            else:
                factors['patent_density'] = None
        except:
            factors['patent_density'] = None
        
        # 4. 生态系统现金比率 (ecosystem_cash_ratio)
        try:
            cash = latest_data.get('Cash & Cash Equivalents - Total', 0) or 0
            total_assets = latest_data.get('Total Assets', 0) or 0
            
            if total_assets > 0:
                cash_ratio = cash / total_assets
                if cash_ratio > 0.1:
                    factors['ecosystem_cash_ratio'] = 0.1
                else:
                    factors['ecosystem_cash_ratio'] = cash_ratio
            else:
                factors['ecosystem_cash_ratio'] = None
        except:
            factors['ecosystem_cash_ratio'] = None
        
        # 5. 调整后ROCE (adjusted_roce)
        try:
            ebit = latest_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
            equity = latest_data.get('Common Equity - Total', 0) or 0
            
            if equity > 0:
                factors['adjusted_roce'] = ebit / equity
            else:
                factors['adjusted_roce'] = None
        except:
            factors['adjusted_roce'] = None
        
        # 6. FCF质量 (fcf_quality)
        try:
            operating_cash_flow = latest_data.get('Net Cash Flow from Operating Activities', 0) or 0
            capex = abs(latest_data.get('Capital Expenditures - Total', 0) or 0)
            net_income = latest_data.get('Net Income - Basic - including Extraordinary Items Applicable to Common - Total', 0) or 0
            
            if net_income > 0:
                fcf = operating_cash_flow - capex
                if fcf > net_income:
                    factors['fcf_quality'] = 3
                elif fcf > 0:
                    factors['fcf_quality'] = 2
                else:
                    factors['fcf_quality'] = 1
            else:
                factors['fcf_quality'] = 0
        except:
            factors['fcf_quality'] = 0
        
        # 7. 动态安全边际 (dynamic_safety_margin)
        try:
            if prev_data is not None:
                prev_revenue = prev_data.get('Revenue from Business Activities - Total', 0) or 0
                current_revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
                
                if prev_revenue > 0:
                    revenue_growth = (current_revenue - prev_revenue) / prev_revenue
                    if revenue_growth > 0.1:
                        factors['dynamic_safety_margin'] = 10
                    elif revenue_growth > 0.05:
                        factors['dynamic_safety_margin'] = 5
                    else:
                        factors['dynamic_safety_margin'] = 1
                else:
                    factors['dynamic_safety_margin'] = 1
            else:
                factors['dynamic_safety_margin'] = 10
        except:
            factors['dynamic_safety_margin'] = 10
        
        # 8. 收入增长连续性 (revenue_growth_continuity)
        try:
            if len(stock_data) >= 3:
                # 检查最近3年的收入增长
                recent_years = stock_data.tail(3)
                growth_positive = 0
                for i in range(1, len(recent_years)):
                    prev_rev = recent_years.iloc[i-1].get('Revenue from Business Activities - Total', 0) or 0
                    curr_rev = recent_years.iloc[i].get('Revenue from Business Activities - Total', 0) or 0
                    if prev_rev > 0 and curr_rev > prev_rev:
                        growth_positive += 1
                
                if growth_positive >= 2:
                    factors['revenue_growth_continuity'] = 1
                else:
                    factors['revenue_growth_continuity'] = 0
            else:
                factors['revenue_growth_continuity'] = 0
        except:
            factors['revenue_growth_continuity'] = 0
        
        # 9. 有效税率改善 (effective_tax_rate_improvement)
        try:
            if prev_data is not None:
                prev_tax = prev_data.get('Income Taxes', 0) or 0
                prev_income = prev_data.get('Income before Taxes', 0) or 0
                curr_tax = latest_data.get('Income Taxes', 0) or 0
                curr_income = latest_data.get('Income before Taxes', 0) or 0
                
                if prev_income > 0 and curr_income > 0:
                    prev_rate = prev_tax / prev_income
                    curr_rate = curr_tax / curr_income
                    if curr_rate < prev_rate:
                        factors['effective_tax_rate_improvement'] = 1
                    else:
                        factors['effective_tax_rate_improvement'] = 0
                else:
                    factors['effective_tax_rate_improvement'] = 0
            else:
                factors['effective_tax_rate_improvement'] = 0
        except:
            factors['effective_tax_rate_improvement'] = 0
        
        # 10. 财务健康 (financial_health)
        try:
            debt = latest_data.get('Debt - Total', 0) or 0
            equity = latest_data.get('Common Equity - Total', 0) or 0
            
            if equity > 0:
                debt_equity_ratio = debt / equity
                if debt_equity_ratio < 0.3:
                    factors['financial_health'] = 3
                elif debt_equity_ratio < 0.5:
                    factors['financial_health'] = 2
                else:
                    factors['financial_health'] = 1
            else:
                factors['financial_health'] = 0
        except:
            factors['financial_health'] = 0
        
        # 11. 估值泡沫信号 (valuation_bubble_signal)
        factors['valuation_bubble_signal'] = 0  # 简化处理
        
        # 12. 研发强度 (rd_intensity)
        try:
            rd_expense = latest_data.get('Research & Development Expense', 0) or 0
            rd_supplemental = latest_data.get('Research & Development Expense - Supplemental', 0) or 0
            total_rd = rd_expense + rd_supplemental
            revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
            
            if revenue > 0:
                factors['rd_intensity'] = (total_rd / revenue) * 100
            else:
                factors['rd_intensity'] = None
        except:
            factors['rd_intensity'] = None
        
        # 13. ROCE稳定性 (roce_stability)
        try:
            if len(stock_data) >= 3:
                recent_roce = []
                for i in range(len(stock_data)-2, len(stock_data)):
                    year_data = stock_data.iloc[i]
                    ebit = year_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
                    equity = year_data.get('Common Equity - Total', 0) or 0
                    if equity > 0:
                        roce = ebit / equity
                        recent_roce.append(roce)
                
                if len(recent_roce) >= 2:
                    roce_std = np.std(recent_roce)
                    factors['roce_stability'] = roce_std
                else:
                    factors['roce_stability'] = None
            else:
                factors['roce_stability'] = None
        except:
            factors['roce_stability'] = None
        
        # 14. 营收同比增长 (revenue_yoy)
        try:
            if prev_data is not None:
                prev_revenue = prev_data.get('Revenue from Business Activities - Total', 0) or 0
                current_revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
                
                if prev_revenue > 0:
                    factors['revenue_yoy'] = ((current_revenue - prev_revenue) / prev_revenue) * 100
                else:
                    factors['revenue_yoy'] = None
            else:
                factors['revenue_yoy'] = None
        except:
            factors['revenue_yoy'] = None
        
        # 15. 营收复合增长率 (revenue_cagr)
        try:
            if len(stock_data) >= 8:  # 需要至少8年数据
                current_revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
                past_revenue = stock_data.iloc[-8].get('Revenue from Business Activities - Total', 0) or 0
                
                if past_revenue > 0 and current_revenue > 0:
                    years = 8
                    cagr = ((current_revenue / past_revenue) ** (1/years) - 1) * 100
                    factors['revenue_cagr'] = cagr
                else:
                    factors['revenue_cagr'] = None
            else:
                factors['revenue_cagr'] = None
        except:
            factors['revenue_cagr'] = None
        
        # 16. 净利润同比增长 (net_income_yoy)
        try:
            if prev_data is not None:
                prev_net_income = prev_data.get('Net Income - Basic - including Extraordinary Items Applicable to Common - Total', 0) or 0
                current_net_income = latest_data.get('Net Income - Basic - including Extraordinary Items Applicable to Common - Total', 0) or 0
                
                if prev_net_income > 0:
                    factors['net_income_yoy'] = ((current_net_income - prev_net_income) / prev_net_income) * 100
                else:
                    factors['net_income_yoy'] = None
            else:
                factors['net_income_yoy'] = None
        except:
            factors['net_income_yoy'] = None
        
        # 17. 利润率 (profit_revenue_ratio)
        try:
            net_income = latest_data.get('Net Income - Basic - including Extraordinary Items Applicable to Common - Total', 0) or 0
            revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
            
            if revenue > 0:
                factors['profit_revenue_ratio'] = (net_income / revenue) * 100
            else:
                factors['profit_revenue_ratio'] = None
        except:
            factors['profit_revenue_ratio'] = None
        
        # 18. 每股自由现金流 (fcf_per_share)
        try:
            operating_cash_flow = latest_data.get('Net Cash Flow from Operating Activities', 0) or 0
            capex = abs(latest_data.get('Capital Expenditures - Total', 0) or 0)
            shares = latest_data.get('Common Shares - Outstanding - Total', 0) or 0
            
            if shares > 0:
                fcf = operating_cash_flow - capex
                factors['fcf_per_share'] = fcf / shares
            else:
                factors['fcf_per_share'] = None
        except:
            factors['fcf_per_share'] = None
        
        # 19. 自由现金流复合增长率 (fcf_cagr)
        try:
            if len(stock_data) >= 8:
                current_fcf = 0
                past_fcf = 0
                
                # 计算当前FCF
                curr_operating_cash = latest_data.get('Net Cash Flow from Operating Activities', 0) or 0
                curr_capex = abs(latest_data.get('Capital Expenditures - Total', 0) or 0)
                current_fcf = curr_operating_cash - curr_capex
                
                # 计算8年前的FCF
                past_data = stock_data.iloc[-8]
                past_operating_cash = past_data.get('Net Cash Flow from Operating Activities', 0) or 0
                past_capex = abs(past_data.get('Capital Expenditures - Total', 0) or 0)
                past_fcf = past_operating_cash - past_capex
                
                if past_fcf > 0 and current_fcf > 0:
                    years = 8
                    cagr = ((current_fcf / past_fcf) ** (1/years) - 1) * 100
                    factors['fcf_cagr'] = cagr
                else:
                    factors['fcf_cagr'] = None
            else:
                factors['fcf_cagr'] = None
        except:
            factors['fcf_cagr'] = None
        
        # 20. 自由现金流净利润比率 (fcf_net_income_ratio)
        try:
            operating_cash_flow = latest_data.get('Net Cash Flow from Operating Activities', 0) or 0
            capex = abs(latest_data.get('Capital Expenditures - Total', 0) or 0)
            net_income = latest_data.get('Net Income - Basic - including Extraordinary Items Applicable to Common - Total', 0) or 0
            
            if net_income > 0:
                fcf = operating_cash_flow - capex
                factors['fcf_net_income_ratio'] = (fcf / net_income) * 100
            else:
                factors['fcf_net_income_ratio'] = None
        except:
            factors['fcf_net_income_ratio'] = None
        
        # 21. 营业利润率 (operating_margin)
        try:
            ebit = latest_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
            revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
            
            if revenue > 0:
                factors['operating_margin'] = (ebit / revenue) * 100
            else:
                factors['operating_margin'] = None
        except:
            factors['operating_margin'] = None
        
        # 22. 营业利润率标准差 (operating_margin_std)
        try:
            if len(stock_data) >= 3:
                margins = []
                for i in range(len(stock_data)-2, len(stock_data)):
                    year_data = stock_data.iloc[i]
                    ebit = year_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
                    revenue = year_data.get('Revenue from Business Activities - Total', 0) or 0
                    if revenue > 0:
                        margin = (ebit / revenue) * 100
                        margins.append(margin)
                
                if len(margins) >= 2:
                    factors['operating_margin_std'] = np.std(margins)
                else:
                    factors['operating_margin_std'] = None
            else:
                factors['operating_margin_std'] = None
        except:
            factors['operating_margin_std'] = None
        
        # 23. ROIC (roic)
        try:
            ebit = latest_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
            tax_expense = latest_data.get('Income Taxes', 0) or 0
            equity = latest_data.get('Common Equity - Total', 0) or 0
            debt = latest_data.get('Debt - Total', 0) or 0
            
            if equity > 0:
                # 使用EBIT作为税前利润的替代
                pretax_income = ebit
                effective_tax_rate = tax_expense / pretax_income if tax_expense > 0 and pretax_income > 0 else 0.25
                nopat = ebit * (1 - effective_tax_rate)
                invested_capital = equity + debt
                
                if invested_capital > 0:
                    factors['roic'] = (nopat / invested_capital) * 100
                else:
                    factors['roic'] = None
            else:
                factors['roic'] = None
        except:
            factors['roic'] = None
        
        # 24. ROIC复合增长率 (roic_cagr)
        try:
            if len(stock_data) >= 8:
                current_roic = factors.get('roic', 0) or 0
                past_roic = 0
                
                # 计算8年前的ROIC
                past_data = stock_data.iloc[-8]
                past_ebit = past_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
                past_tax = past_data.get('Income Taxes', 0) or 0
                past_equity = past_data.get('Common Equity - Total', 0) or 0
                past_debt = past_data.get('Debt - Total', 0) or 0
                
                if past_equity > 0:
                    past_pretax = past_ebit
                    past_effective_tax_rate = past_tax / past_pretax if past_tax > 0 and past_pretax > 0 else 0.25
                    past_nopat = past_ebit * (1 - past_effective_tax_rate)
                    past_invested_capital = past_equity + past_debt
                    
                    if past_invested_capital > 0:
                        past_roic = (past_nopat / past_invested_capital) * 100
                
                if past_roic > 0 and current_roic > 0:
                    years = 8
                    cagr = ((current_roic / past_roic) ** (1/years) - 1) * 100
                    factors['roic_cagr'] = cagr
                else:
                    factors['roic_cagr'] = None
            else:
                factors['roic_cagr'] = None
        except:
            factors['roic_cagr'] = None
        
        # 25. 有效税率 (effective_tax_rate)
        try:
            tax_expense = latest_data.get('Income Taxes', 0) or 0
            ebit = latest_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
            
            if ebit > 0:
                factors['effective_tax_rate'] = (tax_expense / ebit) * 100
            else:
                factors['effective_tax_rate'] = None
        except:
            factors['effective_tax_rate'] = None
        
        # 26. 有效税率标准差 (effective_tax_rate_std)
        try:
            if len(stock_data) >= 3:
                tax_rates = []
                for i in range(len(stock_data)-2, len(stock_data)):
                    year_data = stock_data.iloc[i]
                    tax_expense = year_data.get('Income Taxes', 0) or 0
                    ebit = year_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
                    if ebit > 0:
                        tax_rate = (tax_expense / ebit) * 100
                        tax_rates.append(tax_rate)
                
                if len(tax_rates) >= 2:
                    factors['effective_tax_rate_std'] = np.std(tax_rates)
                else:
                    factors['effective_tax_rate_std'] = None
            else:
                factors['effective_tax_rate_std'] = None
        except:
            factors['effective_tax_rate_std'] = None
            
    except Exception as e:
        print(f"计算因子时出错: {e}")
    
    return factors

def analyze_factor_coverage(df_factors):
    """分析因子覆盖率"""
    print("\n7. 分析因子覆盖率...")
    
    total_stocks = len(df_factors)
    coverage_stats = {}
    
    # 第一组13个因子
    factor_columns_group1 = [
        'tech_premium', 'tech_gap_warning', 'patent_density', 'ecosystem_cash_ratio',
        'adjusted_roce', 'fcf_quality', 'dynamic_safety_margin', 'revenue_growth_continuity',
        'effective_tax_rate_improvement', 'financial_health', 'valuation_bubble_signal',
        'rd_intensity', 'roce_stability'
    ]
    
    # 第二组13个因子
    factor_columns_group2 = [
        'revenue_yoy', 'revenue_cagr', 'net_income_yoy', 'profit_revenue_ratio',
        'fcf_per_share', 'fcf_cagr', 'fcf_net_income_ratio',
        'operating_margin', 'operating_margin_std',
        'roic', 'roic_cagr',
        'effective_tax_rate', 'effective_tax_rate_std'
    ]
    
    # 合并所有因子
    all_factor_columns = factor_columns_group1 + factor_columns_group2
    
    print(f"\n=== 因子覆盖率分析（总共{len(all_factor_columns)}个因子）===")
    print(f"总股票数: {total_stocks:,}")
    
    for factor in all_factor_columns:
        if factor in df_factors.columns:
            coverage = df_factors[factor].notna().sum()
            coverage_pct = (coverage / total_stocks) * 100
            coverage_stats[factor] = {
                'coverage': coverage,
                'coverage_pct': coverage_pct
            }
            print(f"  {factor}: {coverage} 只股票 ({coverage_pct:.1f}%)")
        else:
            print(f"  {factor}: 未找到该因子列")
    
    return coverage_stats

def save_results(df_factors, coverage_stats):
    """保存结果"""
    print("\n8. 保存结果...")
    
    # 保存因子结果
    output_file = 'mclean_factors_817_stocks_complete_results.csv'
    df_factors.to_csv(output_file, index=False, encoding='utf-8')
    print(f"  因子结果已保存到: {output_file}")
    
    # 保存覆盖率报告
    report_file = 'mclean_factors_817_stocks_complete_report.txt'
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("=== Colin McLean成长价值优势投资法则因子实现（817个股票完整版）报告 ===\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        total_stocks = len(df_factors)
        f.write(f"总股票数: {total_stocks:,}\n")
        f.write(f"总因子数: 26个\n\n")
        
        f.write("=== 因子覆盖率详情 ===\n")
        for factor, stats in coverage_stats.items():
            f.write(f"{factor}: {stats['coverage']} 只股票 ({stats['coverage_pct']:.1f}%)\n")
        
        f.write(f"\n=== 数据质量统计 ===\n")
        f.write(f"有完整数据的股票数: {df_factors.dropna().shape[0]:,}\n")
        f.write(f"数据完整性: {(df_factors.dropna().shape[0] / total_stocks * 100):.1f}%\n")
    
    print(f"  覆盖率报告已保存到: {report_file}")

def main():
    """主函数"""
    start_time = time.time()
    
    try:
        # 1. 获取817个股票列表
        stocks = get_817_stocks()
        if not stocks:
            print("错误：无法获取股票列表")
            return
        
        # 2. 获取财务数据（只获取817个股票的数据）
        df_income = get_income_statement_data(stocks)
        df_balance = get_balance_sheet_data(stocks)
        df_cash_flow = get_cash_flow_data(stocks)
        
        # 3. 合并数据
        df_merged = merge_all_data(df_income, df_balance, df_cash_flow)
        
        # 4. 计算因子
        df_factors = calculate_all_26_factors(df_merged)
        
        # 5. 分析覆盖率
        coverage_stats = analyze_factor_coverage(df_factors)
        
        # 6. 保存结果
        save_results(df_factors, coverage_stats)
        
        end_time = time.time()
        print(f"\n=== 执行完成 ===")
        print(f"总耗时: {end_time - start_time:.2f} 秒")
        print(f"成功计算了 {len(df_factors)} 只股票的26个因子")
        
    except Exception as e:
        print(f"执行过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 