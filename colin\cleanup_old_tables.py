from clickhouse_driver import Client

client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')

print("=== 清理旧版本表 ===")

# 要删除的旧表
old_tables = [
    'quarterly_data_filled',  # 已被quarterly_data_filled_enhanced替换
    'long_time_fundamental_na_list'  # 已被long_time_fundamental_na_list_detailed替换
]

print("检查要删除的表...")
existing_tables = [t[0] for t in client.execute("SHOW TABLES")]

for table_name in old_tables:
    if table_name in existing_tables:
        print(f"\n🗑️ 删除旧表: {table_name}")
        try:
            # 先查看表的记录数
            count_result = client.execute(f"SELECT COUNT(*) FROM {table_name}")
            record_count = count_result[0][0] if count_result else 0
            print(f"   📊 表中有 {record_count} 条记录")
            
            # 删除表
            client.execute(f"DROP TABLE {table_name}")
            print(f"   ✅ {table_name} 删除成功")
        except Exception as e:
            print(f"   ❌ 删除 {table_name} 失败: {e}")
    else:
        print(f"⚠️ 表 {table_name} 不存在，跳过")

# 验证删除结果
print("\n🔍 验证删除结果...")
final_tables = [t[0] for t in client.execute("SHOW TABLES")]
print(f"数据库现有 {len(final_tables)} 个表:")
for table in sorted(final_tables):
    print(f"  • {table}")

print("\n📊 清理统计:")
print(f"  🗑️ 删除的表: {len([t for t in old_tables if t not in final_tables])} 个")
print(f"  📋 剩余的表: {len(final_tables)} 个")

print("\n✅ 旧版本表清理完成！")

