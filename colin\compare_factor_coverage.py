import pandas as pd
import numpy as np
from datetime import datetime

def compare_factor_coverage():
    """对比改进前后的因子覆盖率"""
    
    print("=== 对比改进前后的因子覆盖率 ===")
    print()
    
    # 1. 读取改进前的结果（最新的覆盖率文件）
    try:
        old_coverage_file = "colin/factor_coverage_20250810_004921.csv"
        old_coverage = pd.read_csv(old_coverage_file)
        print(f"✅ 读取改进前覆盖率数据: {old_coverage_file}")
    except:
        print("❌ 无法读取改进前的覆盖率文件")
        return
    
    # 2. 读取改进后的结果
    try:
        new_factors_file = "colin/complete_improved_24_factors_20250810_022631.csv"
        new_factors = pd.read_csv(new_factors_file)
        print(f"✅ 读取改进后因子数据: {new_factors_file}")
    except:
        print("❌ 无法读取改进后的因子文件")
        return
    
    # 3. 计算改进后的覆盖率
    print(f"\n📊 数据规模对比:")
    print(f"   改进前: {len(old_coverage)} 个因子，{old_coverage['Total_Stocks'][0] if 'Total_Stocks' in old_coverage.columns else '未知'} 只股票")
    print(f"   改进后: {len(new_factors.columns)-1} 个因子，{len(new_factors)} 只股票")
    
    # 计算改进后的覆盖率
    new_coverage_data = []
    for col in new_factors.columns:
        if col != 'stock_symbol':
            valid_count = new_factors[col].notna().sum()
            total_count = len(new_factors)
            coverage_rate = (valid_count / total_count) * 100
            
            new_coverage_data.append({
                'Factor': col,
                'Valid_Count': valid_count,
                'Total_Count': total_count,
                'Coverage_Rate': coverage_rate
            })
    
    new_coverage_df = pd.DataFrame(new_coverage_data)
    
    # 4. 合并对比数据
    print(f"\n📈 详细覆盖率对比:")
    print("-" * 90)
    print(f"{'因子名称':<30} {'改进前覆盖率':<15} {'改进后覆盖率':<15} {'变化':<15} {'状态'}")
    print("-" * 90)
    
    # 因子名称映射（处理可能的命名差异）
    factor_mapping = {
        'tech_premium': 'tech_premium',
        'tech_gap_warning': 'tech_gap_warning', 
        'patent_density': 'patent_density',
        'ecosystem_cash_ratio': 'ecosystem_cash_ratio',
        'adjusted_roce': 'adjusted_roe',  # 注意这里改名了
        'fcf_quality': 'fcf_quality',
        'dynamic_safety_margin': 'dynamic_safety_margin',
        'revenue_growth_continuity': 'revenue_growth_continuity',
        'effective_tax_rate_improvement': 'effective_tax_rate_improvement',
        'financial_health': 'financial_health',
        'valuation_bubble_signal': 'valuation_bubble_signal',
        'roce_stability': 'roe_stability',  # 注意这里改名了
        'revenue_yoy': 'revenue_yoy',
        'revenue_cagr': 'revenue_cagr',
        'net_income_yoy': 'net_income_yoy',
        'profit_revenue_ratio': 'profit_revenue_ratio',
        'fcf_per_share': 'fcf_per_share',
        'fcf_cagr': 'fcf_cagr',
        'operating_margin': 'operating_margin',
        'operating_margin_std': 'operating_margin_std',
        'roic': 'roic',
        'roic_cagr': 'roic_cagr',
        'effective_tax_rate': 'effective_tax_rate',
        'effective_tax_rate_std': 'effective_tax_rate_std'
    }
    
    improvements = []
    
    for old_factor in old_coverage['Factor']:
        new_factor = factor_mapping.get(old_factor, old_factor)
        
        # 获取改进前覆盖率
        old_rate = old_coverage[old_coverage['Factor'] == old_factor]['Coverage_Rate'].iloc[0]
        
        # 获取改进后覆盖率
        new_row = new_coverage_df[new_coverage_df['Factor'] == new_factor]
        if len(new_row) > 0:
            new_rate = new_row['Coverage_Rate'].iloc[0]
            change = new_rate - old_rate
            
            if change > 5:
                status = "🚀 显著提升"
            elif change > 0:
                status = "✅ 小幅提升"
            elif change > -5:
                status = "➖ 基本持平"
            else:
                status = "⚠️  有所下降"
            
            print(f"{old_factor:<30} {old_rate:>13.1f}% {new_rate:>13.1f}% {change:>+13.1f}% {status}")
            
            improvements.append({
                'factor': old_factor,
                'old_rate': old_rate,
                'new_rate': new_rate,
                'change': change
            })
        else:
            print(f"{old_factor:<30} {old_rate:>13.1f}% {'未找到':>13} {'N/A':>13} ❌ 未找到")
    
    print("-" * 90)
    
    # 5. 统计总结
    if improvements:
        avg_old_rate = np.mean([x['old_rate'] for x in improvements])
        avg_new_rate = np.mean([x['new_rate'] for x in improvements])
        avg_change = avg_new_rate - avg_old_rate
        
        improved_count = len([x for x in improvements if x['change'] > 0])
        declined_count = len([x for x in improvements if x['change'] < -1])
        stable_count = len(improvements) - improved_count - declined_count
        
        print(f"\n📊 总体改进效果:")
        print(f"   平均覆盖率: {avg_old_rate:.1f}% → {avg_new_rate:.1f}% (变化: {avg_change:+.1f}%)")
        print(f"   因子表现: {improved_count}个提升，{stable_count}个稳定，{declined_count}个下降")
        print()
        
        # 显著改进的因子
        significant_improvements = [x for x in improvements if x['change'] > 10]
        if significant_improvements:
            print(f"🚀 显著改进的因子 (提升>10%):")
            for imp in significant_improvements:
                print(f"   • {imp['factor']}: {imp['old_rate']:.1f}% → {imp['new_rate']:.1f}% (+{imp['change']:.1f}%)")
            print()
        
        # 需要关注的因子
        concerning_factors = [x for x in improvements if x['change'] < -5]
        if concerning_factors:
            print(f"⚠️  需要关注的因子 (下降>5%):")
            for imp in concerning_factors:
                print(f"   • {imp['factor']}: {imp['old_rate']:.1f}% → {imp['new_rate']:.1f}% ({imp['change']:.1f}%)")
            print()
        
        # 完美覆盖的因子
        perfect_factors = [x for x in improvements if x['new_rate'] >= 95]
        if perfect_factors:
            print(f"✨ 优秀覆盖的因子 (≥95%):")
            for imp in perfect_factors:
                print(f"   • {imp['factor']}: {imp['new_rate']:.1f}%")
            print()
    
    # 6. 保存对比结果
    comparison_data = []
    for imp in improvements:
        comparison_data.append({
            'Factor': imp['factor'],
            'Old_Coverage': imp['old_rate'],
            'New_Coverage': imp['new_rate'],
            'Change': imp['change'],
            'Status': '提升' if imp['change'] > 0 else ('稳定' if imp['change'] > -1 else '下降')
        })
    
    if comparison_data:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        comparison_df = pd.DataFrame(comparison_data)
        filename = f"colin/coverage_comparison_{timestamp}.csv"
        comparison_df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"✅ 对比结果已保存到: {filename}")

if __name__ == "__main__":
    compare_factor_coverage()

