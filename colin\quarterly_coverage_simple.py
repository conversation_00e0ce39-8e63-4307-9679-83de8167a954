from clickhouse_driver import Client

# 连接ClickHouse数据库
client = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 季报数据覆盖情况分析（简化版）===")

# 1. 总体季报统计
print("\n1. 总体季报统计...")
query_quarterly_total = """
SELECT 
    COUNT(*) as total_quarterly_records,
    COUNT(DISTINCT stock_symbol) as stocks_with_quarterly
FROM priority_quality_fundamental_data_complete_deduped
WHERE period_end_date IS NOT NULL AND period_end_date != ''
  AND financial_period_absolute REGEXP 'FY[0-9]{4}Q[1-4]'
"""

result_total = client.execute(query_quarterly_total)
total_records, stocks_with_quarterly = result_total[0]
print(f"  季报数据记录数: {total_records:,}")
print(f"  有季报数据的股票数: {stocks_with_quarterly:,}")

# 2. 按季度统计
print("\n2. 按季度统计...")
for quarter in ['Q1', 'Q2', 'Q3', 'Q4']:
    query_quarter = f"""
    SELECT 
        COUNT(*) as records,
        COUNT(DISTINCT stock_symbol) as stocks
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE period_end_date IS NOT NULL AND period_end_date != ''
      AND financial_period_absolute LIKE '%{quarter}'
    """
    
    result_quarter = client.execute(query_quarter)
    records, stocks = result_quarter[0]
    print(f"  {quarter}: {records:,}条记录, {stocks:,}只股票")

# 3. 按年份统计（最近10年）
print("\n3. 最近10年季报覆盖情况...")
for year in range(2024, 2014, -1):
    query_year = f"""
    SELECT 
        COUNT(*) as records,
        COUNT(DISTINCT stock_symbol) as stocks
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE period_end_date IS NOT NULL AND period_end_date != ''
      AND financial_period_absolute LIKE 'FY{year}Q%'
    """
    
    result_year = client.execute(query_year)
    records, stocks = result_year[0]
    print(f"  {year}年: {records:,}条记录, {stocks:,}只股票")

# 4. 分析每只股票每年的季报完整性（简化版）
print("\n4. 分析季报完整性（2023年样本）...")
query_2023_completeness = """
SELECT 
    stock_symbol,
    COUNT(DISTINCT financial_period_absolute) as quarters_2023
FROM priority_quality_fundamental_data_complete_deduped
WHERE period_end_date IS NOT NULL AND period_end_date != ''
  AND financial_period_absolute LIKE 'FY2023Q%'
GROUP BY stock_symbol
ORDER BY quarters_2023, stock_symbol
LIMIT 20
"""

result_2023 = client.execute(query_2023_completeness)
if result_2023:
    print("  2023年季报完整性样本（前20只股票）:")
    print("  股票代码    季报数量")
    print("  " + "-" * 25)
    for stock, quarters in result_2023:
        print(f"  {stock:10s}  {quarters:8d}")

# 5. 统计季报完整性分布
print("\n5. 季报完整性分布统计（2023年）...")
query_completeness_dist = """
SELECT 
    quarters_count,
    COUNT(*) as stock_count
FROM (
    SELECT 
        stock_symbol,
        COUNT(DISTINCT financial_period_absolute) as quarters_count
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE period_end_date IS NOT NULL AND period_end_date != ''
      AND financial_period_absolute LIKE 'FY2023Q%'
    GROUP BY stock_symbol
) quarter_stats
GROUP BY quarters_count
ORDER BY quarters_count
"""

result_dist = client.execute(query_completeness_dist)
if result_dist:
    print("  2023年季报完整性分布:")
    total_stocks_2023 = sum(count for _, count in result_dist)
    for quarters, count in result_dist:
        percentage = count / total_stocks_2023 * 100
        completeness = "完整" if quarters == 4 else "不完整"
        print(f"    {quarters}个季度: {count:,}只股票 ({percentage:.1f}%) - {completeness}")
    
    complete_stocks = next((count for quarters, count in result_dist if quarters == 4), 0)
    complete_rate = complete_stocks / total_stocks_2023 * 100 if total_stocks_2023 > 0 else 0
    print(f"  2023年季报完整率: {complete_rate:.1f}%")

print("\n=== 分析完成 ===")

