from datetime import datetime, date

print("=== 科目状态：renamed 详细说明 ===")

def explain_renamed_concept():
    """
    解释renamed科目的概念
    """
    print("\n1. renamed科目状态的定义...")
    
    concept = {
        'definition': '科目实质相同但名称发生变更',
        'core_principle': '同一个财务概念，但在不同时期使用了不同的名称',
        'detection_method': '通过语义分析和数值相关性检测',
        'processing_strategy': '建立映射关系，将历史名称映射到当前名称'
    }
    
    print(f"📋 定义: {concept['definition']}")
    print(f"📋 核心原则: {concept['core_principle']}")
    print(f"📋 检测方法: {concept['detection_method']}")
    print(f"📋 处理策略: {concept['processing_strategy']}")
    
    return concept

def provide_real_world_examples():
    """
    提供真实世界的科目重命名示例
    """
    print(f"\n2. 真实的科目重命名示例...")
    
    examples = {
        'research_development': {
            'historical_name': 'Research and Development',
            'current_name': 'R&D Expenses',
            'reason': '科目名称标准化',
            'semantic_similarity': 0.85,
            'value_correlation': 0.92,
            'confidence': 'high',
            'explanation': '研发费用的不同表述方式，实质内容完全相同'
        },
        
        'depreciation_amortization': {
            'historical_name': 'Depreciation and Amortization',
            'current_name': 'D&A',
            'reason': '科目名称简化',
            'semantic_similarity': 0.90,
            'value_correlation': 0.95,
            'confidence': 'very_high',
            'explanation': '折旧摊销的缩写形式，内容完全一致'
        },
        
        'operating_income': {
            'historical_name': 'Operating Income',
            'current_name': 'Income from Operations',
            'reason': '会计准则用词调整',
            'semantic_similarity': 0.88,
            'value_correlation': 0.98,
            'confidence': 'very_high',
            'explanation': '营业收入的不同表述，实质内容相同'
        },
        
        'interest_expense': {
            'historical_name': 'Interest Expense',
            'current_name': 'Finance Costs',
            'reason': '国际会计准则统一',
            'semantic_similarity': 0.75,
            'value_correlation': 0.89,
            'confidence': 'medium_high',
            'explanation': '利息费用向融资成本的术语转换'
        },
        
        'total_assets': {
            'historical_name': 'Total Assets',
            'current_name': 'Total Assets and Liabilities',
            'reason': '资产负债表格式调整',
            'semantic_similarity': 0.80,
            'value_correlation': 0.85,
            'confidence': 'medium',
            'explanation': '资产负债表标题的扩展表述'
        }
    }
    
    for example_key, example_data in examples.items():
        print(f"\n  📊 {example_key.upper()}:")
        print(f"     历史名称: '{example_data['historical_name']}'")
        print(f"     当前名称: '{example_data['current_name']}'")
        print(f"     变更原因: {example_data['reason']}")
        print(f"     语义相似度: {example_data['semantic_similarity']}")
        print(f"     数值相关性: {example_data['value_correlation']}")
        print(f"     置信度: {example_data['confidence']}")
        print(f"     说明: {example_data['explanation']}")
    
    return examples

def explain_detection_algorithm():
    """
    解释重命名检测算法
    """
    print(f"\n3. 重命名检测算法...")
    
    algorithm = {
        'step_1': {
            'name': '候选识别',
            'description': '找出历史专有科目和当前专有科目',
            'sql': '''
            WITH historical_only AS (
                SELECT item_name, statement_type, AVG(value) as avg_value
                FROM historical_data 
                WHERE stock_symbol = 'STOCK_A'
                GROUP BY item_name, statement_type
            ),
            current_only AS (
                SELECT item_name, statement_type, AVG(value) as avg_value
                FROM current_data
                WHERE stock_symbol = 'STOCK_A' 
                GROUP BY item_name, statement_type
            )
            SELECT h.item_name as historical_item, c.item_name as current_item
            FROM historical_only h, current_only c
            WHERE h.statement_type = c.statement_type
            '''
        },
        
        'step_2': {
            'name': '语义相似度计算',
            'description': '计算科目名称的语义相似度',
            'methods': [
                'Levenshtein距离（编辑距离）',
                '词汇重叠度分析',
                '同义词检测',
                '缩写展开匹配'
            ],
            'threshold': 0.8,
            'example': "calculate_semantic_similarity('Research and Development', 'R&D Expenses') = 0.85"
        },
        
        'step_3': {
            'name': '数值相关性分析',
            'description': '分析历史和当前科目的数值相关性',
            'methods': [
                'Pearson相关系数',
                '数值范围对比',
                '变化趋势一致性',
                '比例关系分析'
            ],
            'threshold': 0.9,
            'example': "calculate_value_correlation(historical_values, current_values) = 0.92"
        },
        
        'step_4': {
            'name': '综合判断',
            'description': '结合语义和数值分析做最终判断',
            'criteria': [
                '语义相似度 > 0.8 OR 数值相关性 > 0.9',
                '且两者都不能太低（避免误判）',
                '考虑业务逻辑合理性',
                '人工验证高置信度结果'
            ],
            'confidence_calculation': 'max(semantic_score, correlation_score)'
        }
    }
    
    for step_key, step_info in algorithm.items():
        print(f"\n  🔍 {step_info['name']}:")
        print(f"     描述: {step_info['description']}")
        
        if 'sql' in step_info:
            print(f"     SQL示例:")
            print(f"     {step_info['sql'].strip()}")
        
        if 'methods' in step_info:
            print(f"     方法:")
            for method in step_info['methods']:
                print(f"       • {method}")
        
        if 'threshold' in step_info:
            print(f"     阈值: {step_info['threshold']}")
        
        if 'example' in step_info:
            print(f"     示例: {step_info['example']}")
        
        if 'criteria' in step_info:
            print(f"     判断标准:")
            for criterion in step_info['criteria']:
                print(f"       • {criterion}")
    
    return algorithm

def explain_renamed_processing():
    """
    解释renamed科目的处理流程
    """
    print(f"\n4. renamed科目的处理流程...")
    
    processing_flow = {
        'estimation_phase': {
            'description': '估算阶段处理',
            'steps': [
                '使用历史科目名称查找历史数据',
                '基于历史数据进行估算',
                '标记item_mapping为当前科目名称',
                '记录重命名检测的置信度'
            ],
            'example': {
                'target': 'MRP FY2023Q2 R&D Expenses',
                'action': '使用历史"Research and Development"数据估算',
                'mapping': 'Research and Development → R&D Expenses',
                'notes': '基于历史"Research and Development"Q2数据进行估算'
            }
        },
        
        'correction_phase': {
            'description': '修正阶段处理',
            'steps': [
                '使用当前科目名称查找最新报告数据',
                '基于最新数据进行修正',
                '更新corrected_effective_date',
                '记录映射关系和检测信息'
            ],
            'example': {
                'target': 'MRP FY2023Q2 R&D Expenses',
                'action': '使用最新"R&D Expenses"数据修正',
                'source': 'FY2023年报中的R&D Expenses科目',
                'notes': '已使用最新"R&D Expenses"科目数据修正，检测到科目重命名（语义相似度0.85，数值相关性0.92）'
            }
        }
    }
    
    for phase_name, phase_info in processing_flow.items():
        print(f"\n  🔄 {phase_info['description']}:")
        print(f"     处理步骤:")
        for step in phase_info['steps']:
            print(f"       {step}")
        
        print(f"     示例:")
        example = phase_info['example']
        for key, value in example.items():
            print(f"       {key}: {value}")
    
    return processing_flow

def compare_with_other_statuses():
    """
    与其他科目状态的对比
    """
    print(f"\n5. 与其他科目状态的对比...")
    
    status_comparison = {
        'active': {
            'historical_exists': True,
            'current_exists': True,
            'relationship': '完全一致',
            'processing': '估算 + 修正',
            'mapping_needed': False
        },
        
        'historical_only': {
            'historical_exists': True,
            'current_exists': False,
            'relationship': '已消失',
            'processing': '只估算，不修正',
            'mapping_needed': False
        },
        
        'current_only': {
            'historical_exists': False,
            'current_exists': True,
            'relationship': '新出现',
            'processing': '无法估算',
            'mapping_needed': False
        },
        
        'renamed': {
            'historical_exists': True,
            'current_exists': True,
            'relationship': '名称变更，实质相同',
            'processing': '估算 + 修正（通过映射）',
            'mapping_needed': True
        }
    }
    
    print(f"科目状态对比表:")
    print(f"{'状态':<15} {'历史存在':<8} {'当前存在':<8} {'关系':<15} {'处理方式':<20} {'需要映射'}")
    print(f"{'-'*80}")
    
    for status_name, status_info in status_comparison.items():
        print(f"{status_name:<15} {str(status_info['historical_exists']):<8} {str(status_info['current_exists']):<8} {status_info['relationship']:<15} {status_info['processing']:<20} {str(status_info['mapping_needed'])}")
    
    return status_comparison

def main():
    """
    主函数
    """
    print("开始详细解释renamed科目状态...")
    
    try:
        # 1. 基本概念
        concept = explain_renamed_concept()
        
        # 2. 真实示例
        examples = provide_real_world_examples()
        
        # 3. 检测算法
        algorithm = explain_detection_algorithm()
        
        # 4. 处理流程
        processing = explain_renamed_processing()
        
        # 5. 状态对比
        comparison = compare_with_other_statuses()
        
        print(f"\n=== RENAMED状态总结 ===")
        print(f"🎯 核心概念: 同一财务概念的不同名称表述")
        print(f"🔍 检测方法: 语义相似度(>0.8) + 数值相关性(>0.9)")
        print(f"🔗 关键特征: 需要建立历史名称→当前名称的映射")
        print(f"⚙️ 处理方式: 估算用历史名称，修正用当前名称")
        print(f"📝 特殊标注: 记录映射关系和检测置信度")
        
        print(f"\n=== 与其他状态的区别 ===")
        print(f"• active: 名称完全一致")
        print(f"• historical_only: 科目已消失")
        print(f"• current_only: 科目新出现")
        print(f"• renamed: 科目重命名但实质相同 ← 这就是renamed！")
        
    except Exception as e:
        print(f"❌ 解释过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

