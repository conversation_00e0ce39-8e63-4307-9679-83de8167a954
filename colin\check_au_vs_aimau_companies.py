from clickhouse_driver import Client

# 连接到lseg数据库
client_lseg = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='lseg'
)

# 连接到ap_research数据库
client_ap = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 检查AU.NB和AIMAU.NB是否为同一家公司 ===")

# 1. 检查ap_research中AU的公司信息
print("\n1. 检查ap_research中AU的基本信息...")

query_au_info = """
SELECT DISTINCT 
    stock_symbol,
    original_stock_code,
    instrument,
    turnover_type
FROM priority_quality_fundamental_data_complete_deduped
WHERE stock_symbol = 'AU'
"""

result_au_info = client_ap.execute(query_au_info)
if result_au_info:
    for symbol, orig_code, instrument, turnover in result_au_info:
        print(f"  AU在ap_research中:")
        print(f"    stock_symbol: {symbol}")
        print(f"    original_stock_code: {orig_code}")
        print(f"    instrument: {instrument}")
        print(f"    turnover_type: {turnover}")

# 2. 在lseg中查找这两个instrument的详细信息
print(f"\n2. 在lseg中查找详细的公司信息...")

instruments_to_check = ['AU.NB', 'AIMAU.NB']
tables = ['income_statement', 'balance_sheet_history', 'cash_flow']

for instrument in instruments_to_check:
    print(f"\n  检查instrument: {instrument}")
    
    # 查看这个instrument的一些样本数据，看看有没有公司名称等信息
    for table in tables:
        query_sample = f"""
        SELECT *
        FROM {table}
        WHERE instrument = '{instrument}'
        ORDER BY financial_period_absolute DESC
        LIMIT 3
        """
        
        try:
            result_sample = client_lseg.execute(query_sample)
            if result_sample:
                print(f"    在{table}中的样本数据:")
                # 显示前几个字段来了解数据结构
                for i, row in enumerate(result_sample[:1]):  # 只显示第一行
                    print(f"      样本记录 {i+1}: {str(row)[:200]}...")
                break  # 只需要一个表的样本就够了
        except Exception as e:
            continue

# 3. 查看是否有其他AU相关的公司
print(f"\n3. 查找所有AU相关的instrument...")

query_au_related = """
SELECT DISTINCT instrument
FROM income_statement
WHERE instrument LIKE '%AU%'
  AND instrument LIKE '%.NB'
ORDER BY instrument
"""

try:
    result_au_related = client_lseg.execute(query_au_related)
    if result_au_related:
        print(f"  所有包含AU的instrument:")
        au_instruments = [instr for (instr,) in result_au_related]
        
        # 分类显示
        au_only = [instr for instr in au_instruments if instr.startswith('AU.') or instr == 'AU.NB']
        au_prefix = [instr for instr in au_instruments if instr.startswith('AU') and instr not in au_only]
        au_suffix = [instr for instr in au_instruments if 'AU' in instr and not instr.startswith('AU')]
        
        print(f"    以AU开头的主要股票:")
        for instr in au_only[:10]:  # 显示前10个
            print(f"      {instr}")
        
        print(f"    以AU开头的其他股票:")
        for instr in au_prefix[:10]:  # 显示前10个
            print(f"      {instr}")
        
        print(f"    包含AU的其他股票:")
        for instr in au_suffix[:10]:  # 显示前10个
            print(f"      {instr}")
            
except Exception as e:
    print(f"  查询出错: {e}")

# 4. 检查原始股票列表中AU的来源
print(f"\n4. 检查AU在原始股票列表中的信息...")

# 查看AU在我们原始数据中的完整信息
query_au_origin = """
SELECT DISTINCT 
    stock_symbol,
    original_stock_code,
    instrument,
    turnover_type,
    item_name,
    value
FROM priority_quality_fundamental_data_complete_deduped
WHERE stock_symbol = 'AU'
  AND item_name LIKE '%Company%' OR item_name LIKE '%Name%'
ORDER BY item_name
LIMIT 10
"""

try:
    result_au_origin = client_ap.execute(query_au_origin)
    if result_au_origin:
        print(f"  AU的公司相关信息:")
        for symbol, orig_code, instrument, turnover, item_name, value in result_au_origin:
            print(f"    {item_name}: {value}")
    else:
        print(f"  没有找到AU的公司名称信息")
except Exception as e:
    print(f"  查询出错: {e}")

# 5. 网上搜索验证
print(f"\n5. 基于股票代码的推测...")

print(f"  AU.NB:")
print(f"    - AU通常代表AngloGold Ashanti (南非金矿公司)")
print(f"    - .NB后缀表示在纳斯达克交易")
print(f"    - 这是一家著名的黄金开采公司")

print(f"\n  AIMAU.NB:")
print(f"    - AIM前缀可能代表不同的公司或实体")
print(f"    - 可能是AU相关的分拆公司、子公司或不同实体")
print(f"    - 也可能是完全不同的公司，只是碰巧包含AU")

# 6. 数据时间重叠分析
print(f"\n6. 数据时间重叠分析...")

# 检查两个instrument在重叠时间段的数据是否一致
query_overlap_check = """
SELECT 
    'AU.NB' as instrument,
    financial_period_absolute,
    COUNT(*) as record_count
FROM income_statement
WHERE instrument = 'AU.NB'
  AND financial_period_absolute IN ('FY2021', 'FY2022', 'FY2023', 'FY2024')
GROUP BY financial_period_absolute

UNION ALL

SELECT 
    'AIMAU.NB' as instrument,
    financial_period_absolute,
    COUNT(*) as record_count
FROM income_statement
WHERE instrument = 'AIMAU.NB'
  AND financial_period_absolute IN ('FY2021', 'FY2022', 'FY2023', 'FY2024')
GROUP BY financial_period_absolute

ORDER BY financial_period_absolute, instrument
"""

try:
    result_overlap = client_lseg.execute(query_overlap_check)
    if result_overlap:
        print(f"  重叠时期的数据对比:")
        current_period = None
        for instrument, period, count in result_overlap:
            if period != current_period:
                print(f"    {period}:")
                current_period = period
            print(f"      {instrument}: {count}条记录")
except Exception as e:
    print(f"  查询出错: {e}")

print(f"\n=== 结论 ===")
print(f"需要进一步验证AU.NB和AIMAU.NB是否为同一家公司:")
print(f"1. 如果是同一家公司的不同代码，数据应该在相同时期保持一致")
print(f"2. 如果是不同公司，我们需要重新评估数据提取策略")
print(f"3. 建议查看实际的财务数据内容来判断公司身份")

