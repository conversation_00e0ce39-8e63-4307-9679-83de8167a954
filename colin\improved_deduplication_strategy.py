from clickhouse_driver import Client
from datetime import datetime

def improved_deduplication_strategy():
    """
    改进的去重策略 - 只删除真正的重复数据，保留有价值的更新
    """
    print("="*80)
    print("改进的精确去重策略")
    print("="*80)
    
    try:
        ap_client = Client(
            host='************',
            port=9000,
            user='default',
            password='5ur2pK8WZQdy2',
            database='ap_research'
        )
        
        print("✅ 成功连接到ap_research数据库!")
        
        # 使用原始备份数据进行重新去重
        backup_table = 'priority_quality_fundamental_data_original_backup'
        
        # 1. 分析真正的重复模式
        print(f"\n🔍 分析真正的重复 vs 有效更新...")
        
        duplicate_analysis = ap_client.execute(f"""
            SELECT 
                stock_symbol, instrument, statement_type, financial_period_absolute, item_name,
                COUNT(DISTINCT value) as value_variants,
                COUNT(DISTINCT source) as source_variants,
                COUNT(DISTINCT currency) as currency_variants,
                COUNT(*) as total_records,
                COUNT(DISTINCT DATE(original_created_at)) as distinct_dates
            FROM {backup_table}
            GROUP BY stock_symbol, instrument, statement_type, financial_period_absolute, item_name
            HAVING COUNT(*) > 1
            ORDER BY total_records DESC
            LIMIT 20
        """)
        
        print(f"📋 重复记录分类分析:")
        print(f"{'股票':<6} {'期间':<12} {'科目':<25} {'总数':<6} {'数值变体':<8} {'源变体':<6} {'币种变体':<8} {'日期数'}")
        print("-" * 90)
        
        true_duplicates = 0
        valid_updates = 0
        
        for record in duplicate_analysis:
            symbol, instrument, stmt_type, period, item, val_var, src_var, curr_var, total, date_count = record
            item_display = (item[:24] + '..') if item and len(item) > 24 else (item or 'N/A')
            
            # 分类判断
            if val_var == 1 and src_var == 1 and curr_var == 1:
                category = "真正重复"
                true_duplicates += 1
            else:
                category = "有效更新"  
                valid_updates += 1
                
            print(f"{symbol:<6} {period:<12} {item_display:<25} {total:<6} {val_var:<8} {src_var:<6} {curr_var:<8} {date_count} ({category})")
        
        print(f"\n📊 分类统计:")
        print(f"   🗑️  真正重复: {true_duplicates} 组")
        print(f"   ✅ 有效更新: {valid_updates} 组") 
        
        # 2. 制定改进的去重规则
        print(f"\n📝 改进去重规则:")
        print(f"   ✅ 保留规则1: 不同数值的记录都保留（财报修正）")
        print(f"   ✅ 保留规则2: 不同来源的记录都保留（多来源验证）")
        print(f"   ✅ 保留规则3: 不同货币的记录都保留（汇率差异）")
        print(f"   🗑️  删除规则: 只有完全相同的记录才删除重复")
        
        # 3. 创建改进后的去重表
        improved_table = 'priority_quality_fundamental_data_improved'
        
        print(f"\n🏗️ 创建改进去重表: {improved_table}")
        
        # 删除可能存在的表
        try:
            ap_client.execute(f"DROP TABLE IF EXISTS {improved_table}")
        except:
            pass
        
        # 创建改进表结构
        create_improved_sql = f"""
        CREATE TABLE {improved_table} (
            stock_symbol String,
            original_stock_code String,
            turnover_type String,
            instrument String,
            statement_type String,
            financial_period_absolute String,
            source String,
            item_name String,
            currency String,
            value Float64,
            cumulative_return Float64,
            annualized_return Float64,
            original_created_at DateTime,
            create_date DateTime DEFAULT now(),
            
            INDEX idx_symbol stock_symbol TYPE bloom_filter GRANULARITY 1,
            INDEX idx_period financial_period_absolute TYPE bloom_filter GRANULARITY 1,
            INDEX idx_item item_name TYPE bloom_filter GRANULARITY 1
        ) ENGINE = MergeTree()
        PARTITION BY statement_type
        ORDER BY (stock_symbol, statement_type, financial_period_absolute, item_name)
        """
        
        ap_client.execute(create_improved_sql)
        print(f"✅ 改进表创建成功!")
        
        # 4. 使用改进的去重逻辑插入数据
        print(f"\n📤 使用改进去重逻辑插入数据...")
        
        # 改进的去重策略：只对完全相同的记录去重，保留最新的
        improved_insert_sql = f"""
        INSERT INTO {improved_table} (
            stock_symbol, original_stock_code, turnover_type, instrument,
            statement_type, financial_period_absolute, source, item_name, currency,
            value, cumulative_return, annualized_return, original_created_at
        )
        SELECT 
            stock_symbol, original_stock_code, turnover_type, instrument,
            statement_type, financial_period_absolute, source, item_name, currency,
            value, cumulative_return, annualized_return, original_created_at
        FROM (
            SELECT 
                stock_symbol, original_stock_code, turnover_type, instrument,
                statement_type, financial_period_absolute, source, item_name, currency,
                value, cumulative_return, annualized_return, original_created_at,
                ROW_NUMBER() OVER (
                    PARTITION BY 
                        stock_symbol, instrument, statement_type, 
                        financial_period_absolute, item_name, 
                        value, source, currency  -- 包含所有关键字段
                    ORDER BY original_created_at DESC
                ) as rn
            FROM {backup_table}
        ) ranked
        WHERE rn = 1
        ORDER BY stock_symbol, statement_type, financial_period_absolute, item_name
        """
        
        ap_client.execute(improved_insert_sql)
        print(f"✅ 改进去重插入完成!")
        
        # 5. 验证改进结果
        print(f"\n🔍 验证改进结果...")
        
        original_count = ap_client.execute(f"SELECT COUNT(*) FROM {backup_table}")[0][0]
        improved_count = ap_client.execute(f"SELECT COUNT(*) FROM {improved_table}")[0][0]
        old_clean_count = ap_client.execute("SELECT COUNT(*) FROM priority_quality_fundamental_data")[0][0]
        
        print(f"📊 数据量对比:")
        print(f"   原始数据: {original_count:,}")
        print(f"   旧去重后: {old_clean_count:,} (保留率: {old_clean_count/original_count*100:.1f}%)")
        print(f"   改进去重: {improved_count:,} (保留率: {improved_count/original_count*100:.1f}%)")
        print(f"   改进增加: {improved_count - old_clean_count:,} 条记录")
        
        # 6. 重要财报科目数据完整性对比
        print(f"\n📋 重要财报科目数据完整性对比:")
        
        completeness_check = ap_client.execute(f"""
            SELECT 
                item_category,
                original.records as original_records,
                old_clean.records as old_clean_records,
                improved.records as improved_records,
                old_clean.records * 100.0 / original.records as old_retention_rate,
                improved.records * 100.0 / original.records as improved_retention_rate
            FROM (
                SELECT 
                    CASE 
                        WHEN item_name LIKE '%Revenue%' THEN 'Revenue'
                        WHEN item_name LIKE '%Net Income%' THEN 'Net Income'
                        WHEN item_name LIKE '%Total Assets%' THEN 'Total Assets'
                        WHEN item_name LIKE '%Cash%' THEN 'Cash'
                        ELSE 'Other'
                    END as item_category,
                    COUNT(*) as records
                FROM {backup_table}
                GROUP BY item_category
            ) original
            JOIN (
                SELECT 
                    CASE 
                        WHEN item_name LIKE '%Revenue%' THEN 'Revenue'
                        WHEN item_name LIKE '%Net Income%' THEN 'Net Income'
                        WHEN item_name LIKE '%Total Assets%' THEN 'Total Assets'
                        WHEN item_name LIKE '%Cash%' THEN 'Cash'
                        ELSE 'Other'
                    END as item_category,
                    COUNT(*) as records
                FROM priority_quality_fundamental_data
                GROUP BY item_category
            ) old_clean ON original.item_category = old_clean.item_category
            JOIN (
                SELECT 
                    CASE 
                        WHEN item_name LIKE '%Revenue%' THEN 'Revenue'
                        WHEN item_name LIKE '%Net Income%' THEN 'Net Income'
                        WHEN item_name LIKE '%Total Assets%' THEN 'Total Assets'
                        WHEN item_name LIKE '%Cash%' THEN 'Cash'
                        ELSE 'Other'
                    END as item_category,
                    COUNT(*) as records
                FROM {improved_table}
                GROUP BY item_category
            ) improved ON original.item_category = improved.item_category
            WHERE original.item_category != 'Other'
            ORDER BY improved_retention_rate DESC
        """)
        
        print(f"{'科目类型':<12} {'原始':<10} {'旧方法':<10} {'改进方法':<10} {'旧保留率%':<10} {'改进保留率%'}")
        print("-" * 70)
        
        significant_improvement = False
        for item_cat, orig, old_clean, improved, old_rate, improved_rate in completeness_check:
            print(f"{item_cat:<12} {orig:<10} {old_clean:<10} {improved:<10} {old_rate:<10.1f} {improved_rate:<10.1f}")
            
            if improved_rate - old_rate > 20:  # 改进超过20%
                significant_improvement = True
        
        # 7. 检查数值变化记录的保留情况
        print(f"\n🔍 数值变化记录保留情况:")
        
        value_variance_preserved = ap_client.execute(f"""
            SELECT 
                stock_symbol, financial_period_absolute, item_name,
                COUNT(DISTINCT value) as value_variants_preserved,
                COUNT(*) as total_records_preserved
            FROM {improved_table}
            GROUP BY stock_symbol, financial_period_absolute, item_name
            HAVING COUNT(DISTINCT value) > 1
            ORDER BY value_variants_preserved DESC, total_records_preserved DESC
            LIMIT 10
        """)
        
        print(f"📋 成功保留的数值变化记录 (前10组):")
        print(f"{'股票':<6} {'期间':<12} {'科目':<25} {'数值变体':<8} {'总记录数'}")
        print("-" * 65)
        
        for symbol, period, item, variants, total in value_variance_preserved:
            item_display = (item[:24] + '..') if item and len(item) > 24 else (item or 'N/A')
            print(f"{symbol:<6} {period:<12} {item_display:<25} {variants:<8} {total}")
        
        # 8. 最终建议
        print(f"\n💡 最终结论:")
        
        if significant_improvement:
            print(f"🎉 改进去重策略显著提升数据完整性!")
            print(f"   ✅ 保留了财报修正和更新数据")
            print(f"   ✅ 保留了多来源验证数据") 
            print(f"   ✅ 重要财报科目完整性大幅改善")
            
            # 建议替换主表
            print(f"\n🔄 建议替换主表:")
            print(f"   1. 备份当前主表")
            print(f"   2. 使用改进去重结果替换")
            print(f"   3. 验证数据完整性")
        else:
            print(f"⚠️ 改进效果有限，需要进一步优化策略")
        
        # 9. 生成详细对比报告
        with open('deduplication_improvement_report.txt', 'w', encoding='utf-8') as f:
            f.write("去重策略改进报告\n")
            f.write("="*60 + "\n\n")
            f.write(f"改进时间: {datetime.now()}\n\n")
            
            f.write(f"数据量对比:\n")
            f.write(f"- 原始数据: {original_count:,} 条\n")
            f.write(f"- 旧去重方法: {old_clean_count:,} 条 (保留{old_clean_count/original_count*100:.1f}%)\n")
            f.write(f"- 改进去重方法: {improved_count:,} 条 (保留{improved_count/original_count*100:.1f}%)\n")
            f.write(f"- 改进效果: 增加保留 {improved_count - old_clean_count:,} 条记录\n\n")
            
            f.write(f"改进策略:\n")
            f.write(f"- 旧策略: 按(stock_symbol, instrument, statement_type, period, item_name, value)去重\n")
            f.write(f"- 新策略: 按(stock_symbol, instrument, statement_type, period, item_name, value, source, currency)去重\n")
            f.write(f"- 核心改进: 保留不同来源和不同货币的记录\n\n")
            
            f.write(f"重要科目完整性改进:\n")
            for item_cat, orig, old_clean, improved, old_rate, improved_rate in completeness_check:
                improvement = improved_rate - old_rate
                f.write(f"- {item_cat}: {old_rate:.1f}% → {improved_rate:.1f}% (改进{improvement:.1f}%)\n")
        
        print(f"\n✅ 改进报告已保存: deduplication_improvement_report.txt")
        
        print(f"\n" + "="*80)
        print(f"✅ 改进去重策略分析完成!")
        print(f"🎯 建议使用改进后的去重结果以保持数据完整性")
        print("="*80)
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    improved_deduplication_strategy()