"""
Colin McLean 26个因子详细实现文档
包含每个因子的计算逻辑、参数、时间窗口、数据字段、计算公式等所有细节
"""

def print_detailed_factor_implementation():
    """打印26个因子的详细实现逻辑"""
    
    print("=== Colin McLean 26个因子详细实现文档 ===")
    print("基于ap_research数据库的实际实现")
    print("数据源：821只股票，平均13年年报数据 + 完整价格数据")
    print()
    
    factors_detail = {
        
        # 技术相关因子 (1-13)
        "1. tech_premium (技术溢价)": {
            "分类": "技术相关因子",
            "计算公式": "tech_premium = (总研发费用 / 营业收入) × 100",
            "数据字段": {
                "分子": "Research & Development Expense + Research & Development Expense - Supplemental",
                "分母": "Revenue from Business Activities - Total"
            },
            "时间窗口": "始终用最新年度数据",
            "最低数据年限要求": "1年（上市满1年）",
            "动态时间窗口": "否",
            "计算逻辑": """
            1. 获取最新年度的研发费用（主要+补充）
            2. 获取最新年度的营业收入
            3. 计算研发强度比例并转换为百分比
            4. 如果营业收入为0或空，则因子为None
            """,
            "单位": "百分比(%)",
            "覆盖率": "42.3% (347/821)",
            "备注": "只有有研发投入的公司才有值，符合预期"
        },
        
        "2. tech_gap_warning (技术差距警告)": {
            "分类": "技术相关因子", 
            "计算公式": "tech_gap_warning = (当前研发强度 - 上年研发强度) / 上年研发强度 × 100",
            "数据字段": {
                "当前研发强度": "tech_premium (当年)",
                "上年研发强度": "tech_premium (上一年)"
            },
            "时间窗口": "上市≥2年：用t年与t-1年；上市<2年：因子缺失",
            "最低数据年限要求": "2年",
            "动态时间窗口": "是",
            "计算逻辑": """
            1. 先计算当前年度的tech_premium
            2. 计算上一年度的tech_premium  
            3. 计算两者的变化率
            4. 如果上市年限<2年或任一年研发强度为0，则因子为None
            """,
            "单位": "百分比(%)",
            "覆盖率": "42.3% (347/821)",
            "备注": "依赖tech_premium，覆盖率相同"
        },
        
        "3. patent_density (专利密度)": {
            "分类": "技术相关因子",
            "计算公式": "patent_density = (总研发费用 / 总资产) × 100", 
            "数据字段": {
                "分子": "Research & Development Expense + Research & Development Expense - Supplemental",
                "分母": "Total Assets"
            },
            "时间窗口": "始终用最新年度数据",
            "最低数据年限要求": "1年",
            "动态时间窗口": "否",
            "计算逻辑": """
            1. 获取最新年度的总研发费用
            2. 获取最新年度的总资产
            3. 计算研发费用占总资产的比例
            4. 使用研发费用/总资产作为专利密度的代理指标
            """,
            "单位": "百分比(%)",
            "覆盖率": "42.3% (347/821)", 
            "备注": "使用研发费用作为专利密度的代理指标"
        },
        
        "4. ecosystem_cash_ratio (生态现金比率)": {
            "分类": "技术相关因子",
            "计算公式": "ecosystem_cash_ratio = (现金及等价物 + 短期投资) / 营业收入 × 100",
            "数据字段": {
                "分子": "Cash & Cash Equivalents + Short-Term Investments",
                "分母": "Revenue from Business Activities - Total"
            },
            "时间窗口": "始终用最新年度数据", 
            "最低数据年限要求": "1年",
            "动态时间窗口": "否",
            "计算逻辑": """
            1. 获取现金及现金等价物
            2. 获取短期投资
            3. 计算总现金（现金+短期投资）
            4. 除以营业收入得到现金比率
            """,
            "单位": "百分比(%)",
            "覆盖率": "95.4% (783/821)",
            "备注": "覆盖率高，现金数据相对完整"
        },
        
        "5. adjusted_roce (调整后ROCE)": {
            "分类": "技术相关因子",
            "计算公式": "adjusted_roce = (净利润 / 股东权益总额) × 100",
            "数据字段": {
                "分子": "Net Income", 
                "分母": "Total Shareholders' Equity"
            },
            "时间窗口": "始终用最新年度数据",
            "最低数据年限要求": "1年", 
            "动态时间窗口": "否",
            "计算逻辑": """
            1. 获取最新年度净利润
            2. 获取最新年度股东权益总额
            3. 计算ROE (Return on Equity)
            4. 实际上这是ROE而不是ROCE
            """,
            "单位": "百分比(%)",
            "覆盖率": "0.0% (0/821)",
            "备注": "⚠️ 字段名问题：'Total Shareholders' Equity' 可能不存在"
        },
        
        "6. fcf_quality (自由现金流质量)": {
            "分类": "技术相关因子",
            "计算公式": "fcf_quality = 自由现金流 / 净利润",
            "数据字段": {
                "分子": "Free Cash Flow",
                "分母": "Net Income"
            },
            "时间窗口": "始终用最新年度数据",
            "最低数据年限要求": "1年",
            "动态时间窗口": "否", 
            "计算逻辑": """
            1. 获取最新年度自由现金流
            2. 获取最新年度净利润
            3. 计算自由现金流与净利润的比值
            4. 比值>1表示现金流质量好
            """,
            "单位": "倍数",
            "覆盖率": "0.0% (0/821)",
            "备注": "⚠️ 字段名问题：'Free Cash Flow' 可能不存在"
        },
        
        "7. dynamic_safety_margin (动态安全边际)": {
            "分类": "技术相关因子",
            "计算公式": "dynamic_safety_margin = tech_premium + ecosystem_cash_ratio", 
            "数据字段": {
                "组成": "tech_premium + ecosystem_cash_ratio"
            },
            "时间窗口": "始终用最新年度数据",
            "最低数据年限要求": "1年（依赖因子1、4均非缺失）",
            "动态时间窗口": "否",
            "计算逻辑": """
            1. 需要tech_premium和ecosystem_cash_ratio都非空
            2. 直接相加得到动态安全边际
            3. 任一因子为空则结果为空
            """,
            "单位": "百分比(%)",
            "覆盖率": "42.1% (346/821)",
            "备注": "依赖tech_premium，覆盖率受限"
        },
        
        "8. revenue_growth_continuity (营收增长连续性)": {
            "分类": "技术相关因子",
            "计算公式": "revenue_growth_continuity = std(近3年营收增长率)",
            "数据字段": {
                "基础数据": "Revenue from Business Activities - Total (多年)"
            },
            "时间窗口": "上市≥3年：近3年（t-2至t年）；上市2年：近2年；上市<2年：因子缺失", 
            "最低数据年限要求": "2年",
            "动态时间窗口": "是",
            "计算逻辑": """
            1. 计算最近3年（或实际可得年数）的营收同比增长率
            2. 计算这些增长率的标准差
            3. 标准差越小表示增长越连续稳定
            4. 最多看3年数据
            """,
            "单位": "百分点",
            "覆盖率": "99.5% (817/821)",
            "备注": "覆盖率高，营收数据完整"
        },
        
        "9. effective_tax_rate_improvement (有效税率改善)": {
            "分类": "技术相关因子",
            "计算公式": "effective_tax_rate_improvement = (所得税费用 / 税前利润) × 100",
            "数据字段": {
                "分子": "Income Taxes",
                "分母": "Income before Taxes"
            },
            "时间窗口": "始终用最新年度数据（需税前利润>0）",
            "最低数据年限要求": "1年",
            "动态时间窗口": "否",
            "计算逻辑": """
            1. 获取最新年度所得税费用
            2. 获取最新年度税前利润
            3. 计算有效税率
            4. 需要税前利润>0才计算
            """,
            "单位": "百分比(%)",
            "覆盖率": "86.4% (709/821)",
            "备注": "实际计算的是有效税率而非改善程度"
        },
        
        "10. financial_health (财务健康度)": {
            "分类": "技术相关因子",
            "计算公式": "financial_health = 流动资产 / 流动负债",
            "数据字段": {
                "分子": "Current Assets - Total",
                "分母": "Current Liabilities - Total"
            },
            "时间窗口": "始终用最新年度数据",
            "最低数据年限要求": "1年",
            "动态时间窗口": "否",
            "计算逻辑": """
            1. 获取最新年度流动资产总额
            2. 获取最新年度流动负债总额  
            3. 计算流动比率
            4. 比率>1表示短期偿债能力良好
            """,
            "单位": "倍数",
            "覆盖率": "0.0% (0/821)",
            "备注": "⚠️ 字段名问题：流动资产/负债字段可能不存在"
        },
        
        "11. valuation_bubble_signal (估值泡沫信号)": {
            "分类": "技术相关因子",
            "计算公式": "valuation_bubble_signal = 收盘价 / 每股收益",
            "数据字段": {
                "分子": "close_price",
                "分母": "Earnings Per Share - Diluted"
            },
            "时间窗口": "始终用最新季度数据",
            "最低数据年限要求": "1年（有10-Q/10-K数据）",
            "动态时间窗口": "否",
            "计算逻辑": """
            1. 获取最新的收盘价
            2. 获取最新的稀释每股收益
            3. 计算PE比率
            4. PE比率越高估值泡沫风险越大
            """,
            "单位": "倍数",
            "覆盖率": "0.0% (0/821)",
            "备注": "⚠️ 字段名问题：EPS字段可能不存在"
        },
        
        "12. rd_intensity (研发强度)": {
            "分类": "技术相关因子", 
            "计算公式": "rd_intensity = tech_premium (相同)",
            "数据字段": {
                "引用": "与tech_premium完全相同"
            },
            "时间窗口": "始终用最新年度数据",
            "最低数据年限要求": "1年",
            "动态时间窗口": "否",
            "计算逻辑": """
            1. 直接引用tech_premium的值
            2. 实际上是重复因子
            """,
            "单位": "百分比(%)",
            "覆盖率": "42.3% (347/821)",
            "备注": "与tech_premium重复"
        },
        
        "13. roce_stability (ROCE稳定性)": {
            "分类": "技术相关因子",
            "计算公式": "roce_stability = std(近5年ROCE值)",
            "数据字段": {
                "基础数据": "Net Income, Total Shareholders' Equity (多年)"
            },
            "时间窗口": "上市≥5年：近5年；上市3-4年：实际年限；上市<3年：因子缺失",
            "最低数据年限要求": "3年", 
            "动态时间窗口": "是",
            "计算逻辑": """
            1. 计算最近5年（或实际可得年数）的ROCE值
            2. ROCE = 净利润 / 股东权益
            3. 计算ROCE值的标准差
            4. 标准差越小表示ROCE越稳定
            """,
            "单位": "百分点",
            "覆盖率": "0.0% (0/821)",
            "备注": "⚠️ 依赖股东权益字段，可能不存在"
        },
        
        # 财务相关因子 (14-26)
        "14. revenue_yoy (营收同比增长)": {
            "分类": "财务相关因子",
            "计算公式": "revenue_yoy = (当年营收 - 上年营收) / 上年营收 × 100",
            "数据字段": {
                "基础数据": "Revenue from Business Activities - Total"
            },
            "时间窗口": "上市≥2年：t年与t-1年；上市<2年：因子缺失",
            "最低数据年限要求": "2年",
            "动态时间窗口": "是",
            "计算逻辑": """
            1. 获取最新年度营收
            2. 获取上一年度营收
            3. 计算同比增长率
            4. 需要上市年限≥2年
            """,
            "单位": "百分比(%)",
            "覆盖率": "99.4% (816/821)",
            "备注": "覆盖率极高，营收数据完整"
        },
        
        "15. revenue_cagr (营收CAGR)": {
            "分类": "财务相关因子",
            "计算公式": "revenue_cagr = ((当前营收/起始营收)^(1/(年数-1)) - 1) × 100",
            "数据字段": {
                "基础数据": "Revenue from Business Activities - Total"
            },
            "时间窗口": "上市≥8年：8年；上市5-7年：实际年限；上市3-4年：实际年限；上市<3年：因子缺失",
            "最低数据年限要求": "3年",
            "动态时间窗口": "是",
            "计算逻辑": """
            1. 确定时间窗口：min(8年, 实际上市年限)
            2. 获取当前年度和起始年度营收
            3. 计算复合年均增长率
            4. 需要两个营收都>0
            """,
            "单位": "百分比(%)",
            "覆盖率": "99.0% (813/821)", 
            "备注": "动态时间窗口，覆盖率高"
        },
        
        "16. net_income_yoy (净利润同比增长)": {
            "分类": "财务相关因子",
            "计算公式": "net_income_yoy = (当年净利润 - 上年净利润) / |上年净利润| × 100",
            "数据字段": {
                "基础数据": "Net Income"
            },
            "时间窗口": "上市≥2年：t年与t-1年；上市<2年：因子缺失",
            "最低数据年限要求": "2年",
            "动态时间窗口": "是",
            "计算逻辑": """
            1. 获取最新年度净利润
            2. 获取上一年度净利润
            3. 计算同比增长率，分母用绝对值处理负值情况
            4. 需要上年净利润≠0
            """,
            "单位": "百分比(%)",
            "覆盖率": "0.0% (0/821)",
            "备注": "⚠️ 字段名问题：'Net Income' 可能不存在"
        },
        
        "17. profit_revenue_ratio (利润营收比)": {
            "分类": "财务相关因子",
            "计算公式": "profit_revenue_ratio = (净利润 / 营业收入) × 100",
            "数据字段": {
                "分子": "Net Income",
                "分母": "Revenue from Business Activities - Total"
            },
            "时间窗口": "同14、16因子（依赖两者非缺失）",
            "最低数据年限要求": "2年",
            "动态时间窗口": "否",
            "计算逻辑": """
            1. 获取最新年度净利润
            2. 获取最新年度营业收入
            3. 计算净利润率
            4. 反映盈利能力
            """,
            "单位": "百分比(%)",
            "覆盖率": "99.5% (817/821)",
            "备注": "覆盖率高，可能使用了替代字段"
        },
        
        "18. fcf_per_share (每股自由现金流)": {
            "分类": "财务相关因子",
            "计算公式": "fcf_per_share = 自由现金流 / 稀释股本",
            "数据字段": {
                "分子": "Free Cash Flow",
                "分母": "Shares Outstanding - Diluted Average"
            },
            "时间窗口": "始终用最新年度数据",
            "最低数据年限要求": "1年",
            "动态时间窗口": "否",
            "计算逻辑": """
            1. 获取最新年度自由现金流
            2. 获取稀释后平均股本
            3. 计算每股自由现金流
            4. 需要股本>0
            """,
            "单位": "货币单位/股",
            "覆盖率": "0.0% (0/821)",
            "备注": "⚠️ 字段名问题：FCF和股本字段可能不存在"
        },
        
        "19. fcf_cagr (自由现金流CAGR)": {
            "分类": "财务相关因子", 
            "计算公式": "fcf_cagr = ((当前FCF/起始FCF)^(1/(年数-1)) - 1) × 100",
            "数据字段": {
                "基础数据": "Free Cash Flow"
            },
            "时间窗口": "同15因子（营收CAGR）",
            "最低数据年限要求": "3年",
            "动态时间窗口": "是",
            "计算逻辑": """
            1. 确定时间窗口：min(8年, 实际上市年限)
            2. 获取当前年度和起始年度FCF
            3. 计算复合年均增长率
            4. 需要两个FCF都>0
            """,
            "单位": "百分比(%)",
            "覆盖率": "70.2% (576/821)",
            "备注": "覆盖率中等，可能使用了替代FCF计算"
        },
        
        "20. fcf_net_income_ratio (自由现金流净利润比)": {
            "分类": "财务相关因子",
            "计算公式": "fcf_net_income_ratio = 自由现金流 / 净利润",
            "数据字段": {
                "分子": "Free Cash Flow",
                "分母": "Net Income"
            },
            "时间窗口": "始终用最新年度数据",
            "最低数据年限要求": "1年", 
            "动态时间窗口": "否",
            "计算逻辑": """
            1. 获取最新年度自由现金流
            2. 获取最新年度净利润
            3. 计算比值，衡量现金流质量
            4. 需要净利润>0
            """,
            "单位": "倍数",
            "覆盖率": "0.0% (0/821)",
            "备注": "⚠️ 与fcf_quality重复，字段问题"
        },
        
        "21. operating_margin (营业利润率)": {
            "分类": "财务相关因子",
            "计算公式": "operating_margin = (营业收入 / 营业收入) × 100",
            "数据字段": {
                "分子": "Operating Income",
                "分母": "Revenue from Business Activities - Total"
            },
            "时间窗口": "始终用最新年度数据",
            "最低数据年限要求": "1年",
            "动态时间窗口": "否",
            "计算逻辑": """
            1. 获取最新年度营业收入
            2. 获取最新年度营业收入（分母）
            3. 计算营业利润率
            4. 需要营业收入>0
            """,
            "单位": "百分比(%)",
            "覆盖率": "99.5% (817/821)",
            "备注": "覆盖率高，核心财务指标"
        },
        
        "22. operating_margin_std (营业利润率稳定性)": {
            "分类": "财务相关因子",
            "计算公式": "operating_margin_std = std(近4年营业利润率)",
            "数据字段": {
                "基础数据": "Operating Income, Revenue from Business Activities - Total"
            },
            "时间窗口": "上市≥4年：近4年；上市2-3年：实际年限；上市<2年：因子缺失",
            "最低数据年限要求": "2年",
            "动态时间窗口": "是",
            "计算逻辑": """
            1. 计算最近4年（或实际可得年数）的营业利润率
            2. 计算这些利润率的标准差
            3. 标准差越小表示盈利越稳定
            4. 至少需要2年数据
            """,
            "单位": "百分点",
            "覆盖率": "99.9% (820/821)",
            "备注": "覆盖率最高，数据最完整"
        },
        
        "23. roic (ROIC)": {
            "分类": "财务相关因子",
            "计算公式": "roic = (净利润 / (总债务 + 股东权益)) × 100",
            "数据字段": {
                "分子": "Net Income",
                "分母": "Total Debt + Total Shareholders' Equity"
            },
            "时间窗口": "始终用最新年度数据",
            "最低数据年限要求": "1年",
            "动态时间窗口": "否",
            "计算逻辑": """
            1. 获取最新年度净利润
            2. 获取总债务和股东权益
            3. 计算投入资本回报率
            4. 投入资本 = 总债务 + 股东权益
            """,
            "单位": "百分比(%)",
            "覆盖率": "0.0% (0/821)",
            "备注": "⚠️ 字段名问题：债务和权益字段可能不存在"
        },
        
        "24. roic_cagr (ROIC CAGR)": {
            "分类": "财务相关因子",
            "计算公式": "roic_cagr = ((当前ROIC/起始ROIC)^(1/(年数-1)) - 1) × 100",
            "数据字段": {
                "基础数据": "依赖ROIC计算"
            },
            "时间窗口": "同15因子（营收CAGR）",
            "最低数据年限要求": "3年",
            "动态时间窗口": "是",
            "计算逻辑": """
            1. 计算当前年度ROIC
            2. 计算起始年度ROIC
            3. 计算ROIC的复合年均增长率
            4. 需要两个ROIC都>0
            """,
            "单位": "百分比(%)",
            "覆盖率": "0.0% (0/821)",
            "备注": "⚠️ 依赖ROIC，字段问题导致无法计算"
        },
        
        "25. effective_tax_rate (有效税率)": {
            "分类": "财务相关因子",
            "计算公式": "effective_tax_rate = (所得税费用 / EBIT) × 100",
            "数据字段": {
                "分子": "Income Taxes",
                "分母": "Earnings before Interest & Taxes (EBIT)"
            },
            "时间窗口": "始终用最新年度数据（需EBIT>0）",
            "最低数据年限要求": "1年",
            "动态时间窗口": "否",
            "计算逻辑": """
            1. 获取最新年度所得税费用
            2. 获取最新年度EBIT
            3. 计算有效税率
            4. 需要EBIT>0
            """,
            "单位": "百分比(%)",
            "覆盖率": "91.2% (749/821)",
            "备注": "覆盖率高，税务数据相对完整"
        },
        
        "26. effective_tax_rate_std (有效税率稳定性)": {
            "分类": "财务相关因子",
            "计算公式": "effective_tax_rate_std = std(近4年有效税率)",
            "数据字段": {
                "基础数据": "Income Taxes, Earnings before Interest & Taxes (EBIT)"
            },
            "时间窗口": "同22因子（营业利润率稳定性）",
            "最低数据年限要求": "2年",
            "动态时间窗口": "是",
            "计算逻辑": """
            1. 计算最近4年（或实际可得年数）的有效税率
            2. 计算这些税率的标准差
            3. 标准差越小表示税率越稳定
            4. 至少需要2年数据
            """,
            "单位": "百分点",
            "覆盖率": "91.2% (749/821)",
            "备注": "与effective_tax_rate覆盖率相同"
        }
    }
    
    # 按分类打印详细信息
    for factor_name, details in factors_detail.items():
        print(f"{'='*80}")
        print(f"{factor_name}")
        print(f"{'='*80}")
        
        for key, value in details.items():
            if key == "数据字段" and isinstance(value, dict):
                print(f"📊 {key}:")
                for field_key, field_value in value.items():
                    print(f"    {field_key}: {field_value}")
            elif key == "计算逻辑":
                print(f"🔧 {key}:")
                for line in value.strip().split('\n'):
                    print(f"    {line.strip()}")
            else:
                print(f"📋 {key}: {value}")
        print()
    
    # 总结统计
    print(f"{'='*80}")
    print("📊 总结统计")
    print(f"{'='*80}")
    
    # 按覆盖率分类统计
    high_coverage = []  # >90%
    medium_coverage = []  # 70-90%
    low_coverage = []  # 40-70%
    zero_coverage = []  # 0%
    
    for factor_name, details in factors_detail.items():
        coverage_str = details.get("覆盖率", "0%")
        coverage_num = float(coverage_str.split('%')[0].split('(')[0])
        
        if coverage_num == 0:
            zero_coverage.append(factor_name.split('.')[1].split(' ')[0])
        elif coverage_num >= 90:
            high_coverage.append(factor_name.split('.')[1].split(' ')[0])
        elif coverage_num >= 70:
            medium_coverage.append(factor_name.split('.')[1].split(' ')[0])
        else:
            low_coverage.append(factor_name.split('.')[1].split(' ')[0])
    
    print(f"🔥 高覆盖率因子 (≥90%): {len(high_coverage)}个")
    print(f"   {', '.join(high_coverage)}")
    print()
    
    print(f"⚡ 中等覆盖率因子 (70-90%): {len(medium_coverage)}个") 
    print(f"   {', '.join(medium_coverage)}")
    print()
    
    print(f"🔬 低覆盖率因子 (40-70%): {len(low_coverage)}个")
    print(f"   {', '.join(low_coverage)}")
    print()
    
    print(f"⚠️ 零覆盖率因子 (0%): {len(zero_coverage)}个")
    print(f"   {', '.join(zero_coverage)}")
    print()
    
    # 问题总结
    print(f"🚨 主要问题:")
    print(f"   1. 字段名映射问题：多个因子因为数据库字段名不匹配导致0%覆盖率")
    print(f"   2. 重复因子：rd_intensity与tech_premium完全重复")
    print(f"   3. 命名不准确：adjusted_roce实际计算的是ROE")
    print(f"   4. 逻辑问题：effective_tax_rate_improvement实际计算税率而非改善")
    print()
    
    print(f"💡 建议修复:")
    print(f"   1. 检查并修正数据库字段名映射")
    print(f"   2. 实现真正的因子计算逻辑（如ROCE vs ROE）")
    print(f"   3. 添加缺失的数据字段（如FCF、股本等）")
    print(f"   4. 完善动态时间窗口逻辑")

if __name__ == "__main__":
    print_detailed_factor_implementation()

