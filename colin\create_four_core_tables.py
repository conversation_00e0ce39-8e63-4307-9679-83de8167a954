from clickhouse_driver import Client
from datetime import datetime, date
import traceback

def connect_to_clickhouse():
    """
    连接到ClickHouse数据库
    """
    try:
        client = Client(
            host='************',
            port=9000,
            user='default',
            password='5ur2pK8WZQdy2',
            database='ap_research'
        )
        print("✅ 成功连接到ap_research数据库")
        return client
    except Exception as e:
        print(f"❌ 连接数据库失败: {e}")
        return None

def create_quarterly_data_filled_enhanced(client):
    """
    创建增强的季报数据填充表
    """
    print("\n📋 创建 quarterly_data_filled_enhanced 表...")
    
    create_sql = """
    CREATE TABLE IF NOT EXISTS quarterly_data_filled_enhanced (
        -- 基本标识字段
        id String,
        stock_symbol String,
        financial_period_absolute String,
        statement_type String,
        item_name String,
        
        -- 原始数据字段
        original_value Nullable(Float64),
        original_effective_date Nullable(Date),
        
        -- 估算数据字段
        filled_simple_estimates Nullable(Float64),
        estimated_effective_date Nullable(Date),
        
        -- 修正数据字段
        corrected_filled_simple_estimates Nullable(Float64),
        corrected_effective_date Nullable(Date),
        
        -- 数据状态字段
        data_status String,
        fill_method Nullable(String),
        confidence_score Nullable(Float64),
        
        -- 会计科目管理字段
        item_status String,
        item_source String,
        item_mapping Nullable(String),
        correction_applicable String,
        historical_frequency Nullable(Int32),
        last_historical_period Nullable(String),
        first_current_period Nullable(String),
        
        -- 详细标注字段
        estimation_notes Nullable(String),
        correction_notes Nullable(String),
        item_change_reason Nullable(String),
        
        -- 源数据追踪字段
        source_periods Nullable(String),
        calculation_formula Nullable(String),
        
        -- 元数据字段
        created_at DateTime,
        updated_at DateTime,
        processing_version String,
        notes Nullable(String)
    )
    ENGINE = MergeTree()
    ORDER BY (stock_symbol, financial_period_absolute, statement_type, item_name)
    PARTITION BY stock_symbol;
    """
    
    try:
        client.execute(create_sql)
        print("✅ quarterly_data_filled_enhanced 表创建成功")
        print(f"   📊 字段数: 25个")
        print(f"   🔧 引擎: MergeTree")
        print(f"   📂 分区: 按stock_symbol")
        print(f"   🔑 排序: (stock_symbol, financial_period_absolute, statement_type, item_name)")
        return True
    except Exception as e:
        print(f"❌ 创建 quarterly_data_filled_enhanced 表失败: {e}")
        return False

def create_item_status_mapping(client):
    """
    创建科目状态映射表
    """
    print("\n📋 创建 item_status_mapping 表...")
    
    create_sql = """
    CREATE TABLE IF NOT EXISTS item_status_mapping (
        -- 基本标识字段
        id String,
        stock_symbol String,
        statement_type String,
        item_name String,
        
        -- 科目状态字段
        item_status String,
        item_source String,
        item_mapping Nullable(String),
        
        -- 历史信息字段
        historical_frequency Nullable(Int32),
        first_historical_period Nullable(String),
        last_historical_period Nullable(String),
        
        -- 当前信息字段
        first_current_period Nullable(String),
        current_frequency Nullable(Int32),
        
        -- 检测信息字段
        semantic_similarity Nullable(Float64),
        value_correlation Nullable(Float64),
        detection_confidence Nullable(Float64),
        
        -- 变化分析字段
        item_change_reason Nullable(String),
        change_detected_date Nullable(Date),
        
        -- 元数据字段
        created_at DateTime,
        updated_at DateTime,
        analysis_version String,
        notes Nullable(String)
    )
    ENGINE = MergeTree()
    ORDER BY (stock_symbol, statement_type, item_name)
    PARTITION BY statement_type;
    """
    
    try:
        client.execute(create_sql)
        print("✅ item_status_mapping 表创建成功")
        print(f"   📊 字段数: 19个")
        print(f"   🔧 引擎: MergeTree")
        print(f"   📂 分区: 按statement_type")
        print(f"   🔑 排序: (stock_symbol, statement_type, item_name)")
        return True
    except Exception as e:
        print(f"❌ 创建 item_status_mapping 表失败: {e}")
        return False

def create_long_time_na_list_detailed(client):
    """
    创建详细的长期缺失数据记录表
    """
    print("\n📋 创建 long_time_fundamental_na_list_detailed 表...")
    
    create_sql = """
    CREATE TABLE IF NOT EXISTS long_time_fundamental_na_list_detailed (
        -- 基本标识字段
        id String,
        stock_symbol String,
        
        -- 缺失分析字段
        missing_periods Array(String),
        available_periods Array(String),
        total_missing_count Int32,
        total_available_count Int32,
        
        -- 时间范围字段
        earliest_available_period Nullable(String),
        latest_available_period Nullable(String),
        missing_period_start Nullable(String),
        missing_period_end Nullable(String),
        
        -- 原因分析字段
        missing_reason String,
        data_gap_type String,
        investigation_status String,
        
        -- 建议字段
        recommended_action String,
        alternative_data_sources Nullable(String),
        
        -- 元数据字段
        created_at DateTime,
        updated_at DateTime,
        analysis_date Date,
        analyst_notes Nullable(String)
    )
    ENGINE = MergeTree()
    ORDER BY (stock_symbol, missing_period_start)
    PARTITION BY missing_reason;
    """
    
    try:
        client.execute(create_sql)
        print("✅ long_time_fundamental_na_list_detailed 表创建成功")
        print(f"   📊 字段数: 16个")
        print(f"   🔧 引擎: MergeTree")
        print(f"   📂 分区: 按missing_reason")
        print(f"   🔑 排序: (stock_symbol, missing_period_start)")
        return True
    except Exception as e:
        print(f"❌ 创建 long_time_fundamental_na_list_detailed 表失败: {e}")
        return False

def create_estimation_quality_metrics(client):
    """
    创建估算质量评估表
    """
    print("\n📋 创建 estimation_quality_metrics 表...")
    
    create_sql = """
    CREATE TABLE IF NOT EXISTS estimation_quality_metrics (
        -- 基本标识字段
        id String,
        metric_type String,
        
        -- 方法统计字段
        fill_method String,
        total_estimations Int32,
        successful_estimations Int32,
        successful_corrections Int32,
        success_rate Float64,
        
        -- 置信度统计字段
        avg_confidence_score Float64,
        min_confidence_score Nullable(Float64),
        max_confidence_score Nullable(Float64),
        confidence_std_dev Nullable(Float64),
        
        -- 科目状态统计字段
        active_items_count Int32,
        historical_only_count Int32,
        current_only_count Int32,
        renamed_items_count Int32,
        
        -- 质量指标字段
        avg_estimation_error Nullable(Float64),
        correction_improvement_rate Nullable(Float64),
        data_coverage_rate Float64,
        
        -- 时间统计字段
        analysis_period_start Date,
        analysis_period_end Date,
        
        -- 元数据字段
        created_at DateTime,
        updated_at DateTime,
        calculation_version String,
        detailed_notes Nullable(String)
    )
    ENGINE = MergeTree()
    ORDER BY (metric_type, fill_method, analysis_period_start)
    PARTITION BY metric_type;
    """
    
    try:
        client.execute(create_sql)
        print("✅ estimation_quality_metrics 表创建成功")
        print(f"   📊 字段数: 22个")
        print(f"   🔧 引擎: MergeTree")
        print(f"   📂 分区: 按metric_type")
        print(f"   🔑 排序: (metric_type, fill_method, analysis_period_start)")
        return True
    except Exception as e:
        print(f"❌ 创建 estimation_quality_metrics 表失败: {e}")
        return False

def verify_tables_created(client):
    """
    验证表是否成功创建
    """
    print("\n🔍 验证表创建情况...")
    
    tables_to_check = [
        'quarterly_data_filled_enhanced',
        'item_status_mapping', 
        'long_time_fundamental_na_list_detailed',
        'estimation_quality_metrics'
    ]
    
    created_tables = []
    
    for table_name in tables_to_check:
        try:
            result = client.execute(f"DESCRIBE {table_name}")
            field_count = len(result)
            created_tables.append((table_name, field_count))
            print(f"✅ {table_name}: {field_count}个字段")
        except Exception as e:
            print(f"❌ {table_name}: 验证失败 - {e}")
    
    return created_tables

def show_database_tables_summary(client):
    """
    显示数据库表汇总
    """
    print("\n📊 ap_research数据库表汇总...")
    
    try:
        # 获取所有表
        tables_result = client.execute("SHOW TABLES")
        all_tables = [table[0] for table in tables_result]
        
        print(f"数据库中的所有表 ({len(all_tables)}个):")
        for i, table_name in enumerate(sorted(all_tables), 1):
            print(f"  {i:2d}. {table_name}")
        
        # 分类显示
        core_tables = [
            'stock_performance_2020_2025_cumulative',
            'priority_quality_stock_hfq', 
            'priority_quality_fundamental_data_complete_deduped'
        ]
        
        new_tables = [
            'quarterly_data_filled_enhanced',
            'item_status_mapping',
            'long_time_fundamental_na_list_detailed', 
            'estimation_quality_metrics'
        ]
        
        old_tables = [
            'quarterly_data_filled',
            'long_time_fundamental_na_list'
        ]
        
        print(f"\n分类统计:")
        print(f"  📊 核心数据表: {len([t for t in core_tables if t in all_tables])}/{len(core_tables)}个")
        print(f"  🆕 新建功能表: {len([t for t in new_tables if t in all_tables])}/{len(new_tables)}个")
        print(f"  🔄 待清理旧表: {len([t for t in old_tables if t in all_tables])}/{len(old_tables)}个")
        
        return all_tables
        
    except Exception as e:
        print(f"❌ 获取表列表失败: {e}")
        return []

def main():
    """
    主函数
    """
    print("开始创建4个核心表到ap_research数据库...")
    
    try:
        # 1. 连接数据库
        client = connect_to_clickhouse()
        if not client:
            return
        
        # 2. 创建表
        success_count = 0
        
        # 表1: quarterly_data_filled_enhanced
        if create_quarterly_data_filled_enhanced(client):
            success_count += 1
        
        # 表2: item_status_mapping  
        if create_item_status_mapping(client):
            success_count += 1
        
        # 表3: long_time_fundamental_na_list_detailed
        if create_long_time_na_list_detailed(client):
            success_count += 1
        
        # 表4: estimation_quality_metrics
        if create_estimation_quality_metrics(client):
            success_count += 1
        
        # 3. 验证创建结果
        created_tables = verify_tables_created(client)
        
        # 4. 显示数据库汇总
        all_tables = show_database_tables_summary(client)
        
        print(f"\n=== 创建结果总结 ===")
        print(f"🎯 计划创建: 4个表")
        print(f"✅ 成功创建: {success_count}个表")
        print(f"📊 验证通过: {len(created_tables)}个表")
        
        if success_count == 4:
            print(f"\n🎉 所有4个核心表创建成功！")
            print(f"📋 quarterly_data_filled_enhanced: 主要填充表（25字段）")
            print(f"📋 item_status_mapping: 科目状态分析表（19字段）")
            print(f"📋 long_time_fundamental_na_list_detailed: 详细缺失记录表（16字段）")
            print(f"📋 estimation_quality_metrics: 质量评估表（22字段）")
            
            print(f"\n📈 ap_research数据库现在包含:")
            print(f"   • 3个核心数据表（已有）")
            print(f"   • 4个新建功能表（刚创建）")
            print(f"   • 2个旧版本表（待清理）")
            print(f"   • 总计: {len(all_tables)}个表")
        else:
            print(f"\n⚠️ 部分表创建失败，请检查错误信息")
        
    except Exception as e:
        print(f"❌ 创建过程中出错: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()

