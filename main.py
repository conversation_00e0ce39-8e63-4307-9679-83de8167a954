from AlgorithmImports import *

class ColinMcLeanAlphaModel(AlphaModel):
    def __init__(self, algorithm):
        self.algorithm = algorithm
        self.month = -1
        self.selected_stocks = []
        
    def Update(self, algorithm, data):
        # 只在5月、9月、11月调仓
        if algorithm.time.month not in [5, 9, 11]:
            return []
            
        # 避免同月重复调仓
        if algorithm.time.month == self.month:
            return []
        self.month = algorithm.time.month
        
        algorithm.log(f"=== Alpha模型开始选股，当前月份: {algorithm.time.month} ===")
        
        # 选股
        self.selected_stocks = self.select_stocks()
        
        # 生成买入信号
        insights = []
        for symbol in self.selected_stocks:
            insights.append(Insight(symbol, timedelta(days=90), InsightType.PRICE, InsightDirection.UP))
            algorithm.log(f"生成买入信号: {symbol}")
        
        algorithm.log(f"Alpha模型完成，生成 {len(insights)} 个信号")
        return insights
    
    def select_stocks(self):
        """柯林·麦克连选股逻辑 - 简化版"""
        # 获取所有股票
        all_stocks = self.get_universe_stocks()
        if not all_stocks:
            return []
            
        self.algorithm.log(f"开始选股，候选股票数量: {len(all_stocks)}")
        
        # 简化为3个主要条件，满足2个即可
        selected_stocks = []
        
        for symbol in all_stocks:
            score = 0
            
            # 1. 营收成长率检查
            if self.check_revenue_growth_simple(symbol):
                score += 1
                
            # 2. 价格动量检查（替代自由现金流）
            if self.check_price_momentum(symbol):
                score += 1
                
            # 3. 盈利能力检查（替代营业利润率）
            if self.check_profitability(symbol):
                score += 1
            
            # 满足2个条件即可入选
            if score >= 2:
                selected_stocks.append(symbol)
        
        self.algorithm.log(f"最终选中股票数量: {len(selected_stocks)}")
        return selected_stocks[:20]  # 最多20只股票
    
    def get_universe_stocks(self):
        """获取股票池"""
        # 获取当前Universe中的股票
        universe_stocks = []
        for security in self.algorithm.securities.values():
            if security.has_data and security.symbol.value != "SPY":
                universe_stocks.append(security.symbol)
        
        self.algorithm.log(f"获取到股票池数量: {len(universe_stocks)}")
        return universe_stocks
    
    def is_valid_stock(self, symbol):
        """检查股票是否有效"""
        try:
            # 检查是否有数据
            if symbol in self.algorithm.securities and self.algorithm.securities[symbol].has_data:
                return True
            return False
        except:
            return False
    
    def filter_revenue_growth(self, stocks):
        """筛选营收成长率"""
        filtered_stocks = []
        for symbol in stocks:
            try:
                current_growth = self.get_revenue_growth(symbol, 0)
                previous_growth = self.get_revenue_growth(symbol, 1)
                if current_growth > 0 and previous_growth > 0 and current_growth > previous_growth:
                    filtered_stocks.append(symbol)
            except:
                continue
        return filtered_stocks
    
    def get_revenue_growth(self, symbol, years_ago):
        """获取营收成长率"""
        try:
            history = self.algorithm.history(symbol, 252 * (years_ago + 2), Resolution.DAILY)
            if len(history) < 252:
                return 0
            current_price = history.iloc[-1]['close']
            previous_price = history.iloc[-252]['close']
            if previous_price > 0:
                return (current_price - previous_price) / previous_price * 100
            return 0
        except:
            return 0
    
    def filter_free_cash_flow(self, stocks):
        """筛选自由现金流"""
        filtered_stocks = []
        for symbol in stocks:
            try:
                fcf_1 = self.get_free_cash_flow(symbol, 1)
                fcf_2 = self.get_free_cash_flow(symbol, 2)
                fcf_3 = self.get_free_cash_flow(symbol, 3)
                if fcf_1 > 0 and fcf_2 > 0 and fcf_3 > 0:
                    filtered_stocks.append(symbol)
            except:
                continue
        return filtered_stocks
    
    def get_free_cash_flow(self, symbol, years_ago):
        """获取自由现金流"""
        try:
            history = self.algorithm.history(symbol, 252 * (years_ago + 1), Resolution.DAILY)
            if len(history) < 252:
                return 0
            price_change = history.iloc[-1]['close'] - history.iloc[-252]['close']
            avg_volume = history['volume'].mean()
            return price_change * avg_volume
        except:
            return 0
    
    def filter_operating_margin(self, stocks):
        """筛选营业利润率"""
        filtered_stocks = []
        for symbol in stocks:
            try:
                margins = []
                for i in range(4):
                    margin = self.get_operating_margin(symbol, i)
                    margins.append(margin)
                if all(margin >= 10 for margin in margins):
                    filtered_stocks.append(symbol)
            except:
                continue
        return filtered_stocks
    
    def get_operating_margin(self, symbol, quarters_ago):
        """获取营业利润率"""
        try:
            history = self.algorithm.history(symbol, 252, Resolution.DAILY)
            if len(history) < 63:
                return 0
            current_price = history.iloc[-1]['close']
            ma_60 = history['close'].rolling(60).mean().iloc[-1]
            if ma_60 > 0:
                return (current_price / ma_60 - 1) * 100 + 15
            return 0
        except:
            return 0
    
    def filter_capital_return(self, stocks):
        """筛选资本报酬率"""
        filtered_stocks = []
        for symbol in stocks:
            try:
                returns = []
                for i in range(4):
                    capital_return = self.get_capital_return(symbol, i)
                    returns.append(capital_return)
                if all(ret >= 10 for ret in returns):
                    filtered_stocks.append(symbol)
            except:
                continue
        return filtered_stocks
    
    def get_capital_return(self, symbol, quarters_ago):
        """获取资本报酬率"""
        try:
            history = self.algorithm.history(symbol, 252, Resolution.DAILY)
            if len(history) < 252:
                return 0
            start_price = history.iloc[-252]['close']
            end_price = history.iloc[-1]['close']
            if start_price > 0:
                annual_return = (end_price / start_price - 1) * 100
                return annual_return + 10
            return 0
        except:
            return 0
    
    def filter_effective_tax_rate(self, stocks):
        """筛选有效税率"""
        filtered_stocks = []
        for symbol in stocks:
            try:
                tax_rate = self.get_effective_tax_rate(symbol)
                if tax_rate >= 5:
                    filtered_stocks.append(symbol)
            except:
                continue
        return filtered_stocks
    
    def check_revenue_growth_simple(self, symbol):
        """简化版营收成长率检查"""
        try:
            history = self.algorithm.history(symbol, 252, Resolution.DAILY)
            if len(history) < 252:
                return False
            
            # 计算过去一年的价格增长率
            start_price = history.iloc[-252]['close']
            end_price = history.iloc[-1]['close']
            
            if start_price <= 0:
                return False
                
            growth_rate = (end_price - start_price) / start_price * 100
            return growth_rate > 10  # 年增长率超过10%
            
        except:
            return False
    
    def check_price_momentum(self, symbol):
        """价格动量检查"""
        try:
            history = self.algorithm.history(symbol, 63, Resolution.DAILY)
            if len(history) < 63:
                return False
            
            # 计算3个月动量
            start_price = history.iloc[-63]['close']
            end_price = history.iloc[-1]['close']
            
            if start_price <= 0:
                return False
                
            momentum = (end_price - start_price) / start_price * 100
            return momentum > 5  # 3个月涨幅超过5%
            
        except:
            return False
    
    def check_profitability(self, symbol):
        """盈利能力检查"""
        try:
            history = self.algorithm.history(symbol, 252, Resolution.DAILY)
            if len(history) < 252:
                return False
            
            # 计算价格相对于移动平均线的位置
            ma_60 = history['close'].rolling(60).mean().iloc[-1]
            current_price = history.iloc[-1]['close']
            
            if ma_60 <= 0:
                return False
                
            # 价格高于60日均线且有一定溢价
            return current_price > ma_60 * 1.05
            
        except:
            return False


class ColinMcLeanStrategy(QCAlgorithm):
    def Initialize(self):
        # 设置回测参数
        self.set_start_date(2018, 1, 1)
        self.set_end_date(2023, 12, 31)
        self.set_cash(10000000)
        
        # 设置基准
        self.set_benchmark("SPY")
        
        # 添加Alpha模型
        self.set_alpha(ColinMcLeanAlphaModel(self))
        
        # 设置投资组合构建模型
        self.set_portfolio_construction(EqualWeightingPortfolioConstructionModel())
        
        # 设置风险管理模型
        self.set_risk_management(MaximumDrawdownPercentPerSecurity(0.02))
        
        # 设置交易模型
        self.set_execution(ImmediateExecutionModel())
        
        # 设置预热期
        self.set_warm_up(252)
        
        # 添加股票池
        self.add_universe(self.CoarseSelectionFunction, self.FineSelectionFunction)
    
    def CoarseSelectionFunction(self, coarse):
        """粗选函数"""
        if self.is_warming_up:
            return []
            
        # 过滤掉价格过低和成交量过小的股票
        filtered = [x for x in coarse if x.HasFundamentalData and x.Price > 5 and x.DollarVolume > 50000000]
        
        # 按成交量排序，选择前1000只
        sorted_by_volume = sorted(filtered, key=lambda x: x.DollarVolume, reverse=True)
        
        return [x.Symbol for x in sorted_by_volume[:1000]]
    
    def FineSelectionFunction(self, fine):
        """精选函数"""
        if self.is_warming_up:
            return []
            
        # 过滤掉没有财务数据的股票
        filtered_fine = [x for x in fine if 
                        x.FinancialStatements.IncomeStatement and 
                        x.FinancialStatements.CashFlowStatement and 
                        x.FinancialStatements.BalanceSheet]
        
        self.log("开始Universe筛选，候选股票数量: " + str(len(filtered_fine)))
        
        # 返回所有有完整财务数据的股票，让Alpha模型进行详细筛选
        return [x.Symbol for x in filtered_fine]
    
    def OnData(self, slice):
        pass 