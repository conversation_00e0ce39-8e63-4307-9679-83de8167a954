import pandas as pd
from clickhouse_driver import Client

# 连接数据库
client = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 分析TSLA和META的CAGR因子缺失原因 ===")
print()

# 检查这两只股票的历史数据情况
target_stocks = ['TSLA', 'META']

for stock in target_stocks:
    print(f"🔍 分析 {stock}:")
    
    # 查询该股票的年报数据（使用正确的字段名）
    query = f"""
    SELECT 
        stock_symbol,
        financial_period_absolute,
        item_name,
        value,
        period_end_date
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = '{stock}'
      AND statement_type = 'Income Statement'
      AND item_name IN ('Free Cash Flow', 'Normalized Net Income - Bottom Line')
      AND substring(financial_period_absolute, 1, 2) = 'FY'
    ORDER BY financial_period_absolute, item_name
    """
    
    try:
        result = client.execute(query)
        
        if result:
            print(f"  📊 找到 {len(result)} 条年报数据")
            
            # 按年份和科目整理数据
            data_by_year = {}
            for row in result:
                symbol, period, item, value, date = row
                year = period[2:6]  # 提取年份
                
                if year not in data_by_year:
                    data_by_year[year] = {}
                data_by_year[year][item] = value
            
            print(f"  📅 数据年份范围: {min(data_by_year.keys())} - {max(data_by_year.keys())}")
            print(f"  📈 总年数: {len(data_by_year)} 年")
            
            # 检查FCF和Net Income数据完整性
            fcf_years = []
            net_income_years = []
            
            print(f"  📋 逐年数据详情:")
            for year in sorted(data_by_year.keys()):
                data = data_by_year[year]
                fcf = data.get('Free Cash Flow')
                net_income = data.get('Normalized Net Income - Bottom Line')
                
                if fcf is not None and fcf != 0:
                    fcf_years.append(year)
                if net_income is not None and net_income != 0:
                    net_income_years.append(year)
                
                fcf_str = f"{fcf:.2f}" if fcf is not None else "None"
                ni_str = f"{net_income:.2f}" if net_income is not None else "None"
                print(f"    {year}: FCF={fcf_str}, Net Income={ni_str}")
            
            print(f"  💰 有效FCF数据年份: {len(fcf_years)} 年 - {fcf_years}")
            print(f"  💼 有效Net Income数据年份: {len(net_income_years)} 年 - {net_income_years}")
            
            # 分析CAGR计算要求
            print(f"  🎯 CAGR因子要求分析:")
            print(f"    - fcf_cagr需要: ≥3年连续FCF数据")
            print(f"    - roic_cagr需要: ≥3年连续Net Income数据")
            
            # 检查数据连续性
            fcf_sufficient = len(fcf_years) >= 3
            roic_sufficient = len(net_income_years) >= 3
            
            print(f"    - {stock} FCF数据: {len(fcf_years)}年 ({'✅ 满足' if fcf_sufficient else '❌ 不足'})")
            print(f"    - {stock} ROIC数据: {len(net_income_years)}年 ({'✅ 满足' if roic_sufficient else '❌ 不足'})")
            
            # 如果数据不足，分析具体原因
            if not fcf_sufficient:
                print(f"    ❌ FCF CAGR缺失原因: 只有{len(fcf_years)}年数据，需要至少3年")
            if not roic_sufficient:
                print(f"    ❌ ROIC CAGR缺失原因: 只有{len(net_income_years)}年数据，需要至少3年")
                
        else:
            print(f"  ❌ 未找到 {stock} 的年报数据")
            
    except Exception as e:
        print(f"  ❌ 查询 {stock} 数据时出错: {e}")
    
    print()

print("=== 进一步检查ROIC计算所需的其他数据 ===")
print()

# 检查ROIC计算需要的其他字段
for stock in target_stocks:
    print(f"🔍 {stock} ROIC相关数据:")
    
    # ROIC = Net Income / (Total Debt + Total Shareholders' Equity)
    roic_query = f"""
    SELECT 
        financial_period_absolute,
        item_name,
        value
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = '{stock}'
      AND substring(financial_period_absolute, 1, 2) = 'FY'
      AND item_name IN (
          'Normalized Net Income - Bottom Line',
          'Debt - Total',
          'Shareholders'' Equity - Attributable to Parent Shareholders - Total'
      )
    ORDER BY financial_period_absolute, item_name
    """
    
    try:
        result = client.execute(roic_query)
        
        if result:
            # 按年份整理数据
            roic_data = {}
            for symbol, period, item, value in result:
                year = period[2:6]
                if year not in roic_data:
                    roic_data[year] = {}
                roic_data[year][item] = value
            
            print(f"  📊 ROIC计算数据:")
            valid_roic_years = []
            
            for year in sorted(roic_data.keys()):
                data = roic_data[year]
                net_income = data.get('Normalized Net Income - Bottom Line')
                debt = data.get('Debt - Total', 0) or 0
                equity = data.get("Shareholders' Equity - Attributable to Parent Shareholders - Total")
                
                # 检查是否可以计算ROIC
                if net_income is not None and equity is not None and equity != 0:
                    invested_capital = debt + equity
                    roic_value = (net_income / invested_capital) * 100 if invested_capital != 0 else None
                    valid_roic_years.append(year)
                    print(f"    {year}: Net Income={net_income:.2f}, Debt={debt:.2f}, Equity={equity:.2f}, ROIC={roic_value:.2f}%" if roic_value else f"    {year}: 无法计算ROIC")
                else:
                    missing = []
                    if net_income is None: missing.append("Net Income")
                    if equity is None or equity == 0: missing.append("Equity")
                    print(f"    {year}: 缺失 {', '.join(missing)}")
            
            print(f"  🎯 可计算ROIC的年份: {len(valid_roic_years)} 年 - {valid_roic_years}")
            print(f"  📈 ROIC CAGR可行性: {'✅ 可计算' if len(valid_roic_years) >= 3 else '❌ 数据不足'}")
            
    except Exception as e:
        print(f"  ❌ 查询失败: {e}")
    
    print()

client.disconnect()

print("=== 结论 ===")
print("CAGR因子缺失的主要原因:")
print("1. 需要至少3年的连续有效数据")
print("2. 数据值不能为None或0")
print("3. ROIC计算需要Net Income、Debt、Equity三个字段都有效")
print("4. 某些公司可能在早期年份数据不完整或数据质量问题")

