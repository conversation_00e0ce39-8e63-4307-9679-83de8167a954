from clickhouse_driver import Client
import random

# 连接到lseg数据库
client_lseg = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='lseg'
)

# 连接到ap_research数据库
client_ap = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 检查lseg中有但ap_research中没有的数据 ===")

# 1. 获取ap_research中的所有instrument列表
print("\n1. 获取ap_research中的instrument列表...")
query_ap_instruments = """
SELECT DISTINCT instrument
FROM priority_quality_fundamental_data_complete_deduped
ORDER BY instrument
"""

result_ap_instruments = client_ap.execute(query_ap_instruments)
ap_instruments = set(instrument for (instrument,) in result_ap_instruments)
print(f"  ap_research中有 {len(ap_instruments)} 个唯一instrument")

# 2. 检查lseg中有多少instrument不在ap_research中
print("\n2. 检查lseg中有多少instrument不在ap_research中...")

lseg_instruments = set()
tables = ['income_statement', 'balance_sheet_history', 'cash_flow']

for table in tables:
    print(f"  正在检查{table}...")
    query_lseg_instruments = f"""
    SELECT DISTINCT instrument
    FROM {table}
    WHERE financial_period_absolute != ''
    """
    
    try:
        result_lseg_instruments = client_lseg.execute(query_lseg_instruments)
        table_instruments = set(instrument for (instrument,) in result_lseg_instruments)
        lseg_instruments.update(table_instruments)
        print(f"    {table}中有 {len(table_instruments)} 个唯一instrument")
    except Exception as e:
        print(f"    查询{table}时出错: {e}")

print(f"  lseg总共有 {len(lseg_instruments)} 个唯一instrument")

# 找出差异
lseg_only_instruments = lseg_instruments - ap_instruments
ap_only_instruments = ap_instruments - lseg_instruments

print(f"\n  差异分析:")
print(f"    只在lseg中存在的instrument: {len(lseg_only_instruments)} 个")
print(f"    只在ap_research中存在的instrument: {len(ap_only_instruments)} 个")

if lseg_only_instruments:
    print(f"\n  只在lseg中存在的instrument样本（前20个）:")
    for i, instrument in enumerate(sorted(list(lseg_only_instruments))[:20]):
        print(f"    {i+1:2d}. {instrument}")
    if len(lseg_only_instruments) > 20:
        print(f"    ... 还有 {len(lseg_only_instruments) - 20} 个")

if ap_only_instruments:
    print(f"\n  只在ap_research中存在的instrument（这不应该发生）:")
    for instrument in sorted(list(ap_only_instruments)):
        print(f"    {instrument}")

# 3. 分析为什么这些instrument没有被提取
print(f"\n3. 分析为什么这些instrument没有被提取...")

# 检查这些instrument的stock_symbol模式
if lseg_only_instruments:
    sample_missing = list(lseg_only_instruments)[:10]  # 取前10个分析
    
    for instrument in sample_missing:
        print(f"\n  分析instrument: {instrument}")
        
        # 提取可能的stock_symbol
        if '.' in instrument:
            potential_symbol = instrument.split('.')[0]
        else:
            potential_symbol = instrument
        
        print(f"    潜在的stock_symbol: {potential_symbol}")
        
        # 检查这个symbol是否在我们的821个股票列表中
        query_check_symbol = f"""
        SELECT COUNT(*) as count
        FROM priority_quality_fundamental_data_complete_deduped
        WHERE stock_symbol = '{potential_symbol}'
        """
        
        result_check = client_ap.execute(query_check_symbol)
        count = result_check[0][0]
        
        if count > 0:
            print(f"    ✅ 该stock_symbol在ap_research中存在")
            
            # 查看这个symbol对应的instrument
            query_symbol_instrument = f"""
            SELECT DISTINCT instrument
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = '{potential_symbol}'
            """
            
            result_symbol_instrument = client_ap.execute(query_symbol_instrument)
            if result_symbol_instrument:
                actual_instrument = result_symbol_instrument[0][0]
                print(f"    在ap_research中使用的instrument: {actual_instrument}")
                if actual_instrument != instrument:
                    print(f"    ❗ instrument不匹配: lseg用{instrument}, ap_research用{actual_instrument}")
        else:
            print(f"    ❌ 该stock_symbol不在我们的821个股票列表中")
            
            # 检查在lseg中这个instrument有多少数据
            total_records = 0
            for table in tables:
                query_count = f"""
                SELECT COUNT(*) as count
                FROM {table}
                WHERE instrument = '{instrument}'
                """
                
                try:
                    result_count = client_lseg.execute(query_count)
                    table_count = result_count[0][0]
                    total_records += table_count
                    if table_count > 0:
                        print(f"      在{table}中有 {table_count} 条记录")
                except Exception as e:
                    continue
            
            print(f"    在lseg中总共有 {total_records} 条记录")

# 4. 检查我们的提取逻辑
print(f"\n4. 验证我们的提取逻辑...")

# 随机选择一个在ap_research中的instrument，验证提取是否完整
if ap_instruments:
    test_instrument = random.choice(list(ap_instruments))
    print(f"  测试instrument: {test_instrument}")
    
    # 在ap_research中的记录数
    query_ap_count = f"""
    SELECT COUNT(*) as count
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE instrument = '{test_instrument}'
    """
    
    result_ap_count = client_ap.execute(query_ap_count)
    ap_count = result_ap_count[0][0]
    print(f"    在ap_research中的记录数: {ap_count}")
    
    # 在lseg中的记录数
    lseg_total = 0
    for table in tables:
        query_lseg_count = f"""
        SELECT COUNT(*) as count
        FROM {table}
        WHERE instrument = '{test_instrument}'
        """
        
        try:
            result_lseg_count = client_lseg.execute(query_lseg_count)
            table_count = result_lseg_count[0][0]
            lseg_total += table_count
            if table_count > 0:
                print(f"    在{table}中的记录数: {table_count}")
        except Exception as e:
            continue
    
    print(f"    在lseg中的总记录数: {lseg_total}")
    
    if ap_count == lseg_total:
        print(f"    ✅ 记录数完全匹配")
    else:
        print(f"    ❗ 记录数不匹配，差异: {abs(ap_count - lseg_total)}")

print(f"\n=== 检查完成 ===")

