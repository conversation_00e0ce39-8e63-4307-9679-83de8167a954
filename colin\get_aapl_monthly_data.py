#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取AAPL最新一个月行情数据脚本
从ClickHouse数据库读取苹果公司最近一个月的价格数据
"""

from clickhouse_driver import Client
import pandas as pd
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def connect_to_database():
    """连接到ClickHouse数据库"""
    try:
        client = Client(
            host='************',
            port=9000,
            user='default',
            password='5ur2pK8WZQdy2',
            database='ap_research'
        )
        print("✅ 成功连接到ap_research数据库")
        return client
    except Exception as e:
        print(f"❌ 连接ap_research数据库失败: {e}")
        
        # 尝试连接lseg数据库
        try:
            client = Client(
                host='************',
                port=9000,
                user='default',
                password='5ur2pK8WZQdy2',
                database='lseg'
            )
            print("✅ 成功连接到lseg数据库")
            return client
        except Exception as e2:
            print(f"❌ 连接lseg数据库也失败: {e2}")
            return None

def get_aapl_monthly_data(client, database_name):
    """获取AAPL最新一个月的行情数据"""
    print(f"\n📊 从 {database_name} 数据库获取AAPL最新一个月行情数据...")
    
    # 计算一个月前的日期
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    print(f"📅 查询时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
    
    if database_name == 'ap_research':
        # 从ap_research数据库查询
        query = """
        SELECT 
            trade_date,
            stock_symbol,
            open,
            high,
            low,
            close,
            volume,
            turnover
        FROM priority_quality_stock_hfq
        WHERE stock_symbol = 'AAPL'
        AND trade_date >= %(start_date)s
        AND trade_date <= %(end_date)s
        ORDER BY trade_date DESC
        """
        
        result = client.execute(query, {
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d')
        })
        
        columns = ['trade_date', 'stock_symbol', 'open', 'high', 'low', 'close', 'volume', 'turnover']
        
    else:
        # 从lseg数据库查询
        query = """
        SELECT 
            trade_date,
            ric,
            open,
            high,
            low,
            close,
            volume,
            turnover
        FROM hfq_intrday_1_day
        WHERE splitByChar('.', ric)[1] = 'AAPL'
        AND trade_date >= %(start_date)s
        AND trade_date <= %(end_date)s
        ORDER BY trade_date DESC
        """
        
        result = client.execute(query, {
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d')
        })
        
        columns = ['trade_date', 'ric', 'open', 'high', 'low', 'close', 'volume', 'turnover']
    
    if not result:
        print("⚠️ 未找到AAPL的行情数据")
        return None
    
    # 转换为DataFrame
    df = pd.DataFrame(result, columns=columns)
    
    # 转换日期格式
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    
    # 计算日收益率
    df['daily_return'] = df['close'].pct_change() * 100
    
    # 计算累计收益率
    df['cumulative_return'] = ((df['close'] / df['close'].iloc[-1]) - 1) * 100
    
    print(f"✅ 获取到 {len(df)} 条AAPL行情数据")
    
    return df

def display_summary(df):
    """显示数据摘要"""
    if df is None or df.empty:
        return
    
    print("\n" + "="*80)
    print("📈 AAPL最新一个月行情数据摘要")
    print("="*80)
    
    # 基本信息
    print(f"📊 数据条数: {len(df):,}")
    print(f"📅 时间范围: {df['trade_date'].min().strftime('%Y-%m-%d')} 到 {df['trade_date'].max().strftime('%Y-%m-%d')}")
    
    # 价格统计
    latest_price = df['close'].iloc[0]
    highest_price = df['high'].max()
    lowest_price = df['low'].min()
    avg_price = df['close'].mean()
    
    print(f"\n💰 价格信息:")
    print(f"  最新收盘价: ${latest_price:.2f}")
    print(f"  期间最高价: ${highest_price:.2f}")
    print(f"  期间最低价: ${lowest_price:.2f}")
    print(f"  期间平均价: ${avg_price:.2f}")
    print(f"  价格波动: ${highest_price - lowest_price:.2f} ({(highest_price/lowest_price - 1)*100:.1f}%)")
    
    # 收益率统计
    total_return = df['daily_return'].sum()
    avg_daily_return = df['daily_return'].mean()
    volatility = df['daily_return'].std()
    
    print(f"\n📈 收益率信息:")
    print(f"  累计收益率: {total_return:.2f}%")
    print(f"  日均收益率: {avg_daily_return:.2f}%")
    print(f"  日收益率波动: {volatility:.2f}%")
    
    # 成交量统计
    total_volume = df['volume'].sum()
    avg_volume = df['volume'].mean()
    
    print(f"\n📊 成交量信息:")
    print(f"  总成交量: {total_volume:,.0f}")
    print(f"  日均成交量: {avg_volume:,.0f}")
    
    # 显示最近5个交易日数据
    print(f"\n📋 最近5个交易日详细数据:")
    print(df.head().to_string(index=False))

def main():
    """主函数"""
    print("🍎 AAPL最新一个月行情数据查询")
    print("="*50)
    
    # 连接数据库
    client = connect_to_database()
    if not client:
        print("❌ 无法连接到数据库")
        return
    
    # 获取数据库名称
    database_name = client.connection.database
    
    # 获取AAPL数据
    df = get_aapl_monthly_data(client, database_name)
    
    # 显示摘要
    display_summary(df)
    
    # 保存到CSV文件
    if df is not None and not df.empty:
        filename = f"aapl_monthly_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        df.to_csv(filename, index=False)
        print(f"\n💾 数据已保存到: {filename}")
    
    print("\n✅ 查询完成!")

if __name__ == "__main__":
    main()
