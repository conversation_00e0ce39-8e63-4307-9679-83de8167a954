"""
专门查找包含"shareholders"的所有科目
"""

from clickhouse_driver import Client

def connect_to_ap_research():
    """连接到ap_research数据库"""
    try:
        client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')
        print("✅ 连接ap_research数据库成功")
        return client
    except Exception as e:
        print(f"❌ 连接数据库失败: {e}")
        return None

def search_shareholders_items(client):
    """查找所有包含shareholders的科目"""
    print("\n=== 查找包含'shareholders'的所有科目 ===")
    
    # 查询包含shareholders的科目
    query = """
    SELECT 
        statement_type,
        item_name,
        COUNT(*) as frequency,
        COUNT(DISTINCT stock_symbol) as stock_count,
        MIN(substring(financial_period_absolute, 3, 4)) as earliest_year,
        MAX(substring(financial_period_absolute, 3, 4)) as latest_year
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE LOWER(item_name) LIKE '%shareholders%'
    GROUP BY statement_type, item_name
    ORDER BY frequency DESC
    """
    
    print("📊 执行查询...")
    result = client.execute(query)
    
    print(f"✅ 找到 {len(result)} 个包含'shareholders'的科目")
    
    if result:
        print("\n📋 所有包含'shareholders'的科目:")
        for i, row in enumerate(result, 1):
            statement_type, item_name, frequency, stock_count, earliest_year, latest_year = row
            print(f"{i:2d}. {item_name}")
            print(f"     报表类型: {statement_type}")
            print(f"     出现频次: {frequency:,}")
            print(f"     股票数量: {stock_count}")
            print(f"     时间跨度: {earliest_year}-{latest_year}")
            print()
    
    return result

def search_equity_items(client):
    """查找所有包含equity的科目"""
    print("\n=== 查找包含'equity'的所有科目 ===")
    
    # 查询包含equity的科目
    query = """
    SELECT 
        statement_type,
        item_name,
        COUNT(*) as frequency,
        COUNT(DISTINCT stock_symbol) as stock_count,
        MIN(substring(financial_period_absolute, 3, 4)) as earliest_year,
        MAX(substring(financial_period_absolute, 3, 4)) as latest_year
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE LOWER(item_name) LIKE '%equity%'
    GROUP BY statement_type, item_name
    ORDER BY frequency DESC
    """
    
    print("📊 执行查询...")
    result = client.execute(query)
    
    print(f"✅ 找到 {len(result)} 个包含'equity'的科目")
    
    if result:
        print("\n📋 所有包含'equity'的科目 (按频次排序):")
        for i, row in enumerate(result, 1):
            statement_type, item_name, frequency, stock_count, earliest_year, latest_year = row
            print(f"{i:2d}. {item_name}")
            print(f"     报表类型: {statement_type}")
            print(f"     出现频次: {frequency:,}")
            print(f"     股票数量: {stock_count}")
            print(f"     时间跨度: {earliest_year}-{latest_year}")
            print()
    
    return result

def search_capital_items(client):
    """查找所有包含capital的科目"""
    print("\n=== 查找包含'capital'的所有科目 ===")
    
    # 查询包含capital的科目
    query = """
    SELECT 
        statement_type,
        item_name,
        COUNT(*) as frequency,
        COUNT(DISTINCT stock_symbol) as stock_count,
        MIN(substring(financial_period_absolute, 3, 4)) as earliest_year,
        MAX(substring(financial_period_absolute, 3, 4)) as latest_year
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE LOWER(item_name) LIKE '%capital%'
    GROUP BY statement_type, item_name
    ORDER BY frequency DESC
    """
    
    print("📊 执行查询...")
    result = client.execute(query)
    
    print(f"✅ 找到 {len(result)} 个包含'capital'的科目")
    
    if result:
        print("\n📋 所有包含'capital'的科目 (按频次排序，显示前20个):")
        for i, row in enumerate(result[:20], 1):
            statement_type, item_name, frequency, stock_count, earliest_year, latest_year = row
            print(f"{i:2d}. {item_name}")
            print(f"     报表类型: {statement_type}")
            print(f"     出现频次: {frequency:,}")
            print(f"     股票数量: {stock_count}")
            print(f"     时间跨度: {earliest_year}-{latest_year}")
            print()
    
    return result

def main():
    """主函数"""
    print("=== 查找股东权益相关科目 ===")
    print("专门搜索shareholders、equity、capital相关科目")
    
    try:
        client = connect_to_ap_research()
        if not client:
            return
        
        # 1. 查找包含shareholders的科目
        shareholders_result = search_shareholders_items(client)
        
        # 2. 查找包含equity的科目
        equity_result = search_equity_items(client)
        
        # 3. 查找包含capital的科目
        capital_result = search_capital_items(client)
        
        # 4. 总结最佳候选字段
        print("\n" + "="*80)
        print("🎯 股东权益字段映射建议总结")
        print("="*80)
        
        print("\n💡 最佳候选字段 (基于覆盖率和语义匹配):")
        
        # 从结果中找出最佳候选
        best_candidates = []
        
        if shareholders_result:
            print("\n📊 Shareholders相关字段:")
            for row in shareholders_result:
                if row[2] > 40000:  # 频次大于40000的高质量字段
                    best_candidates.append(row)
                    print(f"   ✅ {row[1]} ({row[0]}): {row[2]:,}次, {row[3]}只股票")
        
        if equity_result:
            print("\n📊 Equity相关字段 (高频次):")
            for row in equity_result[:10]:  # 显示前10个最高频的
                if row[2] > 40000:
                    best_candidates.append(row)
                    print(f"   ✅ {row[1]} ({row[0]}): {row[2]:,}次, {row[3]}只股票")
        
        print(f"\n🔥 推荐用于ROCE/ROE计算的股东权益字段:")
        # 按频次排序并去重
        seen = set()
        for row in sorted(best_candidates, key=lambda x: x[2], reverse=True):
            if row[1] not in seen and 'equity' in row[1].lower():
                seen.add(row[1])
                print(f"   🎯 {row[1]}")
                print(f"      报表: {row[0]}, 频次: {row[2]:,}, 覆盖: {row[3]}只股票")
        
        client.disconnect()
        
    except Exception as e:
        print(f"❌ 查询过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

