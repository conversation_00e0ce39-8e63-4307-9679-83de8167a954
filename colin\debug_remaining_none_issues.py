#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试剩余的None和NaN问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from final_complete_24_factors_with_field_mapping import (
    connect_to_ap_research, 
    get_stock_data,
    get_price_data,
    get_field_with_priority
)
import pandas as pd

def debug_remaining_none_issues():
    """调试剩余的None和NaN问题"""
    print("🔍 调试剩余的None和NaN问题...")
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        print("❌ 无法连接到数据库")
        return
    
    try:
        test_stock = 'AAPL'
        calculation_date = '2024-01-15'
        
        print(f"📊 测试股票: {test_stock}")
        print(f"📅 计算日期: {calculation_date}")
        
        # 1. 检查价格数据
        print(f"\n🔍 检查价格数据...")
        price_data = get_price_data(client, test_stock, calculation_date)
        if price_data:
            print(f"   ✅ 价格数据存在: {price_data}")
        else:
            print(f"   ❌ 价格数据不存在 - 这会导致 valuation_bubble_signal 为None")
        
        # 2. 检查股本数据
        print(f"\n🔍 检查股本数据...")
        stock_data = get_stock_data(client, test_stock, calculation_date)
        if not stock_data.empty:
            pivot_data = stock_data.pivot_table(
                index=['financial_period_absolute', 'period_end_date', 'effective_date'],
                columns='item_name',
                values='value',
                aggfunc='first'
            ).reset_index()
            
            latest_data = pivot_data.iloc[-1]
            
            # 字段映射
            field_mappings = {
                'shares': [
                    'Common Shares - Outstanding - Total',
                    'Shares Outstanding - Diluted Average',
                    'Shares used to calculate Diluted EPS - Total'
                ]
            }
            
            shares, share_priority = get_field_with_priority(latest_data, field_mappings['shares'])
            if shares and shares > 0:
                print(f"   ✅ 股本数据存在: {shares} (优先级: {share_priority})")
            else:
                print(f"   ❌ 股本数据不存在或为0 - 这会导致多个因子为None")
        
        # 3. 检查专利密度NaN问题
        print(f"\n🔍 检查专利密度NaN问题...")
        
        # 检查无形资产数据
        intangible_fields = [
            'Intangible Assets - Total - Net',
            'Intangible Assets - excluding Goodwill - Net - Total'
        ]
        
        print(f"   检查无形资产字段:")
        for field in intangible_fields:
            if field in latest_data.index:
                value = latest_data[field]
                print(f"     {field}: {value}")
            else:
                print(f"     {field}: 不存在")
        
        # 检查总资产
        total_assets_fields = ['Total Assets']
        for field in total_assets_fields:
            if field in latest_data.index:
                value = latest_data[field]
                print(f"     {field}: {value}")
            else:
                print(f"     {field}: 不存在")
        
        # 4. 检查ROIC相关字段
        print(f"\n🔍 检查ROIC相关字段...")
        
        # 检查EBIT字段
        ebit_fields = [
            'Earnings before Interest & Taxes (EBIT)',
            'Operating Profit before Non-Recurring Income/Expense',
            'Operating Income (Loss)'
        ]
        
        print(f"   检查EBIT字段:")
        for field in ebit_fields:
            if field in latest_data.index:
                value = latest_data[field]
                print(f"     {field}: {value}")
            else:
                print(f"     {field}: 不存在")
        
        # 5. 检查税率相关字段
        print(f"\n🔍 检查税率相关字段...")
        
        tax_fields = [
            'Income Taxes',
            'Income before Income Taxes',
            'Income Tax Expense - Total'
        ]
        
        print(f"   检查税率字段:")
        for field in tax_fields:
            if field in latest_data.index:
                value = latest_data[field]
                print(f"     {field}: {value}")
            else:
                print(f"     {field}: 不存在")
        
        # 6. 分析具体的None原因
        print(f"\n📊 分析具体的None原因:")
        print("-" * 60)
        
        # valuation_bubble_signal
        if not price_data:
            print(f"   ❌ valuation_bubble_signal: 缺少价格数据")
        elif not shares or shares <= 0:
            print(f"   ❌ valuation_bubble_signal: 缺少股本数据")
        else:
            print(f"   ✅ valuation_bubble_signal: 基础数据完整")
        
        # roic
        ebit_exists = any(field in latest_data.index and pd.notna(latest_data[field]) for field in ebit_fields)
        if not ebit_exists:
            print(f"   ❌ roic: 缺少EBIT数据")
        else:
            print(f"   ✅ roic: EBIT数据存在")
        
        # effective_tax_rate
        tax_exists = any(field in latest_data.index and pd.notna(latest_data[field]) for field in tax_fields)
        if not tax_exists:
            print(f"   ❌ effective_tax_rate: 缺少税务数据")
        else:
            print(f"   ✅ effective_tax_rate: 税务数据存在")
        
        # patent_density
        intangible_exists = any(field in latest_data.index and pd.notna(latest_data[field]) for field in intangible_fields)
        if not intangible_exists:
            print(f"   ❌ patent_density: 缺少无形资产数据")
        else:
            intangible_value = None
            for field in intangible_fields:
                if field in latest_data.index and pd.notna(latest_data[field]):
                    intangible_value = latest_data[field]
                    break
            
            if intangible_value == 0:
                print(f"   ⚠️ patent_density: 无形资产为0，会导致NaN")
            else:
                print(f"   ✅ patent_density: 无形资产数据正常 ({intangible_value})")
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            client.disconnect()
            print("🔌 数据库连接已关闭")

if __name__ == "__main__":
    debug_remaining_none_issues()
