import pandas as pd
import numpy as np
import re

def list_all_factors_detailed():
    """详细列出所有26个因子的完整计算过程、参数、时间窗口和逻辑"""
    
    print("=== 所有26个因子的完整计算过程、参数、时间窗口和逻辑 ===")
    print()
    
    # 读取因子计算脚本
    try:
        with open('mclean_factors_combined.py', 'r', encoding='utf-8') as f:
            script_content = f.read()
    except FileNotFoundError:
        print("未找到因子计算脚本文件")
        return
    
    # 所有26个因子的完整信息
    all_factors = {
        # 第一组13个因子（技术相关）
        'tech_premium': {
            'description': '技术溢价系数',
            'calculation_logic': '毛利率 / 研发强度',
            'formula': 'gross_margin / rd_ratio',
            'time_window': '最新年度数据',
            'data_requirements': ['revenue', 'cost', 'rd_expense'],
            'code_location': '第284-290行',
            'current_implementation': 'gross_margin / rd_ratio',
            'calculation_steps': [
                '1. 计算毛利率: gross_margin = (revenue - cost) / revenue',
                '2. 计算研发强度: rd_ratio = rd_expense / revenue',
                '3. 计算技术溢价: tech_premium = gross_margin / rd_ratio'
            ],
            'parameters': {
                'revenue': 'Revenue from Business Activities - Total',
                'cost': 'Cost of Revenues - Total',
                'rd_expense': 'Research & Development Expense'
            }
        },
        'tech_gap_warning': {
            'description': '技术断层预警因子',
            'calculation_logic': '当前研发强度 vs 上年度研发强度',
            'formula': '1 if rd_ratio_decline > 0.2 else 0',
            'time_window': '需要2年数据',
            'data_requirements': ['rd_expense', 'revenue'],
            'code_location': '第304-312行',
            'current_implementation': '1 if rd_ratio_decline > 0.2 else 0',
            'calculation_steps': [
                '1. 计算当前研发强度: current_rd_ratio = current_rd_expense / current_revenue',
                '2. 计算上年度研发强度: prev_rd_ratio = prev_rd_expense / prev_revenue',
                '3. 计算研发强度下降: rd_ratio_decline = prev_rd_ratio - current_rd_ratio',
                '4. 判断预警: 1 if rd_ratio_decline > 0.2 else 0'
            ],
            'parameters': {
                'rd_expense': 'Research & Development Expense',
                'revenue': 'Revenue from Business Activities - Total',
                'threshold': 0.2
            }
        },
        'patent_density': {
            'description': '专利密度',
            'calculation_logic': '无形资产 / 总资产',
            'formula': 'intangible_assets / total_assets',
            'time_window': '最新年度数据',
            'data_requirements': ['intangible_assets', 'total_assets'],
            'code_location': '第320-324行',
            'current_implementation': 'intangible_assets / total_assets',
            'calculation_steps': [
                '1. 获取无形资产: intangible_assets',
                '2. 获取总资产: total_assets',
                '3. 计算专利密度: patent_density = intangible_assets / total_assets'
            ],
            'parameters': {
                'intangible_assets': 'Intangible Assets - Total - Net',
                'total_assets': 'Total Assets'
            }
        },
        'ecosystem_cash_ratio': {
            'description': '生态系统现金比率',
            'calculation_logic': '现金 / 总资产',
            'formula': 'cash / total_assets',
            'time_window': '最新年度数据',
            'data_requirements': ['cash', 'total_assets'],
            'code_location': '第332-336行',
            'current_implementation': '0.1 (硬编码)',
            'calculation_steps': [
                '1. 获取现金: cash',
                '2. 获取总资产: total_assets',
                '3. 计算现金比率: ecosystem_cash_ratio = cash / total_assets'
            ],
            'parameters': {
                'cash': 'Cash & Cash Equivalents - Total',
                'total_assets': 'Total Assets'
            }
        },
        'adjusted_roce': {
            'description': '调整后ROCE',
            'calculation_logic': 'EBIT / (总资产 - 现金)',
            'formula': 'operating_profit / denominator',
            'time_window': '最新年度数据',
            'data_requirements': ['operating_profit', 'total_assets', 'cash'],
            'code_location': '第350-356行',
            'current_implementation': 'operating_profit / denominator',
            'calculation_steps': [
                '1. 获取营业利润: operating_profit',
                '2. 计算分母: denominator = total_assets - cash',
                '3. 计算调整后ROCE: adjusted_roce = operating_profit / denominator'
            ],
            'parameters': {
                'operating_profit': 'Operating Profit before Non-Recurring Income/Expense',
                'total_assets': 'Total Assets',
                'cash': 'Cash & Cash Equivalents - Total'
            }
        },
        'fcf_quality': {
            'description': '自由现金流质量',
            'calculation_logic': 'FCF / 净利润',
            'formula': 'fcf / net_income',
            'time_window': '最新年度数据',
            'data_requirements': ['fcf', 'net_income'],
            'code_location': '第373-377行',
            'current_implementation': 'cash_flow_quality_count (计数)',
            'calculation_steps': [
                '1. 计算FCF: fcf = operating_cf - capex',
                '2. 获取净利润: net_income',
                '3. 计算FCF质量: fcf_quality = fcf / net_income'
            ],
            'parameters': {
                'operating_cf': 'Net Cash Flow from Operating Activities',
                'capex': 'Capital Expenditures - Total',
                'net_income': 'Net Income - Basic - including Extraordinary Items Applicable to Common - Total'
            }
        },
        'dynamic_safety_margin': {
            'description': '动态安全边际',
            'calculation_logic': '基于多个财务指标的综合评分',
            'formula': '1.0 / (tech_premium * ecosystem_coef)',
            'time_window': '最新年度数据',
            'data_requirements': ['tech_premium', 'ecosystem_cash_ratio'],
            'code_location': '第389-393行',
            'current_implementation': '1.0 / (tech_premium * ecosystem_coef)',
            'calculation_steps': [
                '1. 获取技术溢价: tech_premium',
                '2. 获取生态系统系数: ecosystem_coef = ecosystem_cash_ratio',
                '3. 计算动态安全边际: dynamic_safety_margin = 1.0 / (tech_premium * ecosystem_coef)'
            ],
            'parameters': {
                'tech_premium': 'tech_premium因子值',
                'ecosystem_coef': 'ecosystem_cash_ratio因子值'
            }
        },
        'revenue_growth_continuity': {
            'description': '营收增长连续性',
            'calculation_logic': '基于历史营收增长趋势',
            'formula': '1 if growth_rate > 0 else 0',
            'time_window': '需要历史数据',
            'data_requirements': ['historical_revenue'],
            'code_location': '第403-409行',
            'current_implementation': '1 if growth_rate > 0 else 0',
            'calculation_steps': [
                '1. 计算历史营收增长率: growth_rate',
                '2. 判断连续性: 1 if growth_rate > 0 else 0'
            ],
            'parameters': {
                'revenue': 'Revenue from Business Activities - Total',
                'threshold': 0
            }
        },
        'effective_tax_rate_improvement': {
            'description': '有效税率改善',
            'calculation_logic': '税率变化趋势',
            'formula': '1 if effective_tax_rate < 0.25 else 0',
            'time_window': '需要历史数据',
            'data_requirements': ['historical_tax_rates'],
            'code_location': '第418-422行',
            'current_implementation': '1 if effective_tax_rate < 0.25 else 0',
            'calculation_steps': [
                '1. 计算有效税率: effective_tax_rate = income_taxes_paid / pretax_income',
                '2. 判断改善: 1 if effective_tax_rate < 0.25 else 0'
            ],
            'parameters': {
                'income_taxes_paid': 'Income Taxes - Paid/(Reimbursed) - Cash Flow',
                'pretax_income': 'Income before Taxes',
                'threshold': 0.25
            }
        },
        'financial_health': {
            'description': '财务健康度',
            'calculation_logic': '综合财务健康评分',
            'formula': '1 if (debt_ebitda_ratio < 3) and (cash_liability_ratio > 0.15) else 0',
            'time_window': '最新年度数据',
            'data_requirements': ['debt_ebitda_ratio', 'cash_liability_ratio'],
            'code_location': '第438-442行',
            'current_implementation': '1 if (debt_ebitda_ratio < 3) and (cash_liability_ratio > 0.15) else 0',
            'calculation_steps': [
                '1. 计算债务EBITDA比率: debt_ebitda_ratio = debt / ebitda',
                '2. 计算现金负债比率: cash_liability_ratio = cash / total_liabilities',
                '3. 判断财务健康: 1 if (debt_ebitda_ratio < 3) and (cash_liability_ratio > 0.15) else 0'
            ],
            'parameters': {
                'debt': 'Debt - Total',
                'ebitda': 'Earnings before Interest, Taxes, Depreciation & Amortization (EBITDA)',
                'cash': 'Cash & Cash Equivalents - Total',
                'total_liabilities': 'Total Liabilities',
                'debt_threshold': 3,
                'cash_threshold': 0.15
            }
        },
        'valuation_bubble_signal': {
            'description': '估值泡沫信号',
            'calculation_logic': '基于估值的风险信号',
            'formula': 'bubble_detection_algorithm',
            'time_window': '最新年度数据',
            'data_requirements': ['valuation_metrics'],
            'code_location': '第445行',
            'current_implementation': '0 (硬编码)',
            'calculation_steps': [
                '1. 硬编码返回0'
            ],
            'parameters': {
                'hardcoded_value': 0
            }
        },
        'rd_intensity': {
            'description': '研发强度',
            'calculation_logic': '研发费用 / 营收',
            'formula': 'rd_expense / revenue',
            'time_window': '最新年度数据',
            'data_requirements': ['rd_expense', 'revenue'],
            'code_location': '第453-457行',
            'current_implementation': 'rd_expense / revenue',
            'calculation_steps': [
                '1. 获取研发费用: rd_expense',
                '2. 获取营收: revenue',
                '3. 计算研发强度: rd_intensity = rd_expense / revenue'
            ],
            'parameters': {
                'rd_expense': 'Research & Development Expense',
                'revenue': 'Revenue from Business Activities - Total'
            }
        },
        'roce_stability': {
            'description': 'ROCE稳定性',
            'calculation_logic': 'ROCE的标准差',
            'formula': 'roce_mean / roce_std',
            'time_window': '需要历史数据',
            'data_requirements': ['historical_roce'],
            'code_location': '第477-483行',
            'current_implementation': 'roce_mean / roce_std',
            'calculation_steps': [
                '1. 计算历史ROCE值',
                '2. 计算ROCE均值: roce_mean',
                '3. 计算ROCE标准差: roce_std',
                '4. 计算稳定性: roce_stability = roce_mean / roce_std'
            ],
            'parameters': {
                'roce_values': '历史ROCE数据',
                'min_periods': 3
            }
        },
        
        # 第二组13个因子（财务相关）
        'revenue_yoy': {
            'description': '营收同比增长',
            'calculation_logic': '(当前年度 / 上一年度 - 1) * 100',
            'formula': '(revenue / prev_revenue - 1) * 100',
            'time_window': '需要2年数据',
            'data_requirements': ['current_revenue', 'prev_revenue'],
            'code_location': '第493-499行',
            'current_implementation': '使用5年前对比 (iloc[-5])',
            'calculation_steps': [
                '1. 获取当前营收: revenue = stock_data.iloc[-1]',
                '2. 获取5年前营收: prev_revenue = stock_data.iloc[-5]',
                '3. 计算同比增长: revenue_yoy = (revenue / prev_revenue - 1) * 100'
            ],
            'parameters': {
                'revenue': 'Revenue from Business Activities - Total',
                'current_index': -1,
                'prev_index': -5
            }
        },
        'revenue_cagr': {
            'description': '营收复合增长率',
            'calculation_logic': '((当前营收 / 8年前营收) ^ (1/8) - 1) * 100',
            'formula': '((current_revenue / past_revenue) ^ (1/8) - 1) * 100',
            'time_window': '需要8年数据',
            'data_requirements': ['current_revenue', '8_year_ago_revenue'],
            'code_location': '第508-514行',
            'current_implementation': '使用(1/2)而不是(1/8)',
            'calculation_steps': [
                '1. 获取当前营收: current_revenue = stock_data.iloc[-1]',
                '2. 获取8年前营收: past_revenue = stock_data.iloc[-8]',
                '3. 计算CAGR: revenue_cagr = ((current_revenue / past_revenue) ** (1/2) - 1) * 100'
            ],
            'parameters': {
                'revenue': 'Revenue from Business Activities - Total',
                'current_index': -1,
                'past_index': -8,
                'years': 2
            }
        },
        'net_income_yoy': {
            'description': '净利润同比增长',
            'calculation_logic': '(当前年度 / 上一年度 - 1) * 100',
            'formula': '(net_income / prev_net_income - 1) * 100',
            'time_window': '需要2年数据',
            'data_requirements': ['current_net_income', 'prev_net_income'],
            'code_location': '第522-528行',
            'current_implementation': '使用5年前对比 (iloc[-5])',
            'calculation_steps': [
                '1. 获取当前净利润: net_income = stock_data.iloc[-1]',
                '2. 获取5年前净利润: prev_net_income = stock_data.iloc[-5]',
                '3. 计算同比增长: net_income_yoy = (net_income / prev_net_income - 1) * 100'
            ],
            'parameters': {
                'net_income': 'Net Income - Basic - including Extraordinary Items Applicable to Common - Total',
                'current_index': -1,
                'prev_index': -5
            }
        },
        'profit_revenue_ratio': {
            'description': '盈利营收增长比',
            'calculation_logic': '净利润增长率 / 营收增长率',
            'formula': 'net_income_yoy / revenue_yoy',
            'time_window': '需要2年数据',
            'data_requirements': ['net_income_yoy', 'revenue_yoy'],
            'code_location': '第533-540行',
            'current_implementation': '依赖错误的YoY计算',
            'calculation_steps': [
                '1. 获取净利润增长率: net_income_yoy',
                '2. 获取营收增长率: revenue_yoy',
                '3. 计算比率: profit_revenue_ratio = net_income_yoy / revenue_yoy'
            ],
            'parameters': {
                'net_income_yoy': 'net_income_yoy因子值',
                'revenue_yoy': 'revenue_yoy因子值'
            }
        },
        'fcf_per_share': {
            'description': '每股自由现金流',
            'calculation_logic': '(经营现金流 - 资本支出) / 股数',
            'formula': '(operating_cf - capex) / shares',
            'time_window': '最新年度数据',
            'data_requirements': ['operating_cf', 'capex', 'shares'],
            'code_location': '第549-553行',
            'current_implementation': '(operating_cf - capex) / shares',
            'calculation_steps': [
                '1. 获取经营现金流: operating_cf',
                '2. 获取资本支出: capex',
                '3. 获取股数: shares',
                '4. 计算每股FCF: fcf_per_share = (operating_cf - capex) / shares'
            ],
            'parameters': {
                'operating_cf': 'Net Cash Flow from Operating Activities',
                'capex': 'Capital Expenditures - Total',
                'shares': 'Common Shares - Outstanding - Total'
            }
        },
        'fcf_cagr': {
            'description': 'FCF复合增长率',
            'calculation_logic': '((当前FCF / 8年前FCF) ^ (1/8) - 1) * 100',
            'formula': '((current_fcf / past_fcf) ^ (1/8) - 1) * 100',
            'time_window': '需要8年数据',
            'data_requirements': ['current_fcf', '8_year_ago_fcf'],
            'code_location': '第562-568行',
            'current_implementation': '使用(1/2)而不是(1/8)',
            'calculation_steps': [
                '1. 计算当前FCF: current_fcf = operating_cf - capex',
                '2. 计算8年前FCF: past_fcf = past_operating_cf - past_capex',
                '3. 计算CAGR: fcf_cagr = ((current_fcf / past_fcf) ** (1/2) - 1) * 100'
            ],
            'parameters': {
                'operating_cf': 'Net Cash Flow from Operating Activities',
                'capex': 'Capital Expenditures - Total',
                'current_index': -1,
                'past_index': -8,
                'years': 2
            }
        },
        'fcf_net_income_ratio': {
            'description': 'FCF与净利润比值',
            'calculation_logic': '每股FCF / 每股净利润',
            'formula': 'fcf_per_share / net_income_per_share',
            'time_window': '最新年度数据',
            'data_requirements': ['fcf_per_share', 'net_income_per_share'],
            'code_location': '第575-581行',
            'current_implementation': 'fcf_per_share / net_income_per_share',
            'calculation_steps': [
                '1. 获取每股FCF: fcf_per_share',
                '2. 计算每股净利润: net_income_per_share = net_income / shares',
                '3. 计算比值: fcf_net_income_ratio = fcf_per_share / net_income_per_share'
            ],
            'parameters': {
                'fcf_per_share': 'fcf_per_share因子值',
                'net_income': 'Net Income - Basic - including Extraordinary Items Applicable to Common - Total',
                'shares': 'Common Shares - Outstanding - Total'
            }
        },
        'operating_margin': {
            'description': '营业利润率',
            'calculation_logic': '营业利润 / 营收',
            'formula': 'ebit / revenue * 100',
            'time_window': '最新年度数据',
            'data_requirements': ['ebit', 'revenue'],
            'code_location': '第588-592行',
            'current_implementation': 'ebit / revenue * 100',
            'calculation_steps': [
                '1. 获取EBIT: ebit',
                '2. 获取营收: revenue',
                '3. 计算营业利润率: operating_margin = ebit / revenue * 100'
            ],
            'parameters': {
                'ebit': 'Earnings before Interest & Taxes (EBIT)',
                'revenue': 'Revenue from Business Activities - Total'
            }
        },
        'operating_margin_std': {
            'description': '营业利润率稳定性',
            'calculation_logic': '营业利润率的标准差',
            'formula': 'std(operating_margin_values) * 100',
            'time_window': '需要历史数据',
            'data_requirements': ['historical_operating_margins'],
            'code_location': '第605-611行',
            'current_implementation': 'np.std(margins) * 100',
            'calculation_steps': [
                '1. 计算历史营业利润率: margins',
                '2. 计算标准差: operating_margin_std = np.std(margins) * 100'
            ],
            'parameters': {
                'ebit': 'Earnings before Interest & Taxes (EBIT)',
                'revenue': 'Revenue from Business Activities - Total',
                'min_periods': 4
            }
        },
        'roic': {
            'description': '投资回报率',
            'calculation_logic': 'EBIT / (总资产 - 现金)',
            'formula': 'nopat / invested_capital * 100',
            'time_window': '最新年度数据',
            'data_requirements': ['ebit', 'total_assets', 'cash'],
            'code_location': '第628-634行',
            'current_implementation': 'nopat / invested_capital * 100',
            'calculation_steps': [
                '1. 计算有效税率: effective_tax_rate = tax_expense / pretax_income',
                '2. 计算NOPAT: nopat = ebit * (1 - effective_tax_rate)',
                '3. 计算投入资本: invested_capital = total_assets - cash',
                '4. 计算ROIC: roic = nopat / invested_capital * 100'
            ],
            'parameters': {
                'ebit': 'Earnings before Interest & Taxes (EBIT)',
                'tax_expense': 'Income Taxes',
                'pretax_income': 'Income before Taxes',
                'total_assets': 'Total Assets',
                'cash': 'Cash & Cash Equivalents - Total'
            }
        },
        'roic_cagr': {
            'description': 'ROIC复合增长率',
            'calculation_logic': '((当前ROIC / 8年前ROIC) ^ (1/8) - 1) * 100',
            'formula': '((current_roic / past_roic) ^ (1/8) - 1) * 100',
            'time_window': '需要8年数据',
            'data_requirements': ['current_roic', '8_year_ago_roic'],
            'code_location': '第639-643行',
            'current_implementation': 'roic * 0.1 (简化处理)',
            'calculation_steps': [
                '1. 获取当前ROIC: roic',
                '2. 简化计算: roic_cagr = roic * 0.1'
            ],
            'parameters': {
                'roic': 'roic因子值',
                'multiplier': 0.1
            }
        },
        'effective_tax_rate': {
            'description': '有效税率',
            'calculation_logic': '所得税 / 税前利润',
            'formula': 'tax_expense / ebit * 100',
            'time_window': '最新年度数据',
            'data_requirements': ['tax_expense', 'ebit'],
            'code_location': '第650-654行',
            'current_implementation': 'tax_expense / ebit * 100',
            'calculation_steps': [
                '1. 获取所得税: tax_expense',
                '2. 获取EBIT: ebit',
                '3. 计算有效税率: effective_tax_rate = tax_expense / ebit * 100'
            ],
            'parameters': {
                'tax_expense': 'Income Taxes',
                'ebit': 'Earnings before Interest & Taxes (EBIT)'
            }
        },
        'effective_tax_rate_std': {
            'description': '有效税率稳定性',
            'calculation_logic': '有效税率的标准差',
            'formula': 'std(effective_tax_rate_values) * 100',
            'time_window': '需要历史数据',
            'data_requirements': ['historical_tax_rates'],
            'code_location': '第667-673行',
            'current_implementation': 'np.std(tax_rates) * 100',
            'calculation_steps': [
                '1. 计算历史有效税率: tax_rates',
                '2. 计算标准差: effective_tax_rate_std = np.std(tax_rates) * 100'
            ],
            'parameters': {
                'tax_expense': 'Income Taxes',
                'ebit': 'Earnings before Interest & Taxes (EBIT)',
                'min_periods': 4
            }
        }
    }
    
    print(f"=== 总共26个因子 ===")
    print()
    
    # 逐个因子详细列出
    for i, (factor_name, factor_info) in enumerate(all_factors.items(), 1):
        print(f"=== {i:2d}. {factor_name} ===")
        print(f"描述: {factor_info['description']}")
        print(f"计算逻辑: {factor_info['calculation_logic']}")
        print(f"公式: {factor_info['formula']}")
        print(f"时间窗口: {factor_info['time_window']}")
        print(f"数据要求: {', '.join(factor_info['data_requirements'])}")
        print(f"代码位置: {factor_info['code_location']}")
        print(f"当前实现: {factor_info['current_implementation']}")
        print("计算步骤:")
        for step in factor_info['calculation_steps']:
            print(f"  {step}")
        print("参数:")
        for param, value in factor_info['parameters'].items():
            print(f"  {param}: {value}")
        print()

if __name__ == "__main__":
    list_all_factors_detailed() 