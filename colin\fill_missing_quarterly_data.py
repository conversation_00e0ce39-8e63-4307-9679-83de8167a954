from clickhouse_driver import Client
import pandas as pd
from datetime import datetime, timedelta

# 连接到数据库
client_ap = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 缺失季报数据补充机制 ===")

def identify_missing_quarterly_data():
    """
    识别有缺失季报数据的股票和具体缺失的期间
    """
    print("\n1. 识别缺失季报数据的股票...")
    
    # 我们已知的缺失情况（基于之前的分析）
    missing_stocks_periods = {
        'CCEP': ['FY2023Q1', 'FY2023Q2', 'FY2024Q1', 'FY2024Q2'],
        'POST': ['FY2023Q1', 'FY2023Q2', 'FY2023Q3', 'FY2024Q1', 'FY2024Q2', 'FY2024Q3'],
        'MRP': ['FY2023Q1', 'FY2023Q2'],
        'VG': ['FY2023Q1', 'FY2023Q2'],
        'SIG': ['FY2023Q1'],
        'AU': ['FY2024Q1', 'FY2024Q2']
    }
    
    print(f"  发现 {len(missing_stocks_periods)} 只股票有缺失季报数据:")
    for stock, periods in missing_stocks_periods.items():
        print(f"    {stock}: 缺失 {len(periods)} 个季度 - {', '.join(periods)}")
    
    return missing_stocks_periods

def get_available_data_for_estimation(stock_symbol, missing_period):
    """
    获取用于估算的可用历史数据
    """
    # 提取年份和季度
    year = int(missing_period[2:6])
    quarter = int(missing_period[7])
    
    # 确定用于滚动平滑的历史期间（前3个季度）
    source_periods = []
    
    for i in range(1, 4):  # 前3个季度
        if quarter - i > 0:
            # 同一年的前几个季度
            source_period = f"FY{year}Q{quarter - i}"
        else:
            # 跨年的季度
            prev_year = year - 1
            prev_quarter = 4 + (quarter - i)
            source_period = f"FY{prev_year}Q{prev_quarter}"
        source_periods.append(source_period)
    
    source_periods.reverse()  # 按时间顺序排列
    
    # 查询这些期间的数据
    periods_str = "', '".join(source_periods)
    
    query = f"""
    SELECT 
        financial_period_absolute,
        item_name,
        statement_type,
        value
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = '{stock_symbol}'
      AND financial_period_absolute IN ('{periods_str}')
      AND value IS NOT NULL
    ORDER BY financial_period_absolute, statement_type, item_name
    """
    
    try:
        result = client_ap.execute(query)
        df = pd.DataFrame(result, columns=['period', 'item_name', 'statement_type', 'value'])
        return df, source_periods
    except Exception as e:
        print(f"    查询{stock_symbol}历史数据时出错: {e}")
        return pd.DataFrame(), source_periods

def calculate_rolling_average_estimates(historical_data, missing_period, source_periods):
    """
    使用滚动平滑法计算估算值
    """
    estimates = []
    
    # 按会计科目分组计算
    for (item_name, statement_type), group in historical_data.groupby(['item_name', 'statement_type']):
        # 计算可用期间的平均值
        if len(group) >= 2:  # 至少需要2个期间的数据
            avg_value = group['value'].mean()
            
            estimates.append({
                'stock_symbol': missing_period.split('|')[0] if '|' in missing_period else 'Unknown',
                'financial_period_absolute': missing_period,
                'statement_type': statement_type,
                'item_name': item_name,
                'original_value': None,
                'data_status': 'estimated',
                'fill_method': 'rolling_average',
                'source_periods': ','.join(source_periods),
                'filled_simple_estimates': round(avg_value, 2),
                'corrected_filled_simple_estimates': None,
                'estimated_effective_date': datetime.now().date(),
                'confidence_score': min(0.7 + (len(group) - 2) * 0.1, 0.9),  # 更多数据=更高置信度
                'notes': f'Estimated using {len(group)} quarters rolling average'
            })
    
    return estimates

def check_available_corrections(stock_symbol, missing_period):
    """
    检查是否有可用的半年报或年报数据进行修正
    """
    year = int(missing_period[2:6])
    quarter = int(missing_period[7])
    
    # 检查半年报数据
    half_year_periods = []
    if quarter <= 2:
        half_year_periods.append(f'FY{year}H1')
    if quarter >= 3:
        half_year_periods.append(f'FY{year}H2')
    
    # 检查年报数据
    annual_period = f'FY{year}'
    
    correction_data = {}
    
    # 查询半年报数据
    for h_period in half_year_periods:
        query_half = f"""
        SELECT COUNT(*) as count
        FROM priority_quality_fundamental_data_complete_deduped
        WHERE stock_symbol = '{stock_symbol}'
          AND financial_period_absolute = '{h_period}'
        """
        
        try:
            result = client_ap.execute(query_half)
            if result[0][0] > 0:
                correction_data[h_period] = 'half_year_split'
        except:
            continue
    
    # 查询年报数据
    query_annual = f"""
    SELECT COUNT(*) as count
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = '{stock_symbol}'
      AND financial_period_absolute = '{annual_period}'
    """
    
    try:
        result = client_ap.execute(query_annual)
        if result[0][0] > 0:
            correction_data[annual_period] = 'annual_split'
    except:
        pass
    
    return correction_data

def create_quarterly_filling_table():
    """
    创建季报数据补充表
    """
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS quarterly_data_filled (
        id String,
        stock_symbol String,
        financial_period_absolute String,
        statement_type String,
        item_name String,
        original_value Nullable(Float64),
        
        data_status String,
        fill_method Nullable(String),
        source_periods Nullable(String),
        
        filled_simple_estimates Nullable(Float64),
        corrected_filled_simple_estimates Nullable(Float64),
        
        original_effective_date Nullable(Date),
        estimated_effective_date Nullable(Date),
        corrected_effective_date Nullable(Date),
        
        created_at DateTime,
        updated_at DateTime,
        confidence_score Nullable(Float64),
        notes Nullable(String)
    )
    ENGINE = MergeTree()
    ORDER BY (stock_symbol, financial_period_absolute, statement_type, item_name)
    PARTITION BY stock_symbol
    """
    
    try:
        client_ap.execute(create_table_sql)
        print("  ✅ 季报补充表创建成功")
    except Exception as e:
        print(f"  ❌ 创建表时出错: {e}")

def process_missing_data():
    """
    处理所有缺失的季报数据
    """
    print("\n2. 开始处理缺失数据...")
    
    # 创建补充表
    create_quarterly_filling_table()
    
    # 获取缺失数据列表
    missing_data = identify_missing_quarterly_data()
    
    total_estimates = 0
    total_corrections = 0
    
    for stock_symbol, missing_periods in missing_data.items():
        print(f"\n  处理股票: {stock_symbol}")
        
        for missing_period in missing_periods:
            print(f"    处理缺失期间: {missing_period}")
            
            # 获取历史数据用于估算
            historical_data, source_periods = get_available_data_for_estimation(stock_symbol, missing_period)
            
            if len(historical_data) > 0:
                # 计算滚动平均估算
                estimates = calculate_rolling_average_estimates(historical_data, missing_period, source_periods)
                
                print(f"      生成 {len(estimates)} 个估算值")
                total_estimates += len(estimates)
                
                # 检查是否有可用的修正数据
                correction_data = check_available_corrections(stock_symbol, missing_period)
                
                if correction_data:
                    print(f"      发现可用修正数据: {list(correction_data.keys())}")
                    total_corrections += len(correction_data)
                
                # 这里可以插入数据到表中（示例中只显示统计）
                
            else:
                print(f"      ⚠️ 没有足够的历史数据进行估算")
    
    print(f"\n3. 处理完成统计:")
    print(f"  总估算记录数: {total_estimates:,}")
    print(f"  可用修正数据: {total_corrections}")
    
    return total_estimates, total_corrections

# 主函数
def main():
    """
    主处理流程
    """
    print("开始缺失季报数据补充流程...")
    
    try:
        estimates, corrections = process_missing_data()
        
        print(f"\n=== 处理结果 ===")
        print(f"✅ 成功生成 {estimates:,} 个估算值")
        print(f"✅ 发现 {corrections} 个可用修正数据源")
        print(f"📊 涉及 6 只股票的缺失季报数据")
        
        print(f"\n下一步建议:")
        print(f"1. 执行实际的数据插入操作")
        print(f"2. 实现半年报/年报的自动修正逻辑")
        print(f"3. 建立定期更新机制")
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")

if __name__ == "__main__":
    main()

