#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取AAPL当前行情价格脚本
从ClickHouse数据库读取苹果公司的最新价格数据
"""

from clickhouse_driver import Client
import pandas as pd
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def connect_to_database():
    """连接到ClickHouse数据库"""
    try:
        client = Client(
            host='************',
            port=9000,
            user='default',
            password='5ur2pK8VZQdy2',
            database='ap_research'
        )
        print("✅ 成功连接到ap_research数据库")
        return client
    except Exception as e:
        print(f"❌ 连接ap_research数据库失败: {e}")
        
        # 尝试连接lseg数据库
        try:
            client = Client(
                host='************',
                port=9000,
                user='default',
                password='5ur2pK8VZQdy2',
                database='lseg'
            )
            print("✅ 成功连接到lseg数据库")
            return client
        except Exception as e2:
            print(f"❌ 连接lseg数据库也失败: {e2}")
            return None

def get_aapl_latest_price(client, database_name):
    """获取AAPL的最新价格数据"""
    print(f"\n🔍 从 {database_name} 数据库获取AAPL价格数据...")
    
    if database_name == 'ap_research':
        # 从ap_research数据库获取
        query = """
        SELECT 
            stock_symbol,
            trade_date,
            close,
            volume,
            turnover
        FROM priority_quality_stock_hfq
        WHERE stock_symbol = 'AAPL'
        ORDER BY trade_date DESC
        LIMIT 10
        """
    else:
        # 从lseg数据库获取
        query = """
        SELECT 
            ric,
            trade_date,
            close,
            volume,
            turnover
        FROM hfq_intrday_1_day
        WHERE ric LIKE 'AAPL%'
        ORDER BY trade_date DESC
        LIMIT 10
        """
    
    try:
        result = client.execute(query)
        if result:
            if database_name == 'ap_research':
                df = pd.DataFrame(result, columns=['stock_symbol', 'trade_date', 'close', 'volume', 'turnover'])
            else:
                df = pd.DataFrame(result, columns=['ric', 'trade_date', 'close', 'volume', 'turnover'])
            
            print(f"✅ 获取到 {len(df)} 条AAPL价格记录")
            return df
        else:
            print("⚠️ 未找到AAPL的价格数据")
            return None
            
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        return None

def get_aapl_price_history(client, database_name, days=30):
    """获取AAPL的历史价格数据"""
    print(f"\n📈 获取AAPL最近{days}天的价格历史...")
    
    if database_name == 'ap_research':
        query = """
        SELECT 
            stock_symbol,
            trade_date,
            close,
            volume,
            turnover
        FROM priority_quality_stock_hfq
        WHERE stock_symbol = 'AAPL'
        AND trade_date >= (SELECT MAX(trade_date) FROM priority_quality_stock_hfq) - INTERVAL %(days)s DAY
        ORDER BY trade_date DESC
        """
    else:
        query = """
        SELECT 
            ric,
            trade_date,
            close,
            volume,
            turnover
        FROM hfq_intrday_1_day
        WHERE ric LIKE 'AAPL%'
        AND trade_date >= (SELECT MAX(trade_date) FROM hfq_intrday_1_day) - INTERVAL %(days)s DAY
        ORDER BY trade_date DESC
        """
    
    try:
        result = client.execute(query, {'days': days})
        if result:
            if database_name == 'ap_research':
                df = pd.DataFrame(result, columns=['stock_symbol', 'trade_date', 'close', 'volume', 'turnover'])
            else:
                df = pd.DataFrame(result, columns=['ric', 'trade_date', 'close', 'volume', 'turnover'])
            
            print(f"✅ 获取到 {len(df)} 条历史价格记录")
            return df
        else:
            print("⚠️ 未找到AAPL的历史价格数据")
            return None
            
    except Exception as e:
        print(f"❌ 查询历史数据失败: {e}")
        return None

def analyze_price_trend(df):
    """分析价格趋势"""
    if df is None or len(df) == 0:
        return
    
    print(f"\n📊 AAPL价格趋势分析:")
    print("-" * 60)
    
    # 最新价格
    latest = df.iloc[0]
    print(f"最新交易日: {latest['trade_date']}")
    print(f"最新收盘价: ${latest['close']:.2f}")
    
    if len(df) > 1:
        # 价格变化
        prev = df.iloc[1]
        price_change = latest['close'] - prev['close']
        price_change_pct = (price_change / prev['close']) * 100
        
        print(f"较前一日: {price_change:+.2f} ({price_change_pct:+.2f}%)")
        
        # 成交量分析
        if 'volume' in df.columns:
            latest_volume = latest['volume']
            avg_volume = df['volume'].mean()
            print(f"最新成交量: {latest_volume:,.0f}")
            print(f"平均成交量: {avg_volume:,.0f}")
        
        # 价格统计
        prices = df['close']
        print(f"最高价: ${prices.max():.2f}")
        print(f"最低价: ${prices.min():.2f}")
        print(f"平均价: ${prices.mean():.2f}")
        print(f"价格波动: ${prices.std():.2f}")

def main():
    """主函数"""
    print("🍎 AAPL股票价格查询系统")
    print("=" * 50)
    
    # 连接数据库
    client = connect_to_database()
    if not client:
        print("❌ 无法连接到数据库，程序退出")
        return
    
    # 确定数据库名称
    database_name = 'ap_research' if 'ap_research' in str(client) else 'lseg'
    print(f"📊 当前使用数据库: {database_name}")
    
    # 获取最新价格
    latest_prices = get_aapl_latest_price(client, database_name)
    if latest_prices is not None:
        analyze_price_trend(latest_prices)
    
    # 获取价格历史
    price_history = get_aapl_price_history(client, database_name, days=30)
    if price_history is not None:
        print(f"\n📅 最近30天价格概览:")
        print(price_history[['trade_date', 'close']].head(10).to_string(index=False))
    
    print(f"\n✅ AAPL价格查询完成！")

if __name__ == "__main__":
    main()
