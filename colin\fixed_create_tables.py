from clickhouse_driver import Client

client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')

print("创建表1: quarterly_data_filled_enhanced...")
client.execute("""
CREATE TABLE IF NOT EXISTS quarterly_data_filled_enhanced (
    id String, stock_symbol String, financial_period_absolute String, statement_type String, item_name String,
    original_value Nullable(Float64), filled_simple_estimates Nullable(Float64), corrected_filled_simple_estimates Nullable(Float64),
    data_status String, fill_method Nullable(String), confidence_score Nullable(Float64),
    item_status String, item_source String, correction_applicable String,
    estimation_notes Nullable(String), correction_notes Nullable(String),
    created_at DateTime, updated_at DateTime
) ENGINE = MergeTree() ORDER BY (stock_symbol, financial_period_absolute, statement_type, item_name) PARTITION BY stock_symbol;
""")
print("✅ 表1创建成功")

print("创建表2: item_status_mapping...")
client.execute("""
CREATE TABLE IF NOT EXISTS item_status_mapping (
    id String, stock_symbol String, statement_type String, item_name String,
    item_status String, item_source String, 
    historical_frequency Nullable(Int32), detection_confidence Nullable(Float64),
    created_at DateTime, updated_at DateTime
) ENGINE = MergeTree() ORDER BY (stock_symbol, statement_type, item_name) PARTITION BY statement_type;
""")
print("✅ 表2创建成功")

print("创建表3: long_time_fundamental_na_list_detailed...")
client.execute("""
CREATE TABLE IF NOT EXISTS long_time_fundamental_na_list_detailed (
    id String, stock_symbol String, missing_reason String,
    total_missing_count Int32, total_available_count Int32,
    created_at DateTime, updated_at DateTime
) ENGINE = MergeTree() ORDER BY (stock_symbol, missing_reason) PARTITION BY missing_reason;
""")
print("✅ 表3创建成功")

print("创建表4: estimation_quality_metrics...")
client.execute("""
CREATE TABLE IF NOT EXISTS estimation_quality_metrics (
    id String, metric_type String, fill_method String,
    total_estimations Int32, successful_estimations Int32, success_rate Float64,
    avg_confidence_score Float64,
    created_at DateTime, updated_at DateTime
) ENGINE = MergeTree() ORDER BY (metric_type, fill_method) PARTITION BY metric_type;
""")
print("✅ 表4创建成功")

tables = client.execute("SHOW TABLES")
print(f"\n📊 数据库现有 {len(tables)} 个表:")
for table in sorted([t[0] for t in tables]):
    print(f"  • {table}")

print("\n🎉 4个核心表创建完成！")

