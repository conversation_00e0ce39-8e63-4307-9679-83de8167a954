#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
逐步调试complete_24_factors函数
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from final_complete_24_factors_with_field_mapping import (
    connect_to_ap_research, 
    calculate_complete_24_factors
)

def debug_complete_factors_step_by_step():
    """逐步调试complete_24_factors函数"""
    print("🔍 逐步调试complete_24_factors函数...")
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        print("❌ 无法连接到数据库")
        return
    
    try:
        test_stock = 'AAPL'
        calculation_date = '2024-01-15'
        
        print(f"📊 测试股票: {test_stock}")
        print(f"📅 计算日期: {calculation_date}")
        
        # 调用完整因子计算函数
        print(f"\n🔄 调用 calculate_complete_24_factors...")
        factors = calculate_complete_24_factors(client, test_stock, calculation_date)
        
        if not factors:
            print("❌ 因子计算返回空结果")
            return
        
        print(f"✅ 因子计算完成，返回 {len(factors)} 个键值对")
        
        # 检查关键因子
        key_factors_to_check = [
            'profit_revenue_ratio',
            'operating_margin', 
            'tech_premium',
            'revenue_yoy',
            'revenue_cagr'
        ]
        
        print(f"\n📊 关键因子检查:")
        print("-" * 60)
        
        for factor in key_factors_to_check:
            if factor in factors:
                value = factors[factor]
                flag_key = f"{factor}_field_flag"
                flag = factors.get(flag_key, "无标志")
                
                print(f"{factor}:")
                print(f"   值: {value}")
                print(f"   标志: {flag}")
                
                # 特别检查0值
                if value == 0 or value == 0.0:
                    print(f"   ⚠️ 这个因子的值是0！")
                elif value is None:
                    print(f"   ⚠️ 这个因子的值是None！")
                else:
                    print(f"   ✅ 这个因子有正常值")
                print()
            else:
                print(f"{factor}: ❌ 不存在")
        
        # 显示所有因子的值
        print(f"\n📋 所有因子的值:")
        print("-" * 60)
        
        factor_count = 0
        zero_count = 0
        none_count = 0
        normal_count = 0
        
        for key, value in factors.items():
            if not key.endswith('_field_flag') and not key.endswith('_calculation_details'):
                factor_count += 1
                
                if value == 0 or value == 0.0:
                    zero_count += 1
                    print(f"⚠️ {key}: {value} (零值)")
                elif value is None:
                    none_count += 1
                    print(f"❌ {key}: {value} (None)")
                else:
                    normal_count += 1
                    print(f"✅ {key}: {value}")
        
        print(f"\n📊 统计结果:")
        print(f"   总因子数: {factor_count}")
        print(f"   正常值: {normal_count}")
        print(f"   零值: {zero_count}")
        print(f"   None值: {none_count}")
        
        # 检查是否有异常处理被触发
        print(f"\n🔍 检查异常处理标志:")
        for key, value in factors.items():
            if key.endswith('_field_flag') and value == "NA":
                factor_name = key.replace('_field_flag', '')
                print(f"   ❌ {factor_name}: 异常处理被触发")
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            client.disconnect()
            print("🔌 数据库连接已关闭")

if __name__ == "__main__":
    debug_complete_factors_step_by_step()
