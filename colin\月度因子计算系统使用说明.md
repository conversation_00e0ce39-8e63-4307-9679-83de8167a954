# 月度因子计算系统使用说明

## 🎯 系统概述

**月度因子计算系统** 是一个完整的解决方案，能够计算过去每个月所有股票的24个因子值。系统基于我们现有的24因子架构，支持时间序列滚动计算。

## 🔧 系统特性

### ✅ 核心功能
1. **时间序列计算**: 支持按`effective_date`滚动计算因子
2. **全股票覆盖**: 覆盖所有817只有完整财务数据的股票
3. **24因子完整**: 包含所有24个因子的计算逻辑
4. **字段优先级**: 自动使用最佳可用字段，支持降级策略
5. **数据质量跟踪**: 每个因子都有字段使用标志

### 📊 数据架构
- **数据源**: `priority_quality_fundamental_data_complete_deduped`表
- **时间字段**: `effective_date`（公告日+1天生效）
- **财务期间**: 年报数据（FY开头）
- **股票范围**: 817只有完整三大报表的股票

## 🚀 快速开始

### 1. 运行系统
```bash
cd colin
python complete_24_factors_monthly_system.py
```

### 2. 系统输出
系统会自动：
- 连接`ap_research`数据库
- 获取最近2年的财务数据
- 计算每个`effective_date`的所有股票因子
- 保存结果到CSV文件
- 显示计算摘要和示例数据

### 3. 输出文件
```
monthly_24_factors_all_stocks_YYYYMMDD_HHMMSS.csv
```

## 📈 输出数据结构

### 核心字段
- `stock_symbol`: 股票代码
- `effective_date`: 因子生效日期
- `fiscal_year`: 财年
- `tech_premium`: 技术溢价
- `tech_gap_warning`: 技术差距警告
- `adjusted_roce`: 调整后ROCE
- `roic`: ROIC
- `net_profit_margin`: 净利润率
- `fcf`: 自由现金流
- `fcf_cagr`: 自由现金流CAGR
- `roic_cagr`: ROIC CAGR
- ... (其他因子)

### 数据示例
```csv
stock_symbol,effective_date,fiscal_year,tech_premium,tech_gap_warning,adjusted_roce,roic,net_profit_margin,fcf,fcf_cagr,roic_cagr
AAPL,2024-01-01,2024,5.23,2.15,25.67,24.89,21.45,987654321,15.67,12.34
MSFT,2024-01-01,2024,8.91,1.87,28.45,27.12,23.67,1234567890,18.92,14.56
```

## 🔍 系统工作原理

### 1. 数据获取阶段
```python
# 从数据库获取基本面数据
query = """
SELECT stock_symbol, financial_period_absolute, statement_type, 
       item_name, value, announcement_date, effective_date
FROM priority_quality_fundamental_data_complete_deduped
WHERE effective_date BETWEEN %(start_date)s AND %(end_date)s
  AND substring(financial_period_absolute, 1, 2) = 'FY'
"""
```

### 2. 数据透视阶段
```python
# 转换为宽表格式，便于因子计算
df_pivot = df_financial.pivot_table(
    index=['stock_symbol', 'financial_period_absolute', 'effective_date', 'fiscal_year'],
    columns='item_name',
    values='value',
    aggfunc='first'
).reset_index()
```

### 3. 滚动计算阶段
```python
# 对每个effective_date计算所有股票的因子
for target_date in all_dates:
    for stock_symbol in df_pivot['stock_symbol'].unique():
        stock_data = df_pivot[df_pivot['stock_symbol'] == stock_symbol].copy()
        factors = calculate_24_factors_for_date(stock_data, target_date)
```

### 4. 因子计算逻辑
```python
def calculate_24_factors_for_date(stock_data, target_date):
    # 筛选到目标日期为止的数据
    available_data = stock_data[stock_data['effective_date'] <= target_date].copy()
    
    # 获取最新数据
    latest_data = available_data.iloc[-1]
    prev_data = available_data.iloc[-2] if len(available_data) >= 2 else None
    
    # 计算各个因子...
    factors = {}
    
    # 1. 技术溢价
    rd_expense = get_field_with_priority(latest_data, FIELD_CONFIGS['rd_expense'])
    revenue = get_field_with_priority(latest_data, FIELD_CONFIGS['revenue'])
    if revenue > 0:
        factors['tech_premium'] = (rd_expense / revenue) * 100
    
    # 继续计算其他因子...
    
    return factors
```

## ⚙️ 自定义配置

### 1. 修改时间范围
```python
# 在main()函数中修改
start_date = datetime(2020, 1, 1)  # 自定义开始日期
end_date = datetime(2024, 12, 31)  # 自定义结束日期

df_monthly_factors = calculate_monthly_factors_for_all_stocks(
    client, start_date, end_date
)
```

### 2. 添加新因子
```python
# 在calculate_24_factors_for_date函数中添加
# 新因子计算逻辑
try:
    # 计算新因子
    new_factor_value = calculate_new_factor(latest_data)
    factors['new_factor'] = new_factor_value
except:
    factors['new_factor'] = None
```

### 3. 修改字段优先级
```python
# 在FIELD_CONFIGS中添加或修改
FIELD_CONFIGS['new_field'] = {
    'fields': [
        "Primary Field Name",      # 优先级1
        "Secondary Field Name",    # 优先级2
        "Tertiary Field Name"      # 优先级3
    ]
}
```

## 📊 性能优化建议

### 1. 数据分批处理
```python
# 对于大量数据，可以分批处理
batch_size = 1000
for i in range(0, len(all_stocks), batch_size):
    batch_stocks = all_stocks[i:i+batch_size]
    # 处理这批股票
```

### 2. 并行计算
```python
# 使用多进程加速计算
from multiprocessing import Pool

def process_stock_batch(stock_batch):
    # 处理股票批次
    pass

with Pool(processes=4) as pool:
    results = pool.map(process_stock_batch, stock_batches)
```

### 3. 内存优化
```python
# 及时释放不需要的数据
del df_financial  # 释放原始数据
del df_pivot      # 释放透视表数据
```

## 🔧 故障排除

### 常见问题

#### 1. 数据库连接失败
```
❌ 连接数据库失败: [Errno 111] Connection refused
```
**解决方案**: 检查数据库服务器状态和网络连接

#### 2. 数据为空
```
❌ 没有计算出有效的月度因子数据
```
**解决方案**: 
- 检查数据库中的财务数据
- 验证日期范围设置
- 检查SQL查询条件

#### 3. 内存不足
```
MemoryError: Unable to allocate array
```
**解决方案**: 
- 减少处理的时间范围
- 分批处理数据
- 增加系统内存

### 调试模式
```python
# 添加调试信息
import logging
logging.basicConfig(level=logging.DEBUG)

# 在关键步骤添加打印
print(f"处理股票: {stock_symbol}")
print(f"可用数据点: {len(available_data)}")
print(f"计算因子: {factors}")
```

## 📈 扩展功能

### 1. 因子可视化
```python
import matplotlib.pyplot as plt
import seaborn as sns

# 绘制因子时间序列
def plot_factor_timeseries(df, stock_symbol, factor_name):
    stock_data = df[df['stock_symbol'] == stock_symbol]
    plt.figure(figsize=(12, 6))
    plt.plot(stock_data['effective_date'], stock_data[factor_name])
    plt.title(f'{stock_symbol} - {factor_name} 时间序列')
    plt.show()
```

### 2. 因子相关性分析
```python
# 计算因子间相关性
factor_correlation = df_monthly_factors[[
    'tech_premium', 'roic', 'net_profit_margin', 'fcf'
]].corr()

# 绘制热力图
sns.heatmap(factor_correlation, annot=True, cmap='coolwarm')
plt.show()
```

### 3. 因子排名分析
```python
# 计算每期因子排名
def calculate_factor_rankings(df, date):
    date_data = df[df['effective_date'] == date]
    rankings = {}
    
    for factor in ['tech_premium', 'roic', 'net_profit_margin']:
        if factor in date_data.columns:
            rankings[factor] = date_data[factor].rank(ascending=False)
    
    return rankings
```

## 🎯 使用场景

### 1. 投资研究
- 分析股票因子随时间的变化趋势
- 识别因子表现优异的股票
- 构建动态投资组合

### 2. 风险管理
- 监控因子异常值
- 跟踪因子稳定性
- 预警投资风险

### 3. 策略回测
- 基于历史因子值回测策略
- 优化因子权重
- 验证因子有效性

## 📞 技术支持

如果在使用过程中遇到问题，请：

1. 检查错误日志和输出信息
2. 验证数据库连接和数据完整性
3. 参考故障排除部分
4. 联系技术支持团队

---

**系统版本**: 1.0  
**最后更新**: 2024年8月  
**兼容性**: Python 3.7+, ClickHouse 21.8+
