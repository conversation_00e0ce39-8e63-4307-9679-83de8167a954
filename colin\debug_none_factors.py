#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度调试None值因子的具体问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from final_complete_24_factors_with_field_mapping import (
    connect_to_ap_research, 
    calculate_complete_24_factors,
    get_stock_data
)
import pandas as pd

def debug_none_factors():
    """调试None值因子的具体问题"""
    print("🔍 深度调试None值因子...")
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        print("❌ 无法连接到数据库")
        return
    
    try:
        test_stock = 'AAPL'
        calculation_date = '2024-01-15'
        
        print(f"📊 测试股票: {test_stock}")
        print(f"📅 计算日期: {calculation_date}")
        
        # 计算因子
        factors = calculate_complete_24_factors(client, test_stock, calculation_date)
        
        if not factors:
            print("❌ 因子计算失败")
            return
        
        # 识别None值因子
        none_factors = []
        for key, value in factors.items():
            if not key.endswith('_field_flag') and not key.endswith('_calculation_details'):
                if key not in ['stock_symbol', 'calculation_date', 'fiscal_year', 'period_end_date', 'effective_date']:
                    if value is None:
                        none_factors.append(key)
        
        print(f"\n🚨 发现 {len(none_factors)} 个None值因子:")
        print("-" * 60)
        
        for factor in none_factors:
            flag_key = f"{factor}_field_flag"
            flag = factors.get(flag_key, "无标志")
            print(f"❌ {factor}: {flag}")
        
        # 重点分析几个关键的None因子
        key_none_factors = ['roic', 'effective_tax_rate', 'financial_health', 'fcf_per_share']
        
        print(f"\n🔍 重点分析关键None因子:")
        print("=" * 80)
        
        for factor in key_none_factors:
            if factor in none_factors:
                print(f"\n📋 分析因子: {factor}")
                print("-" * 40)
                
                flag_key = f"{factor}_field_flag"
                flag = factors.get(flag_key, "无标志")
                print(f"   标志: {flag}")
                
                # 分析标志中的信息
                if "NA" in flag:
                    print(f"   ❌ 字段映射失败")
                    
                    # 根据因子类型给出可能的原因
                    if factor == 'roic':
                        print(f"   💡 可能原因: 缺少 EBIT 或 投入资本 相关字段映射")
                        print(f"   🔧 需要检查: operating_income, total_assets, current_liabilities 等字段")
                    
                    elif factor == 'effective_tax_rate':
                        print(f"   💡 可能原因: 缺少 税前利润 或 所得税费用 字段映射")
                        print(f"   🔧 需要检查: income_before_tax, income_tax_expense 等字段")
                    
                    elif factor == 'financial_health':
                        print(f"   💡 可能原因: 缺少 流动资产/负债 字段映射")
                        print(f"   🔧 需要检查: current_assets, current_liabilities 等字段")
                    
                    elif factor == 'fcf_per_share':
                        print(f"   💡 可能原因: 缺少 自由现金流 或 股本 字段映射")
                        print(f"   🔧 需要检查: free_cash_flow, shares_outstanding 等字段")
                
                else:
                    print(f"   ⚠️ 其他计算问题")
        
        # 检查当前字段映射的完整性
        print(f"\n🔍 检查当前字段映射完整性:")
        print("=" * 80)
        
        stock_data = get_stock_data(client, test_stock, calculation_date)
        if not stock_data.empty:
            # 透视数据
            pivot_data = stock_data.pivot_table(
                index=['financial_period_absolute', 'period_end_date', 'effective_date'],
                columns='item_name',
                values='value',
                aggfunc='first'
            ).reset_index()
            
            latest_data = pivot_data.iloc[-1]
            
            # 检查关键字段是否存在
            critical_fields = {
                'EBIT相关': ['Earnings before Interest & Taxes (EBIT)', 'Operating Income (Loss)', 'Operating Profit before Non-Recurring Income/Expense'],
                '税务相关': ['Income Tax Expense - Total', 'Income Taxes', 'Income before Income Taxes'],
                '流动性相关': ['Current Assets - Total', 'Current Liabilities - Total', 'Total Current Assets', 'Total Current Liabilities'],
                '股本相关': ['Common Shares - Outstanding - Total', 'Shares Outstanding - Diluted Average'],
                '自由现金流': ['Free Cash Flow', 'Net Cash Flow from Operating Activities']
            }
            
            for category, fields in critical_fields.items():
                print(f"\n📋 {category}:")
                found_any = False
                for field in fields:
                    if field in latest_data.index and pd.notna(latest_data[field]):
                        print(f"   ✅ {field}: {latest_data[field]}")
                        found_any = True
                
                if not found_any:
                    print(f"   ❌ 该类别没有找到任何有效字段")
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            client.disconnect()
            print("🔌 数据库连接已关闭")

if __name__ == "__main__":
    debug_none_factors()
