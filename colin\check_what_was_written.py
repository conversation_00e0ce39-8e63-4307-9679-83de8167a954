from clickhouse_driver import Client

client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')

print("=== 检查ap_research数据库中实际写入的内容 ===")

# 1. 显示所有表
print("1. 数据库中的所有表...")
tables = client.execute("SHOW TABLES")
all_tables = [t[0] for t in tables]
print(f"   总共 {len(all_tables)} 个表:")
for i, table in enumerate(sorted(all_tables), 1):
    print(f"   {i:2d}. {table}")

print("\n2. 检查每个表的数据量...")
for table_name in sorted(all_tables):
    try:
        count = client.execute(f"SELECT COUNT(*) FROM {table_name}")[0][0]
        print(f"   📊 {table_name}: {count:,} 条记录")
        
        # 如果是新建的表，显示结构
        if table_name in ['quarterly_data_filled_enhanced', 'item_status_mapping', 'long_time_fundamental_na_list_detailed', 'estimation_quality_metrics']:
            print(f"      🆕 新建表（空表）")
            
    except Exception as e:
        print(f"   ❌ {table_name}: 查询失败 - {e}")

print("\n3. 检查核心数据表的详细信息...")

# 检查基本面数据
print("\n   📋 priority_quality_fundamental_data_complete_deduped:")
try:
    # 基本统计
    stats = client.execute("""
        SELECT 
            COUNT(*) as total_records,
            COUNT(DISTINCT stock_symbol) as unique_stocks,
            COUNT(DISTINCT financial_period_absolute) as unique_periods,
            COUNT(DISTINCT statement_type) as statement_types
        FROM priority_quality_fundamental_data_complete_deduped
    """)[0]
    
    total_records, unique_stocks, unique_periods, statement_types = stats
    print(f"      总记录数: {total_records:,}")
    print(f"      股票数量: {unique_stocks}")
    print(f"      财务期间数: {unique_periods}")
    print(f"      报表类型数: {statement_types}")
    
    # 年报数据统计
    annual_data = client.execute("""
        SELECT COUNT(DISTINCT stock_symbol) 
        FROM priority_quality_fundamental_data_complete_deduped 
        WHERE financial_period_absolute REGEXP '^FY[0-9]{4}$'
    """)[0][0]
    print(f"      有年报的股票: {annual_data}")
    
except Exception as e:
    print(f"      ❌ 查询失败: {e}")

# 检查价格数据
print("\n   📈 priority_quality_stock_hfq:")
try:
    price_stats = client.execute("""
        SELECT 
            COUNT(*) as total_records,
            COUNT(DISTINCT stock_symbol) as unique_stocks,
            MIN(trade_date) as start_date,
            MAX(trade_date) as end_date
        FROM priority_quality_stock_hfq
    """)[0]
    
    total_records, unique_stocks, start_date, end_date = price_stats
    print(f"      总记录数: {total_records:,}")
    print(f"      股票数量: {unique_stocks}")
    print(f"      时间范围: {start_date} 到 {end_date}")
    
except Exception as e:
    print(f"      ❌ 查询失败: {e}")

# 检查股票池
print("\n   📊 stock_performance_2020_2025_cumulative:")
try:
    stock_pool = client.execute("SELECT COUNT(*) FROM stock_performance_2020_2025_cumulative")[0][0]
    print(f"      股票池大小: {stock_pool}")
    
    # 显示几个样本
    samples = client.execute("SELECT stock_symbol, turnover_type FROM stock_performance_2020_2025_cumulative LIMIT 3")
    print(f"      样本:")
    for sample in samples:
        print(f"        {sample[0]} ({sample[1]})")
        
except Exception as e:
    print(f"      ❌ 查询失败: {e}")

print("\n=== 总结 ===")
print("✅ 核心数据表: 有完整数据")
print("   • priority_quality_fundamental_data_complete_deduped: 完整财报数据")
print("   • priority_quality_stock_hfq: 完整价格数据") 
print("   • stock_performance_2020_2025_cumulative: 821只股票池")

print("\n🆕 新建功能表: 空表（只有结构）")
print("   • quarterly_data_filled_enhanced: 0条记录")
print("   • item_status_mapping: 0条记录")
print("   • long_time_fundamental_na_list_detailed: 0条记录")
print("   • estimation_quality_metrics: 0条记录")

print("\n💡 实际情况:")
print("   ✅ 有完整的基础数据可以计算因子")
print("   ⚠️ 新建的功能表都是空的，只有表结构")
print("   🔄 如果要用季报填充功能，需要重新执行填充算法")
print("   🚀 但是可以直接基于现有年报数据计算24个主要因子！")

