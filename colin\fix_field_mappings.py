#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查和修复字段映射问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from final_complete_24_factors_with_field_mapping import connect_to_ap_research
import pandas as pd

def check_and_fix_field_mappings():
    """检查和修复字段映射"""
    print("🔍 开始检查和修复字段映射问题...")
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        print("❌ 无法连接到数据库")
        return
    
    try:
        # 测试股票
        test_stock = 'AAPL'
        calculation_date = '2024-01-15'
        
        print(f"📊 测试股票: {test_stock}")
        print(f"📅 计算日期: {calculation_date}")
        
        # 获取滚动数据
        start_date = (pd.to_datetime(calculation_date) - pd.Timedelta(days=3650)).strftime('%Y-%m-%d')
        
        query = f"""
        SELECT
            stock_symbol,
            financial_period_absolute,
            period_end_date,
            item_name,
            value,
            effective_date
        FROM fundamental_data_with_announcement_dates
        WHERE stock_symbol = '{test_stock}'
          AND statement_type IN ('income_statement', 'balance_sheet_history', 'cash_flow')
          AND financial_period_absolute REGEXP '^FY[0-9]{{4}}$'
          AND effective_date BETWEEN '{start_date}' AND '{calculation_date}'
        ORDER BY effective_date ASC
        """
        
        result = client.execute(query)
        if not result:
            print("❌ 无法获取数据")
            return
        
        # 创建DataFrame
        df = pd.DataFrame(result, columns=[
            'stock_symbol', 'financial_period_absolute', 'period_end_date',
            'item_name', 'value', 'effective_date'
        ])
        
        # 透视数据
        pivot_data = df.pivot_table(
            index=['financial_period_absolute', 'period_end_date', 'effective_date'],
            columns='item_name',
            values='value',
            aggfunc='first'
        ).reset_index()
        
        print(f"✅ 成功获取滚动数据，形状: {pivot_data.shape}")
        
        # 检查关键字段是否存在
        print("\n🔍 检查关键字段是否存在...")
        
        # 定义需要的关键字段
        key_fields = {
            'revenue': ['Revenue', 'Sales', 'Total Revenue'],
            'net_income': ['Net Income', 'Net Earnings', 'Net Profit'],
            'total_assets': ['Total Assets', 'Assets'],
            'total_equity': ['Shareholders Equity', 'Total Equity', 'Common Equity'],
            'rd_expense': ['Research', 'R&D', 'Development'],
            'operating_cash_flow': ['Operating Cash Flow', 'Cash Flow from Operations'],
            'ebit': ['Operating Income', 'EBIT', 'Earnings Before Interest'],
            'tax_expense': ['Income Tax', 'Tax Expense', 'Taxes'],
            'cash': ['Cash', 'Cash and Cash Equivalents'],
            'debt': ['Total Debt', 'Long Term Debt', 'Debt'],
            'patents': ['Patents', 'Intellectual Property']
        }
        
        found_fields = {}
        missing_fields = {}
        
        for category, search_terms in key_fields.items():
            found_fields[category] = []
            missing_fields[category] = []
            
            for search_term in search_terms:
                matching_fields = [col for col in pivot_data.columns if search_term.lower() in col.lower()]
                if matching_fields:
                    found_fields[category].extend(matching_fields)
                else:
                    missing_fields[category].append(search_term)
        
        # 显示结果
        print("\n📊 字段匹配结果:")
        for category in key_fields.keys():
            print(f"\n   {category}:")
            if found_fields[category]:
                print(f"     ✅ 找到: {found_fields[category][:3]}")  # 只显示前3个
            else:
                print(f"     ❌ 未找到")
        
        # 显示所有可用的字段名（前50个）
        print(f"\n📋 数据库中可用的字段名（前50个）:")
        available_fields = [col for col in pivot_data.columns if col not in ['stock_symbol', 'financial_period_absolute', 'period_end_date', 'effective_date']]
        for i, field in enumerate(available_fields[:50]):
            print(f"     {i+1:2d}. {field}")
        
        # 尝试找到最匹配的字段
        print(f"\n🎯 尝试找到最匹配的字段...")
        
        # 搜索特定字段
        revenue_fields = [col for col in available_fields if 'revenue' in col.lower() or 'sales' in col.lower()]
        net_income_fields = [col for col in available_fields if 'net income' in col.lower() or 'net earnings' in col.lower()]
        assets_fields = [col for col in available_fields if 'total assets' in col.lower()]
        equity_fields = [col for col in available_fields if 'shareholders equity' in col.lower() or 'common equity' in col.lower()]
        
        print(f"   Revenue相关字段: {revenue_fields[:3]}")
        print(f"   Net Income相关字段: {net_income_fields[:3]}")
        print(f"   Total Assets相关字段: {assets_fields[:3]}")
        print(f"   Equity相关字段: {equity_fields[:3]}")
        
        # 测试字段值获取
        print(f"\n🧪 测试字段值获取...")
        if len(pivot_data) > 0:
            latest_data = pivot_data.iloc[-1]
            
            # 测试几个关键字段
            test_fields = ['Revenue from Business Activities - Total', 'Net Income (Loss) - Total', 'Total Assets']
            for field in test_fields:
                if field in latest_data:
                    value = latest_data[field]
                    print(f"     {field}: {value}")
                else:
                    print(f"     {field}: 未找到")
        
        # 生成修复后的字段映射
        print(f"\n🔧 生成修复后的字段映射...")
        
        # 基于实际找到的字段生成映射
        fixed_mappings = {}
        
        # 查找Revenue字段
        revenue_candidates = [col for col in available_fields if 'revenue' in col.lower() and 'total' in col.lower()]
        if revenue_candidates:
            fixed_mappings['revenue'] = revenue_candidates[:2]
        
        # 查找Net Income字段
        net_income_candidates = [col for col in available_fields if 'net income' in col.lower() and 'total' in col.lower()]
        if net_income_candidates:
            fixed_mappings['net_income'] = net_income_candidates[:2]
        
        # 查找Total Assets字段
        assets_candidates = [col for col in available_fields if 'total assets' in col.lower()]
        if assets_candidates:
            fixed_mappings['total_assets'] = assets_candidates[:2]
        
        # 查找Equity字段
        equity_candidates = [col for col in available_fields if 'shareholders equity' in col.lower() or 'common equity' in col.lower()]
        if equity_candidates:
            fixed_mappings['total_equity'] = equity_candidates[:2]
        
        print(f"   修复后的字段映射:")
        for key, values in fixed_mappings.items():
            print(f"     {key}: {values}")
        
    except Exception as e:
        print(f"❌ 检查过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            client.disconnect()
            print("🔌 数据库连接已关闭")

if __name__ == "__main__":
    check_and_fix_field_mappings()
