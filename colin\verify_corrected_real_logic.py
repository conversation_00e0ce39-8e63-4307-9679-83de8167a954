from clickhouse_driver import Client

# 连接到数据库
client_ap = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 验证 corrected_real 逻辑的正确性 ===")

def check_calculation_validity():
    """
    检查当前标记为 corrected_real 的计算是否真的符合要求
    """
    print("\n检查每个 corrected_real 记录的计算依据...")
    
    # 获取所有 corrected_real 记录
    query = """
    SELECT DISTINCT stock_symbol, financial_period_absolute, source_periods
    FROM quarterly_data_filled 
    WHERE data_status = 'corrected_real'
    ORDER BY stock_symbol, financial_period_absolute
    """
    
    try:
        results = client_ap.execute(query)
        print(f"发现 {len(results)} 个不同的计算组合")
        
        for stock_symbol, missing_period, source_periods in results:
            print(f"\n📊 {stock_symbol} - {missing_period}")
            print(f"   计算依据: {source_periods}")
            
            # 解析源期间
            periods = source_periods.split(',')
            annual_period = periods[0]  # 第一个应该是年报
            known_quarters = periods[1:]  # 其余是已知季度
            
            print(f"   年报期间: {annual_period}")
            print(f"   已知季度: {known_quarters}")
            
            # 验证这些期间是否真的都存在原始数据
            is_valid = verify_source_data_exists(stock_symbol, annual_period, known_quarters, missing_period)
            
            if is_valid:
                print(f"   ✅ 计算有效：所有源数据都是原始财报数据")
            else:
                print(f"   ❌ 计算无效：包含估算或缺失数据")
                
    except Exception as e:
        print(f"查询时出错: {e}")

def verify_source_data_exists(stock_symbol, annual_period, known_quarters, missing_period):
    """
    验证用于计算的源数据是否都真实存在
    """
    year = int(missing_period[2:6])
    missing_quarter = int(missing_period[7])
    
    # 1. 验证年报数据存在
    annual_query = f"""
    SELECT COUNT(DISTINCT item_name) as item_count
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = '{stock_symbol}'
      AND financial_period_absolute = '{annual_period}'
    """
    
    try:
        annual_result = client_ap.execute(annual_query)
        annual_items = annual_result[0][0]
        
        if annual_items == 0:
            print(f"      ❌ 年报 {annual_period} 不存在")
            return False
        else:
            print(f"      ✅ 年报 {annual_period} 存在 {annual_items} 个科目")
    except:
        print(f"      ❌ 查询年报 {annual_period} 时出错")
        return False
    
    # 2. 验证所有已知季度数据存在
    for quarter_period in known_quarters:
        quarter_query = f"""
        SELECT COUNT(DISTINCT item_name) as item_count
        FROM priority_quality_fundamental_data_complete_deduped
        WHERE stock_symbol = '{stock_symbol}'
          AND financial_period_absolute = '{quarter_period}'
        """
        
        try:
            quarter_result = client_ap.execute(quarter_query)
            quarter_items = quarter_result[0][0]
            
            if quarter_items == 0:
                print(f"      ❌ 季度 {quarter_period} 不存在")
                return False
            else:
                print(f"      ✅ 季度 {quarter_period} 存在 {quarter_items} 个科目")
        except:
            print(f"      ❌ 查询季度 {quarter_period} 时出错")
            return False
    
    # 3. 验证计算逻辑：年报 = Q1 + Q2 + Q3 + Q4
    print(f"      📋 计算逻辑验证:")
    print(f"         缺失季度: Q{missing_quarter}")
    print(f"         已知季度: {[f'Q{int(p[7])}' for p in known_quarters]}")
    
    expected_quarters = {1, 2, 3, 4}
    known_quarter_nums = {int(p[7]) for p in known_quarters}
    
    if missing_quarter in known_quarter_nums:
        print(f"      ❌ 逻辑错误：缺失季度 Q{missing_quarter} 在已知季度中")
        return False
    
    total_quarters = known_quarter_nums | {missing_quarter}
    if total_quarters != expected_quarters:
        print(f"      ❌ 逻辑错误：季度不完整 {total_quarters} != {expected_quarters}")
        return False
    
    print(f"      ✅ 计算逻辑正确：{annual_period} - {known_quarters} = {missing_period}")
    return True

def check_missing_periods_completeness():
    """
    检查是否遗漏了一些应该能计算但没有计算的期间
    """
    print(f"\n=== 检查是否有遗漏的可计算期间 ===")
    
    # 我们之前识别的缺失期间
    identified_missing = {
        'CCEP': ['FY2023Q1', 'FY2023Q2', 'FY2024Q1', 'FY2024Q2'],
        'POST': ['FY2023Q1', 'FY2023Q2', 'FY2023Q3', 'FY2024Q1', 'FY2024Q2', 'FY2024Q3'],
        'MRP': ['FY2023Q1', 'FY2023Q2'],
        'VG': ['FY2023Q1', 'FY2023Q2'],
        'SIG': ['FY2023Q1'],
        'AU': ['FY2024Q1', 'FY2024Q2']
    }
    
    for stock_symbol, missing_periods in identified_missing.items():
        print(f"\n📈 {stock_symbol}:")
        
        for missing_period in missing_periods:
            year = int(missing_period[2:6])
            missing_quarter = int(missing_period[7])
            annual_period = f'FY{year}'
            
            # 检查是否有足够的数据进行计算
            quarters_query = f"""
            SELECT DISTINCT financial_period_absolute
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = '{stock_symbol}'
              AND financial_period_absolute LIKE 'FY{year}Q%'
              AND financial_period_absolute != '{missing_period}'
            ORDER BY financial_period_absolute
            """
            
            annual_query = f"""
            SELECT COUNT(*) as count
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = '{stock_symbol}'
              AND financial_period_absolute = '{annual_period}'
            """
            
            try:
                quarters_result = client_ap.execute(quarters_query)
                annual_result = client_ap.execute(annual_query)
                
                available_quarters = [row[0] for row in quarters_result]
                has_annual = annual_result[0][0] > 0
                
                print(f"   {missing_period}:")
                print(f"     年报 {annual_period}: {'✅' if has_annual else '❌'}")
                print(f"     可用季度: {available_quarters}")
                
                if has_annual and len(available_quarters) == 3:
                    print(f"     ✅ 可以计算 (年报 - 3个已知季度)")
                elif has_annual and len(available_quarters) < 3:
                    print(f"     ⚠️ 数据不足 (只有{len(available_quarters)}个季度)")
                else:
                    print(f"     ❌ 无法计算 (缺少年报)")
                    
            except Exception as e:
                print(f"     ❌ 查询出错: {e}")

def main():
    """
    主验证函数
    """
    print("开始验证 corrected_real 逻辑...")
    
    try:
        # 1. 检查当前计算的有效性
        check_calculation_validity()
        
        # 2. 检查是否有遗漏
        check_missing_periods_completeness()
        
        print(f"\n=== 验证完成 ===")
        print(f"请检查上述结果，确保所有 corrected_real 记录都基于真实的财报数据")
        
    except Exception as e:
        print(f"❌ 验证过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

