from clickhouse_driver import Client

# 连接ClickHouse数据库
client = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 季报数据综合覆盖情况分析 ===")

# 1. 总体概况
print("\n1. 总体概况...")
query_overview = """
SELECT 
    COUNT(*) as total_quarterly_records,
    COUNT(DISTINCT stock_symbol) as stocks_with_quarterly,
    COUNT(DISTINCT financial_period_absolute) as unique_quarters,
    MIN(period_end_date) as earliest_date,
    MAX(period_end_date) as latest_date
FROM priority_quality_fundamental_data_complete_deduped
WHERE period_end_date IS NOT NULL AND period_end_date != ''
  AND financial_period_absolute REGEXP 'FY[0-9]{4}Q[1-4]'
"""

result_overview = client.execute(query_overview)
total_records, stocks, unique_quarters, earliest, latest = result_overview[0]
print(f"  季报数据记录数: {total_records:,}")
print(f"  有季报数据的股票数: {stocks:,}")
print(f"  唯一季报期间数: {unique_quarters:,}")
print(f"  数据时间范围: {earliest} 到 {latest}")

# 2. 各年份季报完整率统计
print("\n2. 各年份季报完整率统计...")
query_yearly_completeness = """
SELECT 
    fiscal_year,
    total_stocks,
    complete_stocks,
    complete_stocks * 100.0 / total_stocks as completeness_rate,
    avg_quarters
FROM (
    SELECT 
        substring(financial_period_absolute, 3, 4) as fiscal_year,
        COUNT(DISTINCT stock_symbol) as total_stocks,
        SUM(CASE WHEN quarters_per_stock = 4 THEN 1 ELSE 0 END) as complete_stocks,
        AVG(quarters_per_stock) as avg_quarters
    FROM (
        SELECT 
            stock_symbol,
            financial_period_absolute,
            COUNT(*) as quarters_per_stock
        FROM (
            SELECT DISTINCT 
                stock_symbol,
                financial_period_absolute
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE period_end_date IS NOT NULL AND period_end_date != ''
              AND financial_period_absolute REGEXP 'FY[0-9]{4}Q[1-4]'
        ) distinct_quarters
        GROUP BY stock_symbol, substring(financial_period_absolute, 1, 6)
    ) stock_year_quarters
    GROUP BY substring(financial_period_absolute, 3, 4)
) yearly_stats
WHERE toInt32(fiscal_year) >= 2015
ORDER BY fiscal_year DESC
"""

result_yearly = client.execute(query_yearly_completeness)
if result_yearly:
    print("  年份   总股票数  完整股票数  完整率   平均季度数")
    print("  " + "-" * 50)
    for year, total, complete, rate, avg_quarters in result_yearly:
        print(f"  {year}   {total:8d}  {complete:10d}  {rate:6.1f}%  {avg_quarters:10.1f}")

# 3. 季报缺失模式分析
print("\n3. 季报缺失模式分析（2023年）...")
query_missing_pattern = """
SELECT 
    missing_quarters,
    COUNT(*) as stock_count
FROM (
    SELECT 
        stock_symbol,
        4 - COUNT(DISTINCT substring(financial_period_absolute, 7, 2)) as missing_quarters
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE period_end_date IS NOT NULL AND period_end_date != ''
      AND financial_period_absolute LIKE 'FY2023Q%'
    GROUP BY stock_symbol
) missing_stats
GROUP BY missing_quarters
ORDER BY missing_quarters
"""

result_missing = client.execute(query_missing_pattern)
if result_missing:
    print("  2023年季报缺失情况:")
    print("  缺失季度数  股票数量")
    print("  " + "-" * 20)
    for missing, count in result_missing:
        completeness = 4 - missing
        print(f"  {missing:10d}  {count:8d} (有{completeness}个季度)")

# 4. 找出季报数据不完整的具体股票
print("\n4. 季报数据不完整的股票（2023年）...")
query_incomplete_stocks = """
SELECT 
    stock_symbol,
    available_quarters,
    quarter_list
FROM (
    SELECT 
        stock_symbol,
        COUNT(DISTINCT substring(financial_period_absolute, 7, 2)) as available_quarters,
        groupArray(DISTINCT substring(financial_period_absolute, 7, 2)) as quarter_list
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE period_end_date IS NOT NULL AND period_end_date != ''
      AND financial_period_absolute LIKE 'FY2023Q%'
    GROUP BY stock_symbol
    HAVING available_quarters < 4
) incomplete_stocks
ORDER BY available_quarters, stock_symbol
"""

result_incomplete = client.execute(query_incomplete_stocks)
if result_incomplete:
    print("  不完整的股票列表:")
    print("  股票代码   可用季度  具体季度")
    print("  " + "-" * 35)
    for stock, quarters, quarter_list in result_incomplete:
        quarter_str = str(quarter_list).replace("'", "").replace("[", "").replace("]", "")
        print(f"  {stock:10s} {quarters:8d}  {quarter_str}")
else:
    print("  2023年所有股票的季报数据都是完整的！")

# 5. 历年季报完整率趋势
print("\n5. 历年季报完整率趋势...")
query_trend = """
SELECT 
    fiscal_year,
    complete_rate
FROM (
    SELECT 
        substring(financial_period_absolute, 3, 4) as fiscal_year,
        SUM(CASE WHEN quarters_per_stock = 4 THEN 1 ELSE 0 END) * 100.0 / COUNT(*) as complete_rate
    FROM (
        SELECT 
            stock_symbol,
            substring(financial_period_absolute, 1, 6) as year_period,
            COUNT(DISTINCT substring(financial_period_absolute, 7, 2)) as quarters_per_stock
        FROM priority_quality_fundamental_data_complete_deduped
        WHERE period_end_date IS NOT NULL AND period_end_date != ''
          AND financial_period_absolute REGEXP 'FY[0-9]{4}Q[1-4]'
        GROUP BY stock_symbol, substring(financial_period_absolute, 1, 6)
    ) stock_completeness
    GROUP BY substring(year_period, 3, 4)
) trend_data
WHERE toInt32(fiscal_year) >= 2015
ORDER BY fiscal_year DESC
"""

result_trend = client.execute(query_trend)
if result_trend:
    print("  年份   季报完整率")
    print("  " + "-" * 18)
    for year, rate in result_trend:
        print(f"  {year}   {rate:10.1f}%")

print("\n=== 分析完成 ===")

