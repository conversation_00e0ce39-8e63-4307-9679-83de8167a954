from clickhouse_driver import Client
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')
import time

# 连接ClickHouse数据库
client = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='lseg'
)

print("=== <PERSON> 13因子实现（修正版）===")

def get_financial_data():
    """获取财务数据及其公告日期"""
    print("\n1. 获取财务数据...")
    
    # 获取利润表数据 - 使用实际可用的数据项
    income_query = """
    SELECT 
        i.instrument,
        i.financial_period_absolute,
        i.item_name,
        i.income_statement as value,
        sh.original_announcement_date_time
    FROM lseg.income_statement i
    LEFT JOIN lseg.statement_header sh
        ON i.instrument = sh.instrument
        AND i.financial_period_absolute = sh.financial_period_absolute
        AND sh.statement_type = 'Income Statement'
    WHERE i.item_name IN (
        'Revenue from Business Activities - Total',
        'Normalized Net Income - Bottom Line',
        'Earnings before Interest & Taxes (EBIT)',
        'Operating Profit before Non-Recurring Income/Expense',
        'Income before Taxes',
        'Income Taxes'
    )
    AND i.financial_period_absolute >= 'FY2020Q1'
    AND sh.original_announcement_date_time IS NOT NULL
    LIMIT 100000
    """
    
    income_data = client.execute(income_query)
    df_income = pd.DataFrame(income_data, columns=['instrument', 'financial_period_absolute', 'item_name', 'value', 'original_announcement_date_time'])
    
    # 获取资产负债表数据 - 使用实际可用的数据项
    balance_query = """
    SELECT 
        b.instrument,
        b.financial_period_absolute,
        b.item_name,
        b.balance_sheet as value,
        sh.original_announcement_date_time
    FROM lseg.balance_sheet_history b
    LEFT JOIN lseg.statement_header sh
        ON b.instrument = sh.instrument
        AND b.financial_period_absolute = sh.financial_period_absolute
        AND sh.statement_type = 'Balance Sheet'
    WHERE b.item_name IN (
        'Shareholders'' Equity - Attributable to Parent Shareholders - Total',
        'Common Equity - Total',
        'Debt - Total',
        'Common Shares - Outstanding - Total'
    )
    AND b.financial_period_absolute >= 'FY2020Q1'
    AND sh.original_announcement_date_time IS NOT NULL
    LIMIT 100000
    """
    
    balance_data = client.execute(balance_query)
    df_balance = pd.DataFrame(balance_data, columns=['instrument', 'financial_period_absolute', 'item_name', 'value', 'original_announcement_date_time'])
    
    # 获取现金流量表数据 - 使用实际可用的数据项
    cash_flow_query = """
    SELECT 
        c.instrument,
        c.financial_period_absolute,
        c.item_name,
        c.cash_flow_statement as value,
        sh.original_announcement_date_time
    FROM lseg.cash_flow c
    LEFT JOIN lseg.statement_header sh
        ON c.instrument = sh.instrument
        AND c.financial_period_absolute = sh.financial_period_absolute
        AND sh.statement_type = 'Statement of Cash Flows'
    WHERE c.item_name IN (
        'Net Cash Flow from Operating Activities',
        'Capital Expenditures - Total'
    )
    AND c.financial_period_absolute >= 'FY2020Q1'
    AND sh.original_announcement_date_time IS NOT NULL
    LIMIT 100000
    """
    
    cash_flow_data = client.execute(cash_flow_query)
    df_cash_flow = pd.DataFrame(cash_flow_data, columns=['instrument', 'financial_period_absolute', 'item_name', 'value', 'original_announcement_date_time'])
    
    print(f"  利润表数据: {len(df_income):,} 条记录")
    print(f"  资产负债表数据: {len(df_balance):,} 条记录")
    print(f"  现金流量表数据: {len(df_cash_flow):,} 条记录")
    
    return df_income, df_balance, df_cash_flow

def process_and_calculate_factors(df_income, df_balance, df_cash_flow):
    """处理数据并计算13个因子"""
    print("\n2. 处理数据并计算因子...")
    
    # 转换日期格式
    for df in [df_income, df_balance, df_cash_flow]:
        df['original_announcement_date_time'] = pd.to_datetime(df['original_announcement_date_time'])
        df['effective_date'] = df['original_announcement_date_time'] + timedelta(days=1)
    
    # 数据透视
    df_income_pivot = df_income.pivot_table(
        index=['instrument', 'financial_period_absolute', 'effective_date'],
        columns='item_name',
        values='value',
        aggfunc='first'
    ).reset_index()
    
    df_balance_pivot = df_balance.pivot_table(
        index=['instrument', 'financial_period_absolute', 'effective_date'],
        columns='item_name',
        values='value',
        aggfunc='first'
    ).reset_index()
    
    df_cash_flow_pivot = df_cash_flow.pivot_table(
        index=['instrument', 'financial_period_absolute', 'effective_date'],
        columns='item_name',
        values='value',
        aggfunc='first'
    ).reset_index()
    
    # 合并三大报表
    df_merged = df_income_pivot.merge(
        df_balance_pivot,
        on=['instrument', 'financial_period_absolute', 'effective_date'],
        how='outer'
    )
    
    df_merged = df_merged.merge(
        df_cash_flow_pivot,
        on=['instrument', 'financial_period_absolute', 'effective_date'],
        how='outer'
    )
    
    # 提取财年信息
    df_merged['fiscal_year'] = df_merged['financial_period_absolute'].str.extract(r'FY(\d{4})').astype(float)
    df_merged['quarter'] = df_merged['financial_period_absolute'].str.extract(r'Q(\d)').astype(float)
    
    # 计算因子
    factors_results = []
    
    for instrument in df_merged['instrument'].unique():
        stock_data = df_merged[df_merged['instrument'] == instrument].copy()
        stock_data = stock_data.sort_values('effective_date')
        
        if len(stock_data) < 4:
            continue
        
        latest_data = stock_data.iloc[-1]
        factor_row = {
            'instrument': instrument,
            'fiscal_year': latest_data['fiscal_year'],
            'quarter': latest_data['quarter'],
            'effective_date': latest_data['effective_date']
        }
        
        # 计算13个因子
        # 1. 营收同比增长率
        try:
            revenue = latest_data.get('Revenue from Business Activities - Total')
            if revenue and len(stock_data) >= 5:
                prev_revenue = stock_data.iloc[-5].get('Revenue from Business Activities - Total')
                if prev_revenue and prev_revenue > 0:
                    factor_row['revenue_yoy'] = (revenue / prev_revenue - 1) * 100
                else:
                    factor_row['revenue_yoy'] = None
            else:
                factor_row['revenue_yoy'] = None
        except:
            factor_row['revenue_yoy'] = None
        
        # 2. 近3年营收CAGR
        try:
            if len(stock_data) >= 8:
                current_revenue = stock_data.iloc[-1].get('Revenue from Business Activities - Total')
                past_revenue = stock_data.iloc[-8].get('Revenue from Business Activities - Total')
                if current_revenue and past_revenue and past_revenue > 0:
                    factor_row['revenue_cagr'] = ((current_revenue / past_revenue) ** (1/2) - 1) * 100
                else:
                    factor_row['revenue_cagr'] = None
            else:
                factor_row['revenue_cagr'] = None
        except:
            factor_row['revenue_cagr'] = None
        
        # 3. 净利润同比增长率
        try:
            net_income = latest_data.get('Normalized Net Income - Bottom Line')
            if net_income and len(stock_data) >= 5:
                prev_net_income = stock_data.iloc[-5].get('Normalized Net Income - Bottom Line')
                if prev_net_income and prev_net_income > 0:
                    factor_row['net_income_yoy'] = (net_income / prev_net_income - 1) * 100
                else:
                    factor_row['net_income_yoy'] = None
            else:
                factor_row['net_income_yoy'] = None
        except:
            factor_row['net_income_yoy'] = None
        
        # 4. 盈利营收增长比
        try:
            if factor_row.get('revenue_yoy') is not None and factor_row.get('net_income_yoy') is not None:
                if factor_row['revenue_yoy'] != 0:
                    factor_row['profit_revenue_ratio'] = factor_row['net_income_yoy'] / factor_row['revenue_yoy']
                else:
                    factor_row['profit_revenue_ratio'] = None
            else:
                factor_row['profit_revenue_ratio'] = None
        except:
            factor_row['profit_revenue_ratio'] = None
        
        # 5. 每股自由现金流
        try:
            operating_cf = latest_data.get('Net Cash Flow from Operating Activities') or 0
            capex = latest_data.get('Capital Expenditures - Total') or 0
            shares = latest_data.get('Common Shares - Outstanding - Total')
            
            if shares and shares > 0:
                factor_row['fcf_per_share'] = (operating_cf - capex) / shares
            else:
                factor_row['fcf_per_share'] = None
        except:
            factor_row['fcf_per_share'] = None
        
        # 6. FCF复合增长率
        try:
            if len(stock_data) >= 8:
                current_fcf = (stock_data.iloc[-1].get('Net Cash Flow from Operating Activities') or 0) - (stock_data.iloc[-1].get('Capital Expenditures - Total') or 0)
                past_fcf = (stock_data.iloc[-8].get('Net Cash Flow from Operating Activities') or 0) - (stock_data.iloc[-8].get('Capital Expenditures - Total') or 0)
                
                if current_fcf and past_fcf and past_fcf > 0:
                    factor_row['fcf_cagr'] = ((current_fcf / past_fcf) ** (1/2) - 1) * 100
                else:
                    factor_row['fcf_cagr'] = None
            else:
                factor_row['fcf_cagr'] = None
        except:
            factor_row['fcf_cagr'] = None
        
        # 7. FCF与净利润比值
        try:
            if factor_row.get('fcf_per_share') is not None and net_income and shares and shares > 0:
                net_income_per_share = net_income / shares
                if net_income_per_share != 0:
                    factor_row['fcf_net_income_ratio'] = factor_row['fcf_per_share'] / net_income_per_share
                else:
                    factor_row['fcf_net_income_ratio'] = None
            else:
                factor_row['fcf_net_income_ratio'] = None
        except:
            factor_row['fcf_net_income_ratio'] = None
        
        # 8. 营业利益率 - 使用EBIT
        try:
            ebit = latest_data.get('Earnings before Interest & Taxes (EBIT)')
            revenue = latest_data.get('Revenue from Business Activities - Total')
            if ebit and revenue and revenue > 0:
                factor_row['operating_margin'] = ebit / revenue * 100
            else:
                factor_row['operating_margin'] = None
        except:
            factor_row['operating_margin'] = None
        
        # 9. 营业利益率标准差
        try:
            if len(stock_data) >= 4:
                margins = []
                for i in range(-4, 0):
                    ebit = stock_data.iloc[i].get('Earnings before Interest & Taxes (EBIT)')
                    rev = stock_data.iloc[i].get('Revenue from Business Activities - Total')
                    if ebit and rev and rev > 0:
                        margins.append(ebit / rev)
                
                if len(margins) >= 2:
                    factor_row['operating_margin_std'] = np.std(margins) * 100
                else:
                    factor_row['operating_margin_std'] = None
            else:
                factor_row['operating_margin_std'] = None
        except:
            factor_row['operating_margin_std'] = None
        
        # 10. ROIC
        try:
            pretax_income = latest_data.get('Income before Taxes')
            tax_expense = latest_data.get('Income Taxes') or 0
            equity = latest_data.get('Shareholders'' Equity - Attributable to Parent Shareholders - Total')
            debt = latest_data.get('Debt - Total') or 0
            
            if pretax_income and pretax_income > 0 and equity and equity > 0:
                effective_tax_rate = tax_expense / pretax_income if tax_expense > 0 else 0.25
                ebit = latest_data.get('Earnings before Interest & Taxes (EBIT)')
                nopat = ebit * (1 - effective_tax_rate)
                invested_capital = equity + debt
                
                if invested_capital > 0:
                    factor_row['roic'] = nopat / invested_capital * 100
                else:
                    factor_row['roic'] = None
            else:
                factor_row['roic'] = None
        except:
            factor_row['roic'] = None
        
        # 11. ROIC CAGR (简化)
        try:
            if factor_row.get('roic') is not None:
                factor_row['roic_cagr'] = factor_row['roic'] * 0.1  # 简化处理
            else:
                factor_row['roic_cagr'] = None
        except:
            factor_row['roic_cagr'] = None
        
        # 12. 有效税率
        try:
            if pretax_income and pretax_income > 0:
                factor_row['effective_tax_rate'] = tax_expense / pretax_income * 100
            else:
                factor_row['effective_tax_rate'] = None
        except:
            factor_row['effective_tax_rate'] = None
        
        # 13. 有效税率标准差
        try:
            if len(stock_data) >= 4:
                tax_rates = []
                for i in range(-4, 0):
                    pt_income = stock_data.iloc[i].get('Income before Taxes')
                    tx_expense = stock_data.iloc[i].get('Income Taxes') or 0
                    if pt_income and pt_income > 0:
                        tax_rates.append(tx_expense / pt_income)
                
                if len(tax_rates) >= 2:
                    factor_row['effective_tax_rate_std'] = np.std(tax_rates) * 100
                else:
                    factor_row['effective_tax_rate_std'] = None
            else:
                factor_row['effective_tax_rate_std'] = None
        except:
            factor_row['effective_tax_rate_std'] = None
        
        factors_results.append(factor_row)
    
    df_factors = pd.DataFrame(factors_results)
    
    print(f"  计算完成的因子记录数: {len(df_factors):,}")
    return df_factors

def analyze_and_save_results(df_factors):
    """分析覆盖率并保存结果"""
    print("\n3. 分析覆盖率并保存结果...")
    
    total_stocks = len(df_factors)
    coverage_stats = {}
    
    factor_columns = [
        'revenue_yoy', 'revenue_cagr', 'net_income_yoy', 'profit_revenue_ratio',
        'fcf_per_share', 'fcf_cagr', 'fcf_net_income_ratio',
        'operating_margin', 'operating_margin_std',
        'roic', 'roic_cagr',
        'effective_tax_rate', 'effective_tax_rate_std'
    ]
    
    for factor in factor_columns:
        if factor in df_factors.columns:
            coverage = df_factors[factor].notna().sum()
            coverage_pct = (coverage / total_stocks) * 100
            coverage_stats[factor] = {
                'coverage': coverage,
                'coverage_pct': coverage_pct
            }
            print(f"  {factor}: {coverage} 只股票 ({coverage_pct:.1f}%)")
    
    # 保存结果
    output_file = 'mclean_13_factors_corrected_results.csv'
    df_factors.to_csv(output_file, index=False, encoding='utf-8')
    print(f"  因子结果已保存到: {output_file}")
    
    # 生成报告
    report_file = 'mclean_13_factors_corrected_report.txt'
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# Colin McLean 13因子分析报告（修正版）\n\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write("## 数据概览\n")
        f.write(f"- 总股票数: {len(df_factors):,}\n")
        f.write("- 分析期间: 2020年至今\n")
        f.write("- 数据来源: 三大财务报表（利润表、资产负债表、现金流量表）\n\n")
        
        f.write("## 因子覆盖率统计\n")
        for factor, stats in coverage_stats.items():
            f.write(f"- {factor}: {stats['coverage']} 只股票 ({stats['coverage_pct']:.1f}%)\n")
        
        f.write("\n## 13个因子说明\n")
        f.write("一、增长趋势加速因子 (4个):\n")
        f.write("1. revenue_yoy: 营收同比增长率\n")
        f.write("2. revenue_cagr: 近3年营收CAGR\n")
        f.write("3. net_income_yoy: 净利润同比增长率\n")
        f.write("4. profit_revenue_ratio: 盈利营收增长比\n")
        f.write("\n二、现金流持续保障因子 (3个):\n")
        f.write("5. fcf_per_share: 每股自由现金流\n")
        f.write("6. fcf_cagr: FCF复合增长率\n")
        f.write("7. fcf_net_income_ratio: FCF与净利润比值\n")
        f.write("\n三、运营效率因子 (2个):\n")
        f.write("8. operating_margin: 营业利益率（基于EBIT）\n")
        f.write("9. operating_margin_std: 营业利益率标准差\n")
        f.write("\n四、资本效率因子 (2个):\n")
        f.write("10. roic: 可运用资本报酬率(ROIC)\n")
        f.write("11. roic_cagr: 近3年ROIC复合增长率\n")
        f.write("\n五、税务合理性因子 (2个):\n")
        f.write("12. effective_tax_rate: 有效税率\n")
        f.write("13. effective_tax_rate_std: 有效税率标准差\n")
    
    print(f"  分析报告已保存到: {report_file}")
    
    return coverage_stats

def main():
    """主函数"""
    start_time = time.time()
    
    try:
        # 获取财务数据
        df_income, df_balance, df_cash_flow = get_financial_data()
        
        # 处理数据并计算因子
        df_factors = process_and_calculate_factors(df_income, df_balance, df_cash_flow)
        
        # 分析并保存结果
        coverage_stats = analyze_and_save_results(df_factors)
        
        print(f"\n=== 完成！总耗时: {time.time() - start_time:.2f}秒 ===")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 