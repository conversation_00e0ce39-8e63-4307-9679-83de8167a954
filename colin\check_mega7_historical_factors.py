#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Mega7最近10年的因子值，识别0值和映射问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from final_complete_24_factors_with_field_mapping import (
    connect_to_ap_research, 
    calculate_complete_24_factors
)
import pandas as pd
from datetime import datetime, timedelta

def check_mega7_historical_factors():
    """检查Mega7最近10年的因子值"""
    print("🔍 检查Mega7最近10年的因子值...")
    
    # Mega7股票
    mega7_stocks = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META']
    
    # 生成最近10年的日期
    end_date = datetime(2024, 1, 15)
    dates = []
    for i in range(10):
        date = end_date - timedelta(days=365 * i)
        dates.append(date.strftime('%Y-%m-%d'))
    
    print(f"📅 检查日期: {dates}")
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        print("❌ 无法连接到数据库")
        return
    
    try:
        # 存储所有结果
        all_results = []
        zero_value_issues = []
        
        for stock in mega7_stocks:
            print(f"\n📊 处理股票: {stock}")
            print("-" * 50)
            
            for date in dates:
                print(f"   计算日期: {date}")
                
                try:
                    factors = calculate_complete_24_factors(client, stock, date)
                    
                    if factors:
                        # 检查关键因子的0值问题
                        key_factors = [
                            'profit_revenue_ratio', 'operating_margin', 'tech_premium',
                            'revenue_yoy', 'revenue_cagr', 'net_income_yoy',
                            'fcf_per_share', 'roic', 'effective_tax_rate'
                        ]
                        
                        zero_count = 0
                        none_count = 0
                        normal_count = 0
                        
                        for factor in key_factors:
                            if factor in factors:
                                value = factors[factor]
                                if value == 0 or value == 0.0:
                                    zero_count += 1
                                    # 记录0值问题
                                    flag_key = f"{factor}_field_flag"
                                    flag = factors.get(flag_key, "无标志")
                                    zero_value_issues.append({
                                        'stock': stock,
                                        'date': date,
                                        'factor': factor,
                                        'value': value,
                                        'flag': flag
                                    })
                                elif value is None:
                                    none_count += 1
                                else:
                                    normal_count += 1
                        
                        print(f"     ✅ 成功 - 正常值:{normal_count}, 零值:{zero_count}, None值:{none_count}")
                        
                        # 存储结果
                        all_results.append({
                            'stock': stock,
                            'date': date,
                            'normal_count': normal_count,
                            'zero_count': zero_count,
                            'none_count': none_count,
                            'total_factors': len(key_factors)
                        })
                        
                    else:
                        print(f"     ❌ 计算失败")
                        
                except Exception as e:
                    print(f"     ❌ 错误: {e}")
                    continue
        
        # 分析结果
        print(f"\n📊 分析结果:")
        print("=" * 80)
        
        # 统计0值问题
        if zero_value_issues:
            print(f"\n🚨 发现 {len(zero_value_issues)} 个0值问题:")
            print("-" * 60)
            
            # 按因子分组统计
            factor_zero_counts = {}
            for issue in zero_value_issues:
                factor = issue['factor']
                if factor not in factor_zero_counts:
                    factor_zero_counts[factor] = []
                factor_zero_counts[factor].append(issue)
            
            for factor, issues in factor_zero_counts.items():
                print(f"\n❌ {factor}: {len(issues)} 个0值")
                
                # 显示前5个案例
                for i, issue in enumerate(issues[:5]):
                    print(f"   {i+1}. {issue['stock']} {issue['date']}: {issue['flag']}")
                
                if len(issues) > 5:
                    print(f"   ... 还有 {len(issues)-5} 个案例")
        
        # 统计整体情况
        print(f"\n📈 整体统计:")
        print("-" * 60)
        
        df_results = pd.DataFrame(all_results)
        if not df_results.empty:
            print(f"总计算次数: {len(df_results)}")
            print(f"平均正常值数量: {df_results['normal_count'].mean():.1f}")
            print(f"平均零值数量: {df_results['zero_count'].mean():.1f}")
            print(f"平均None值数量: {df_results['none_count'].mean():.1f}")
            
            # 按股票统计
            print(f"\n📊 按股票统计:")
            stock_stats = df_results.groupby('stock').agg({
                'normal_count': 'mean',
                'zero_count': 'mean',
                'none_count': 'mean'
            }).round(1)
            
            for stock in stock_stats.index:
                stats = stock_stats.loc[stock]
                print(f"   {stock}: 正常值={stats['normal_count']}, 零值={stats['zero_count']}, None值={stats['none_count']}")
        
        # 识别需要修复的字段映射
        print(f"\n🔧 需要修复的字段映射:")
        print("-" * 60)
        
        mapping_issues = {}
        for issue in zero_value_issues:
            flag = issue['flag']
            if 'NA' in flag:  # 字段映射失败
                factor = issue['factor']
                if factor not in mapping_issues:
                    mapping_issues[factor] = set()
                mapping_issues[factor].add(flag)
        
        for factor, flags in mapping_issues.items():
            print(f"\n❌ {factor}:")
            for flag in flags:
                print(f"   {flag}")
        
        if not mapping_issues:
            print("   ✅ 没有发现明显的字段映射问题")
        
    except Exception as e:
        print(f"❌ 检查过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            client.disconnect()
            print("🔌 数据库连接已关闭")

if __name__ == "__main__":
    check_mega7_historical_factors()
