#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mega7股票24因子标准化值排序分析
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from final_complete_24_factors_with_field_mapping import (
    connect_to_ap_research, 
    calculate_complete_24_factors,
    standardize_factors
)
import pandas as pd
import numpy as np

def calculate_mega7_standardized_factors():
    """计算Mega7股票的标准化因子值"""
    print("🚀 开始计算Mega7股票24因子标准化值...")
    
    # 连接ClickHouse
    client = connect_to_ap_research()
    if not client:
        print("❌ 无法连接到ClickHouse数据库")
        return None
    
    try:
        # Mega7股票列表
        mega7_stocks = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META']
        calculation_date = '2024-01-15'  # 使用历史日期避免未来数据
        
        print(f"📊 处理股票: {', '.join(mega7_stocks)}")
        print(f"📅 计算日期: {calculation_date}")
        
        # 存储所有股票的因子数据
        all_factors_data = {}
        
        for i, stock in enumerate(mega7_stocks, 1):
            print(f"\n📈 处理进度: {i}/{len(mega7_stocks)} - {stock}")
            try:
                # 1. 计算原始因子
                factors = calculate_complete_24_factors(client, stock, calculation_date)
                if factors:
                    # 2. 进行标准化
                    factors = standardize_factors(factors, client, stock, calculation_date)
                    all_factors_data[stock] = factors
                    print(f"   ✅ {stock}: 因子计算和标准化完成")
                else:
                    print(f"   ❌ {stock}: 因子计算失败")
            except Exception as e:
                print(f"   ❌ {stock}: 处理失败 - {e}")
                continue
        
        return all_factors_data
        
    except Exception as e:
        print(f"❌ 计算过程中出现错误: {e}")
        return None
    finally:
        if client:
            client.disconnect()
            print("🔌 数据库连接已关闭")

def analyze_factor_rankings(all_factors_data):
    """分析因子排名"""
    if not all_factors_data:
        print("❌ 没有可分析的因子数据")
        return
    
    print("\n" + "="*80)
    print("📊 MEGA7股票24因子标准化值排名分析")
    print("="*80)
    
    # 提取标准化后的因子值
    normalized_data = {}
    factor_names = []
    
    for stock, factors in all_factors_data.items():
        stock_data = {}
        for key, value in factors.items():
            if key.endswith('_normalized'):
                factor_name = key.replace('_normalized', '')
                if factor_name not in factor_names:
                    factor_names.append(factor_name)
                stock_data[factor_name] = value
        normalized_data[stock] = stock_data
    
    # 创建DataFrame
    df = pd.DataFrame.from_dict(normalized_data, orient='index')
    
    print(f"📈 分析维度:")
    print(f"   股票数量: {len(df)}")
    print(f"   因子数量: {len(factor_names)}")
    
    # 按因子分析排名
    print("\n" + "="*80)
    print("🏆 各因子Mega7股票排名 (标准化值从高到低)")
    print("="*80)
    
    for factor in factor_names:
        if factor in df.columns:
            # 获取该因子的所有值
            factor_values = df[factor].dropna()
            
            if len(factor_values) > 0:
                # 按标准化值排序（从高到低）
                sorted_rankings = factor_values.sort_values(ascending=False)
                
                print(f"\n📊 {factor}:")
                print(f"   标准化方法: {all_factors_data[sorted_rankings.index[0]].get(f'{factor}_standardization_method', 'Unknown')}")
                
                # 显示排名
                for rank, (stock, value) in enumerate(sorted_rankings.items(), 1):
                    original_value = all_factors_data[stock].get(factor, 'N/A')
                    print(f"   {rank:2d}. {stock:5s}: {value:8.4f} (原始值: {original_value})")
                
                # 显示统计信息
                print(f"   统计: 均值={factor_values.mean():.4f}, 标准差={factor_values.std():.4f}")
                print(f"   范围: [{factor_values.min():.4f}, {factor_values.max():.4f}]")
            else:
                print(f"\n📊 {factor}: 无有效数据")
    
    # 按股票分析综合表现
    print("\n" + "="*80)
    print("🏆 Mega7股票综合因子表现分析")
    print("="*80)
    
    # 计算每个股票的平均标准化因子值
    stock_avg_scores = {}
    stock_factor_counts = {}
    
    for stock in df.index:
        valid_factors = df.loc[stock].dropna()
        if len(valid_factors) > 0:
            stock_avg_scores[stock] = valid_factors.mean()
            stock_factor_counts[stock] = len(valid_factors)
    
    # 按平均分排序
    sorted_stocks = sorted(stock_avg_scores.items(), key=lambda x: x[1], reverse=True)
    
    print(f"\n📈 股票综合排名 (按平均标准化因子值):")
    for rank, (stock, avg_score) in enumerate(sorted_stocks, 1):
        factor_count = stock_factor_counts[stock]
        print(f"   {rank:2d}. {stock:5s}: 平均分={avg_score:8.4f} (有效因子: {factor_count}/24)")
    
    # 显示每个股票的详细因子分布
    print(f"\n📊 各股票因子分布详情:")
    for stock in sorted_stocks:
        stock_name = stock[0]
        stock_data = df.loc[stock_name]
        
        # 统计不同标准化方法的因子数量
        method_counts = {}
        for factor in factor_names:
            if factor in stock_data and pd.notna(stock_data[factor]):
                method = all_factors_data[stock_name].get(f'{factor}_standardization_method', 'Unknown')
                if method not in method_counts:
                    method_counts[method] = 0
                method_counts[method] += 1
        
        print(f"\n   {stock_name}:")
        for method, count in method_counts.items():
            print(f"     {method}: {count} 个因子")
        
        # 显示表现最好和最差的因子
        valid_factors = stock_data.dropna()
        if len(valid_factors) > 0:
            best_factor = valid_factors.idxmax()
            worst_factor = valid_factors.idxmin()
            print(f"     最佳因子: {best_factor} = {valid_factors[best_factor]:.4f}")
            print(f"     最差因子: {worst_factor} = {valid_factors[worst_factor]:.4f}")
    
    # 保存结果到CSV
    timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
    filename = f"mega7_factor_rankings_{timestamp}.csv"
    
    # 添加标准化方法信息
    method_df = pd.DataFrame(index=df.index, columns=df.columns)
    for stock in df.index:
        for factor in df.columns:
            method_df.loc[stock, factor] = all_factors_data[stock].get(f'{factor}_standardization_method', 'Unknown')
    
    # 合并数据
    result_df = pd.DataFrame()
    for stock in df.index:
        stock_row = {}
        for factor in df.columns:
            stock_row[f'{factor}_normalized'] = df.loc[stock, factor]
            stock_row[f'{factor}_method'] = method_df.loc[stock, factor]
        result_df = pd.concat([result_df, pd.DataFrame([stock_row])], ignore_index=True)
    
    result_df.index = df.index
    result_df.to_csv(filename, encoding='utf-8-sig')
    print(f"\n📁 详细结果已保存至: {filename}")
    
    return df

def main():
    """主函数"""
    print("🚀 Mega7股票24因子标准化值排名分析")
    print("="*80)
    
    # 1. 计算标准化因子值
    all_factors_data = calculate_mega7_standardized_factors()
    
    if all_factors_data:
        # 2. 分析排名
        analyze_factor_rankings(all_factors_data)
        print("\n✅ 分析完成！")
    else:
        print("❌ 无法获取因子数据进行分析")

if __name__ == "__main__":
    main()
