from clickhouse_driver import Client

client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')

print("=== 检查价格表结构 ===")

# 1. 查看表结构
print("\n1. priority_quality_stock_hfq表结构:")
try:
    schema_result = client.execute("DESCRIBE priority_quality_stock_hfq")
    for row in schema_result:
        print(f"   {row[0]}: {row[1]}")
except Exception as e:
    print(f"   ❌ 查看表结构失败: {e}")

# 2. 查看样本数据
print("\n2. 样本数据:")
try:
    sample_result = client.execute("SELECT * FROM priority_quality_stock_hfq LIMIT 3")
    if sample_result:
        # 获取列名
        columns_result = client.execute("DESCRIBE priority_quality_stock_hfq")
        column_names = [row[0] for row in columns_result]
        print(f"   列名: {column_names}")
        
        for i, row in enumerate(sample_result, 1):
            print(f"   样本{i}: {dict(zip(column_names, row))}")
    else:
        print("   ⚠️ 表为空")
except Exception as e:
    print(f"   ❌ 查看样本数据失败: {e}")

client.disconnect()

