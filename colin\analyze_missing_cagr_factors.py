import pandas as pd
from clickhouse_driver import Client

# 连接数据库
client = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 分析TSLA和META的CAGR因子缺失原因 ===")
print()

# 检查这两只股票的历史数据情况
target_stocks = ['TSLA', 'META']

for stock in target_stocks:
    print(f"🔍 分析 {stock}:")
    
    # 查询该股票的年报数据
    query = f"""
    SELECT 
        stock_symbol,
        financial_period_absolute,
        item_name,
        item_value,
        effective_date
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = '{stock}'
      AND statement_type = 'Income Statement'
      AND item_name IN ('Free Cash Flow', 'Normalized Net Income - Bottom Line')
      AND substring(financial_period_absolute, 1, 2) = 'FY'
    ORDER BY financial_period_absolute, item_name
    """
    
    try:
        result = client.execute(query)
        
        if result:
            print(f"  📊 找到 {len(result)} 条年报数据")
            
            # 按年份和科目整理数据
            data_by_year = {}
            for row in result:
                symbol, period, item, value, date = row
                year = period[2:6]  # 提取年份
                
                if year not in data_by_year:
                    data_by_year[year] = {}
                data_by_year[year][item] = value
            
            print(f"  📅 数据年份范围: {min(data_by_year.keys())} - {max(data_by_year.keys())}")
            print(f"  📈 总年数: {len(data_by_year)} 年")
            
            # 检查FCF数据完整性
            fcf_years = []
            roic_years = []
            
            for year, data in sorted(data_by_year.items()):
                fcf = data.get('Free Cash Flow')
                net_income = data.get('Normalized Net Income - Bottom Line')
                
                if fcf is not None:
                    fcf_years.append(year)
                if net_income is not None:
                    roic_years.append(year)
                
                print(f"    {year}: FCF={fcf}, Net Income={net_income}")
            
            print(f"  💰 有FCF数据的年份: {len(fcf_years)} 年 - {fcf_years}")
            print(f"  💼 有Net Income数据的年份: {len(roic_years)} 年 - {roic_years}")
            
            # 分析CAGR计算要求
            print(f"  🎯 CAGR因子要求分析:")
            print(f"    - fcf_cagr需要: ≥3年FCF数据")
            print(f"    - roic_cagr需要: ≥3年Net Income数据")
            print(f"    - {stock} FCF数据年数: {len(fcf_years)} ({'✅ 满足' if len(fcf_years) >= 3 else '❌ 不足'})")
            print(f"    - {stock} ROIC数据年数: {len(roic_years)} ({'✅ 满足' if len(roic_years) >= 3 else '❌ 不足'})")
            
        else:
            print(f"  ❌ 未找到 {stock} 的年报数据")
            
    except Exception as e:
        print(f"  ❌ 查询 {stock} 数据时出错: {e}")
    
    print()

print("=== 进一步检查股票上市时间和数据质量 ===")
print()

# 检查股票的上市时间和数据覆盖
for stock in target_stocks:
    print(f"🔍 {stock} 详细分析:")
    
    # 查询该股票所有年报数据的年份分布
    query = f"""
    SELECT 
        substring(financial_period_absolute, 3, 4) as year,
        COUNT(DISTINCT item_name) as item_count,
        MIN(effective_date) as earliest_date,
        MAX(effective_date) as latest_date
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = '{stock}'
      AND substring(financial_period_absolute, 1, 2) = 'FY'
    GROUP BY year
    ORDER BY year
    """
    
    try:
        result = client.execute(query)
        
        if result:
            print(f"  📊 年报数据分布:")
            for year, item_count, earliest, latest in result:
                print(f"    {year}: {item_count}个科目 (最早:{earliest}, 最晚:{latest})")
                
            # 检查关键科目的数据连续性
            key_items = ['Free Cash Flow', 'Normalized Net Income - Bottom Line', 
                        'Debt - Total', 'Shareholders\' Equity - Attributable to Parent Shareholders - Total']
            
            for item in key_items:
                item_query = f"""
                SELECT 
                    substring(financial_period_absolute, 3, 4) as year,
                    item_value
                FROM priority_quality_fundamental_data_complete_deduped
                WHERE stock_symbol = '{stock}'
                  AND substring(financial_period_absolute, 1, 2) = 'FY'
                  AND item_name = '{item}'
                ORDER BY year
                """
                
                item_result = client.execute(item_query)
                years_with_data = [row[0] for row in item_result if row[1] is not None]
                
                print(f"  📈 {item}:")
                print(f"    有数据年份: {years_with_data} ({len(years_with_data)}年)")
                
                if len(years_with_data) < 3:
                    print(f"    ❌ 数据不足，无法计算CAGR (需要≥3年)")
                else:
                    print(f"    ✅ 数据充足，可计算CAGR")
        
    except Exception as e:
        print(f"  ❌ 查询失败: {e}")
    
    print()

client.disconnect()

