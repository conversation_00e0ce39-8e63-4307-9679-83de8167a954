from clickhouse_driver import Client

client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')

print("=== 检查当前数据状况 ===")

# 检查新建的enhanced表
print("1. 检查quarterly_data_filled_enhanced表...")
try:
    count = client.execute("SELECT COUNT(*) FROM quarterly_data_filled_enhanced")[0][0]
    print(f"   📊 记录数: {count}")
    
    if count == 0:
        print("   ⚠️ 表是空的！之前的填充数据在删除旧表时丢失了")
        print("   💡 需要重新执行数据填充算法")
    else:
        # 显示样本数据
        sample = client.execute("SELECT stock_symbol, financial_period_absolute, item_name, data_status FROM quarterly_data_filled_enhanced LIMIT 5")
        print("   📋 样本数据:")
        for record in sample:
            print(f"      {record}")
            
except Exception as e:
    print(f"   ❌ 检查失败: {e}")

# 检查基础数据是否完整
print("\n2. 检查基础数据完整性...")
try:
    # 年报数据
    annual_stocks = client.execute("""
        SELECT COUNT(DISTINCT stock_symbol) 
        FROM priority_quality_fundamental_data_complete_deduped 
        WHERE financial_period_absolute REGEXP '^FY[0-9]{4}$'
    """)[0][0]
    print(f"   📈 有年报数据的股票: {annual_stocks}")
    
    # 价格数据  
    price_stocks = client.execute("SELECT COUNT(DISTINCT stock_symbol) FROM priority_quality_stock_hfq")[0][0]
    print(f"   📊 有价格数据的股票: {price_stocks}")
    
    # 基础股票池
    base_stocks = client.execute("SELECT COUNT(*) FROM stock_performance_2020_2025_cumulative")[0][0]
    print(f"   📋 基础股票池: {base_stocks}")
    
except Exception as e:
    print(f"   ❌ 基础数据检查失败: {e}")

print("\n3. 之前的数据填充状况...")
print("   📝 根据之前的分析:")
print("   • 年报数据: ✅ 100%完整（821股票）")
print("   • 季报缺失: 仅14个期间（5只股票）")
print("   • 已识别: 1个corrected_real + 14个needs_estimation")
print("   • 状态: 旧表被删除，数据丢失")

print("\n🎯 结论:")
print("   ✅ 基础数据完整，26个因子理论上都可以计算")
print("   ⚠️ 但是季报填充数据在清理旧表时丢失了")
print("   💡 建议: 可以选择直接用年报数据计算24个因子，或重新填充季报数据")

print("\n📊 当前可执行的选项:")
print("   1. 立即计算24个因子（基于100%完整的年报数据）")
print("   2. 重新填充14个缺失季报期间，然后计算全部26个因子")
print("   3. 混合方案：先算24个，同时并行填充季报数据")

