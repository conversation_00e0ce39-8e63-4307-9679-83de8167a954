from clickhouse_driver import Client
import pandas as pd
import numpy as np
from datetime import datetime

def connect_to_ap_research():
    """连接到ap_research数据库"""
    client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')
    print("✅ 连接ap_research数据库成功")
    return client

def check_field_data(client, stock_symbol):
    """检查特定股票的字段数据"""
    print(f"\n🔍 检查股票 {stock_symbol} 的字段数据")
    print("=" * 60)
    
    # 检查operating_cash_flow相关字段
    print("\n📊 检查经营现金流相关字段:")
    query = f"""
    SELECT DISTINCT item_name, value, financial_period_absolute, period_end_date
    FROM fundamental_data_with_announcement_dates
    WHERE stock_symbol = '{stock_symbol}'
      AND item_name LIKE '%Cash Flow%'
      AND item_name LIKE '%Operating%'
      AND financial_period_absolute REGEXP '^FY[0-9]{{4}}$'
    ORDER BY financial_period_absolute DESC
    LIMIT 10
    """
    
    result = client.execute(query)
    if result:
        df = pd.DataFrame(result, columns=['item_name', 'value', 'financial_period_absolute', 'period_end_date'])
        print(df.to_string(index=False))
    else:
        print("❌ 未找到经营现金流相关字段")
    
    # 检查total_liabilities相关字段
    print("\n📊 检查总负债相关字段:")
    query = f"""
    SELECT DISTINCT item_name, value, financial_period_absolute, period_end_date
    FROM fundamental_data_with_announcement_dates
    WHERE stock_symbol = '{stock_symbol}'
      AND item_name LIKE '%Liabilities%'
      AND financial_period_absolute REGEXP '^FY[0-9]{{4}}$'
    ORDER BY financial_period_absolute DESC
    LIMIT 10
    """
    
    result = client.execute(query)
    if result:
        df = pd.DataFrame(result, columns=['item_name', 'value', 'financial_period_absolute', 'period_end_date'])
        print(df.to_string(index=False))
    else:
        print("❌ 未找到总负债相关字段")
    
    # 检查R&D相关字段
    print("\n📊 检查研发费用相关字段:")
    query = f"""
    SELECT DISTINCT item_name, value, financial_period_absolute, period_end_date
    FROM fundamental_data_with_announcement_dates
    WHERE stock_symbol = '{stock_symbol}'
      AND item_name LIKE '%Research%'
      AND financial_period_absolute REGEXP '^FY[0-9]{{4}}$'
    ORDER BY financial_period_absolute DESC
    LIMIT 10
    """
    
    result = client.execute(query)
    if result:
        df = pd.DataFrame(result, columns=['item_name', 'value', 'financial_period_absolute', 'period_end_date'])
        print(df.to_string(index=False))
    else:
        print("❌ 未找到研发费用相关字段")
    
    # 检查收入相关字段
    print("\n📊 检查收入相关字段:")
    query = f"""
    SELECT DISTINCT item_name, value, financial_period_absolute, period_end_date
    FROM fundamental_data_with_announcement_dates
    WHERE stock_symbol = '{stock_symbol}'
      AND item_name LIKE '%Revenue%'
      AND financial_period_absolute REGEXP '^FY[0-9]{{4}}$'
    ORDER BY financial_period_absolute DESC
    LIMIT 10
    """
    
    result = client.execute(query)
    if result:
        df = pd.DataFrame(result, columns=['item_name', 'value', 'financial_period_absolute', 'period_end_date'])
        print(df.to_string(index=False))
    else:
        print("❌ 未找到收入相关字段")

def test_field_mapping_calculation(client, stock_symbol):
    """测试字段映射计算"""
    print(f"\n🧮 测试 {stock_symbol} 的字段映射计算")
    print("=" * 60)
    
    # 定义字段映射
    field_mappings = {
        'operating_cash_flow': [
            'Net Cash Flow from Operating Activities',
            'Net Cash Flow from Operating Activities - Total',
            'Cash Flow from Operations'
        ],
        'total_liabilities': [
            'Total Liabilities',
            'Total Liabilities & Shareholders Equity'
        ],
        'rd_expense': [
            'Research & Development Expense',
            'Research & Development Expense - Supplemental'
        ],
        'revenue': [
            'Revenue from Business Activities - Total',
            'Revenue from Goods & Services'
        ]
    }
    
    def get_field_with_priority(data, field_priority_list):
        """按优先级获取字段值"""
        for field_name in field_priority_list:
            if field_name in data:
                return data[field_name], field_name
        return None, None
    
    # 获取最新数据
    query = f"""
    SELECT 
        item_name,
        value,
        financial_period_absolute,
        period_end_date
    FROM fundamental_data_with_announcement_dates
    WHERE stock_symbol = '{stock_symbol}'
      AND financial_period_absolute REGEXP '^FY[0-9]{{4}}$'
      AND item_name IN (
        'Net Cash Flow from Operating Activities',
        'Net Cash Flow from Operating Activities - Total',
        'Cash Flow from Operations',
        'Total Liabilities',
        'Total Liabilities & Shareholders Equity',
        'Research & Development Expense',
        'Research & Development Expense - Supplemental',
        'Revenue from Business Activities - Total',
        'Revenue from Goods & Services'
      )
    ORDER BY financial_period_absolute DESC, period_end_date DESC
    """
    
    result = client.execute(query)
    if result:
        df = pd.DataFrame(result, columns=['item_name', 'value', 'financial_period_absolute', 'period_end_date'])
        
        # 转换为字典格式
        data_dict = {}
        for _, row in df.iterrows():
            data_dict[row['item_name']] = row['value']
        
        print("📊 找到的字段数据:")
        for field, value in data_dict.items():
            print(f"  {field}: {value}")
        
        # 测试现金流覆盖率计算
        print("\n🧮 测试现金流覆盖率计算:")
        ocf, ocf_flag = get_field_with_priority(data_dict, field_mappings['operating_cash_flow'])
        liab, liab_flag = get_field_with_priority(data_dict, field_mappings['total_liabilities'])
        
        print(f"  经营现金流: {ocf} ({ocf_flag})")
        print(f"  总负债: {liab} ({liab_flag})")
        
        if ocf is not None and liab is not None and liab > 0:
            coverage = (ocf / liab) * 100
            print(f"  现金流覆盖率: {coverage:.2f}%")
        else:
            print("  ❌ 无法计算现金流覆盖率")
        
        # 测试tech_gap_warning计算
        print("\n🧮 测试tech_gap_warning计算:")
        rd, rd_flag = get_field_with_priority(data_dict, field_mappings['rd_expense'])
        revenue, revenue_flag = get_field_with_priority(data_dict, field_mappings['revenue'])
        
        print(f"  研发费用: {rd} ({rd_flag})")
        print(f"  收入: {revenue} ({revenue_flag})")
        
        if rd is not None and revenue is not None and revenue > 0:
            tech_premium = (rd / revenue) * 100
            print(f"  技术溢价: {tech_premium:.2f}%")
        else:
            print("  ❌ 无法计算技术溢价")
    else:
        print("❌ 未找到相关字段数据")

def main():
    """主函数"""
    client = connect_to_ap_research()
    
    # 检查Mega7股票
    mega7_stocks = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'META', 'TSLA']
    
    for stock in mega7_stocks:
        check_field_data(client, stock)
        test_field_mapping_calculation(client, stock)
        print("\n" + "="*80 + "\n")
    
    client.disconnect()

if __name__ == "__main__":
    main()
