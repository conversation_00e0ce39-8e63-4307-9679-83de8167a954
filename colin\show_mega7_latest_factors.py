#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
展示Mega7最新的所有因子值
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from final_complete_24_factors_with_field_mapping import (
    connect_to_ap_research, 
    calculate_complete_24_factors
)
import pandas as pd

def show_mega7_latest_factors():
    """展示Mega7最新的所有因子值"""
    print("📊 Mega7最新因子值展示")
    print("=" * 80)
    
    # Mega7股票
    mega7_stocks = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META']
    calculation_date = '2024-01-15'
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        print("❌ 无法连接到数据库")
        return
    
    try:
        all_factors_data = []
        
        for stock in mega7_stocks:
            print(f"\n🔄 计算 {stock} 的因子...")
            
            factors = calculate_complete_24_factors(client, stock, calculation_date)
            
            if factors:
                # 提取所有因子值（排除元数据和标志）
                factor_values = {}
                for key, value in factors.items():
                    if not key.endswith('_field_flag') and not key.endswith('_calculation_details'):
                        if key not in ['stock_symbol', 'calculation_date', 'fiscal_year', 'period_end_date', 'effective_date']:
                            factor_values[key] = value
                
                factor_values['stock'] = stock
                all_factors_data.append(factor_values)
                print(f"   ✅ 完成，获得 {len(factor_values)-1} 个因子")
            else:
                print(f"   ❌ 计算失败")
        
        if not all_factors_data:
            print("❌ 没有获得任何因子数据")
            return
        
        # 转换为DataFrame
        df = pd.DataFrame(all_factors_data)
        df = df.set_index('stock')
        
        # 获取所有因子列
        factor_columns = [col for col in df.columns if col != 'stock']
        
        print(f"\n📋 Mega7完整因子矩阵 ({len(factor_columns)} 个因子)")
        print("=" * 120)
        
        # 按因子类别分组显示
        factor_categories = {
            '🔬 技术创新因子': [
                'tech_premium', 'tech_gap_warning', 'patent_density'
            ],
            '💰 盈利能力因子': [
                'profit_revenue_ratio', 'operating_margin', 'roic'
            ],
            '📈 增长因子': [
                'revenue_yoy', 'revenue_cagr', 'net_income_yoy', 'fcf_cagr', 'roic_cagr'
            ],
            '💵 现金流因子': [
                'fcf_quality', 'fcf_per_share', 'cash_flow_coverage_ratio'
            ],
            '🏦 财务健康因子': [
                'financial_health', 'ecosystem_cash_ratio', 'dynamic_safety_margin'
            ],
            '📊 稳定性因子': [
                'operating_margin_std', 'revenue_growth_continuity', 'roce_volatility'
            ],
            '⚠️ 风险因子': [
                'valuation_bubble_signal', 'effective_tax_rate', 'effective_tax_rate_improvement', 'effective_tax_rate_std'
            ]
        }
        
        for category, factors in factor_categories.items():
            print(f"\n{category}")
            print("-" * 80)
            
            # 检查哪些因子存在于数据中
            available_factors = [f for f in factors if f in df.columns]
            
            if available_factors:
                category_df = df[available_factors].round(2)
                
                # 转置以便更好地显示
                category_df_t = category_df.T
                
                print(category_df_t.to_string())
                
                # 统计每个因子的状态
                print(f"\n📊 {category} 统计:")
                for factor in available_factors:
                    values = df[factor]
                    non_null_count = values.notna().sum()
                    zero_count = (values == 0).sum()
                    print(f"   {factor}: 有效值={non_null_count}/7, 零值={zero_count}")
            else:
                print("   ❌ 该类别的因子都不存在于数据中")
        
        # 整体统计
        print(f"\n📊 整体统计")
        print("=" * 80)
        
        total_factors = len(factor_columns)
        
        # 按股票统计
        print(f"\n按股票统计:")
        for stock in mega7_stocks:
            if stock in df.index:
                stock_data = df.loc[stock]
                non_null_count = stock_data.notna().sum()
                zero_count = (stock_data == 0).sum()
                none_count = stock_data.isna().sum()
                
                print(f"   {stock}: 总因子={total_factors}, 有效值={non_null_count}, 零值={zero_count}, None值={none_count}")
        
        # 按因子统计
        print(f"\n按因子统计 (覆盖率):")
        factor_coverage = {}
        for factor in factor_columns:
            if factor in df.columns:
                coverage = df[factor].notna().sum()
                factor_coverage[factor] = coverage
        
        # 按覆盖率排序
        sorted_factors = sorted(factor_coverage.items(), key=lambda x: x[1], reverse=True)
        
        for factor, coverage in sorted_factors:
            percentage = (coverage / 7) * 100
            if coverage == 7:
                status = "✅"
            elif coverage >= 5:
                status = "⚠️"
            else:
                status = "❌"
            print(f"   {status} {factor}: {coverage}/7 ({percentage:.0f}%)")
        
        # 保存结果到CSV
        output_file = f"mega7_complete_factors_{calculation_date.replace('-', '')}.csv"
        df.to_csv(output_file)
        print(f"\n💾 结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            client.disconnect()
            print("🔌 数据库连接已关闭")

if __name__ == "__main__":
    show_mega7_latest_factors()
