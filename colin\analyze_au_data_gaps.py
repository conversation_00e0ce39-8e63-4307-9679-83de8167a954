from clickhouse_driver import Client

# 连接到lseg数据库
client_lseg = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='lseg'
)

print("=== 分析AU.NB的数据间隙 ===")

tables = ['income_statement', 'balance_sheet_history', 'cash_flow']

# 1. 查看AU.NB的完整时间线
print("\n1. AU.NB的完整时间线分析...")

for table in tables:
    print(f"\n  {table}表中AU.NB的数据:")
    
    query_timeline = f"""
    SELECT 
        financial_period_absolute,
        COUNT(*) as record_count
    FROM {table}
    WHERE instrument = 'AU.NB'
      AND financial_period_absolute != ''
    GROUP BY financial_period_absolute
    ORDER BY financial_period_absolute
    """
    
    try:
        result_timeline = client_lseg.execute(query_timeline)
        
        if result_timeline:
            print(f"    共有 {len(result_timeline)} 个期间")
            
            # 显示所有期间
            for period, count in result_timeline:
                print(f"      {period}: {count}条记录")
        else:
            print(f"    没有数据")
            
    except Exception as e:
        print(f"    查询出错: {e}")

# 2. 分析时间间隙
print(f"\n2. 识别时间间隙...")

# 获取所有期间并分析连续性
all_periods = set()
for table in tables:
    query_periods = f"""
    SELECT DISTINCT financial_period_absolute
    FROM {table}
    WHERE instrument = 'AU.NB'
      AND financial_period_absolute != ''
    """
    
    try:
        result_periods = client_lseg.execute(query_periods)
        table_periods = set(period for (period,) in result_periods)
        all_periods.update(table_periods)
    except Exception as e:
        continue

# 按年份分组分析
periods_by_year = {}
for period in all_periods:
    if 'FY' in period:
        # 提取年份
        if period.startswith('FY') and len(period) >= 6:
            year = period[2:6]
            if year not in periods_by_year:
                periods_by_year[year] = []
            periods_by_year[year].append(period)

print(f"  AU.NB数据覆盖的年份:")
for year in sorted(periods_by_year.keys()):
    periods = sorted(periods_by_year[year])
    print(f"    {year}年: {', '.join(periods)}")

# 3. 识别缺失的年份
print(f"\n3. 识别缺失的年份...")

if periods_by_year:
    min_year = int(min(periods_by_year.keys()))
    max_year = int(max(periods_by_year.keys()))
    
    print(f"  数据年份范围: {min_year} - {max_year}")
    
    missing_years = []
    for year in range(min_year, max_year + 1):
        if str(year) not in periods_by_year:
            missing_years.append(year)
    
    if missing_years:
        print(f"  缺失的年份: {', '.join(map(str, missing_years))}")
    else:
        print(f"  没有完全缺失的年份")

# 4. 检查每年的季报完整性
print(f"\n4. 检查每年的季报完整性...")

for year in sorted(periods_by_year.keys()):
    periods = periods_by_year[year]
    
    # 检查是否有完整的季报
    expected_quarters = [f'FY{year}Q1', f'FY{year}Q2', f'FY{year}Q3', f'FY{year}Q4']
    existing_quarters = [p for p in periods if p in expected_quarters]
    missing_quarters = [q for q in expected_quarters if q not in existing_quarters]
    
    # 检查年报
    annual_report = f'FY{year}'
    has_annual = annual_report in periods
    
    # 检查半年报
    half_year_reports = [f'FY{year}H1', f'FY{year}H2']
    existing_half_year = [h for h in half_year_reports if h in periods]
    
    print(f"  {year}年:")
    print(f"    年报: {'✅' if has_annual else '❌'}")
    print(f"    季报: {len(existing_quarters)}/4 ({', '.join(existing_quarters) if existing_quarters else '无'})")
    if missing_quarters:
        print(f"    缺失季报: {', '.join(missing_quarters)}")
    if existing_half_year:
        print(f"    半年报: {', '.join(existing_half_year)}")

# 5. 对比AIMAU.NB的数据
print(f"\n5. 对比AIMAU.NB的数据...")

aimau_periods = set()
for table in tables:
    query_aimau = f"""
    SELECT DISTINCT financial_period_absolute
    FROM {table}
    WHERE instrument = 'AIMAU.NB'
      AND financial_period_absolute != ''
    """
    
    try:
        result_aimau = client_lseg.execute(query_aimau)
        table_aimau_periods = set(period for (period,) in result_aimau)
        aimau_periods.update(table_aimau_periods)
    except Exception as e:
        continue

# 按年份分组AIMAU数据
aimau_by_year = {}
for period in aimau_periods:
    if 'FY' in period and len(period) >= 6:
        year = period[2:6]
        if year not in aimau_by_year:
            aimau_by_year[year] = []
        aimau_by_year[year].append(period)

print(f"  AIMAU.NB数据覆盖的年份:")
for year in sorted(aimau_by_year.keys()):
    periods = sorted(aimau_by_year[year])
    print(f"    {year}年: {', '.join(periods)}")

# 6. 分析AU.NB和AIMAU.NB的关系
print(f"\n6. AU.NB vs AIMAU.NB 数据覆盖对比:")

all_years = set(periods_by_year.keys()) | set(aimau_by_year.keys())
for year in sorted(all_years):
    au_has_data = year in periods_by_year
    aimau_has_data = year in aimau_by_year
    
    status = ""
    if au_has_data and aimau_has_data:
        status = "两者都有"
    elif au_has_data and not aimau_has_data:
        status = "仅AU.NB有"
    elif not au_has_data and aimau_has_data:
        status = "仅AIMAU.NB有"
    else:
        status = "都没有"
    
    print(f"  {year}年: {status}")

print(f"\n=== 分析完成 ===")

# 总结
print(f"\n=== 总结 ===")
print(f"AU.NB的'数据间隙'指的是:")
print(f"1. 某些年份完全没有数据")
print(f"2. 某些年份只有部分季度的数据")
print(f"3. 数据在时间轴上不连续")
print(f"4. 可能与AIMAU.NB形成互补关系（不同时期使用不同代码）")

