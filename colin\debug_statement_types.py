from clickhouse_driver import C<PERSON>

def connect_to_ap_research():
    """连接到ap_research数据库"""
    client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')
    print("✅ 连接ap_research数据库成功")
    return client

def debug_statement_types():
    """调试statement_type和数据"""
    
    client = connect_to_ap_research()
    
    # 1. 检查所有的statement_type
    print("1. 检查所有statement_type:")
    print("=" * 60)
    
    query1 = """
    SELECT DISTINCT statement_type, COUNT(*) as count
    FROM priority_quality_fundamental_data_complete_deduped
    GROUP BY statement_type
    ORDER BY count DESC
    """
    
    result1 = client.execute(query1)
    for row in result1:
        print(f"   {row[0]:<30}: {row[1]:,}")
    
    print()
    
    # 2. 检查NVDA的数据
    print("2. 检查NVDA的数据:")
    print("=" * 60)
    
    query2 = """
    SELECT DISTINCT statement_type, financial_period_absolute, COUNT(*) as count
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = 'NVDA'
      AND financial_period_absolute REGEXP '^FY[0-9]{4}$'
    GROUP BY statement_type, financial_period_absolute
    ORDER BY financial_period_absolute DESC, statement_type
    LIMIT 20
    """
    
    result2 = client.execute(query2)
    for row in result2:
        print(f"   {row[0]:<25} {row[1]:<10}: {row[2]:,}")
    
    print()
    
    # 3. 检查正确的statement_type过滤
    print("3. 测试不同的statement_type过滤:")
    print("=" * 60)
    
    test_filters = [
        "('Income Statement', 'Balance Sheet', 'Cash Flow Statement')",
        "('income_statement_h', 'balance_sheet_h', 'cash_flow_h')",
        "('income_statement', 'balance_sheet', 'cash_flow')"
    ]
    
    for filter_str in test_filters:
        query3 = f"""
        SELECT COUNT(*) as count
        FROM priority_quality_fundamental_data_complete_deduped
        WHERE stock_symbol = 'NVDA'
          AND statement_type IN {filter_str}
          AND financial_period_absolute REGEXP '^FY[0-9]{{4}}$'
        """
        
        result3 = client.execute(query3)
        count = result3[0][0] if result3 else 0
        print(f"   {filter_str:<50}: {count:,}")
    
    print()
    
    # 4. 检查具体的项目数据
    print("4. 检查NVDA的具体项目数据:")
    print("=" * 60)
    
    query4 = """
    SELECT 
        financial_period_absolute,
        statement_type,
        item_name,
        value
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = 'NVDA'
      AND financial_period_absolute = 'FY2024'
      AND item_name LIKE '%Revenue%'
    ORDER BY item_name
    LIMIT 10
    """
    
    result4 = client.execute(query4)
    for row in result4:
        print(f"   {row[0]} | {row[1]:<20} | {row[2]:<40} | {row[3]:,.0f}")

if __name__ == "__main__":
    debug_statement_types()

