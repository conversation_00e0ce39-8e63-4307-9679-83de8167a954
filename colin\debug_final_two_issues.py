#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试最后两个None问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from final_complete_24_factors_with_field_mapping import (
    connect_to_ap_research, 
    get_stock_data,
    get_price_data,
    get_field_with_priority
)
import pandas as pd

def debug_final_two_issues():
    """调试最后两个None问题"""
    print("🔍 调试最后两个None问题...")
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        print("❌ 无法连接到数据库")
        return
    
    try:
        test_stock = 'AAPL'
        calculation_date = '2024-01-15'
        
        print(f"📊 测试股票: {test_stock}")
        print(f"📅 计算日期: {calculation_date}")
        
        # 获取数据
        stock_data = get_stock_data(client, test_stock, calculation_date)
        price_data = get_price_data(client, test_stock, calculation_date)
        
        pivot_data = stock_data.pivot_table(
            index=['financial_period_absolute', 'period_end_date', 'effective_date'],
            columns='item_name',
            values='value',
            aggfunc='first'
        ).reset_index()
        
        latest_data = pivot_data.iloc[-1]
        
        # 字段映射
        field_mappings = {
            'intangible_assets': [
                'Intangible Assets - Total - Net',
                'Intangible Assets - excluding Goodwill - Net - Total'
            ],
            'revenue': [
                'Revenue from Business Activities - Total',
                'Revenue from Goods & Services'
            ],
            'net_income': [
                'Normalized Net Income - Bottom Line',
                'Net Income - Basic - including Extraordinary Items Applicable to Common - Total',
                'Net Income - Diluted - including Extraordinary Items Applicable to Common - Total',
                'Net Income before Minority Interest',
                'Net Income after Minority Interest',
                'Net Income (Loss) - Total',
                'Net Income - Total'
            ],
            'shares': [
                'Common Shares - Outstanding - Total',
                'Shares Outstanding - Diluted Average',
                'Shares used to calculate Diluted EPS - Total'
            ]
        }
        
        print(f"\n🔍 问题1: patent_density")
        print("-" * 40)
        
        # 检查无形资产
        intangible_assets, intangible_priority = get_field_with_priority(latest_data, field_mappings['intangible_assets'])
        revenue, revenue_priority = get_field_with_priority(latest_data, field_mappings['revenue'])
        
        print(f"   无形资产: {intangible_assets} (优先级: {intangible_priority})")
        print(f"   收入: {revenue} (优先级: {revenue_priority})")
        
        if intangible_assets is None or pd.isna(intangible_assets):
            print(f"   ❌ 无形资产数据确实不存在，patent_density无法计算")
            print(f"   💡 建议: 对于没有无形资产的公司，可以设置patent_density为0")
        else:
            print(f"   ✅ 无形资产数据存在，应该能计算")
        
        print(f"\n🔍 问题2: valuation_bubble_signal")
        print("-" * 40)
        
        # 检查估值泡沫信号的所有条件
        net_income, ni_priority = get_field_with_priority(latest_data, field_mappings['net_income'])
        shares, share_priority = get_field_with_priority(latest_data, field_mappings['shares'])
        
        print(f"   净利润: {net_income} (优先级: {ni_priority})")
        print(f"   股本: {shares} (优先级: {share_priority})")
        print(f"   价格数据: {price_data}")
        print(f"   历史数据期间数: {len(pivot_data)}")
        
        # 检查基本条件
        if net_income > 0 and shares > 0 and price_data:
            print(f"   ✅ 基本条件满足")
            
            eps = net_income / shares
            pe_ratio = price_data['close_price'] / eps
            print(f"   EPS: {eps}")
            print(f"   PE比率: {pe_ratio}")
            
            # 检查历史数据条件
            if len(pivot_data) >= 3:
                print(f"   ✅ 历史数据足够 (>=3年)")
                
                # 检查收入数据
                revenues = []
                for i in range(min(5, len(pivot_data))):
                    data = pivot_data.iloc[-(i+1)]
                    revenue, _ = get_field_with_priority(data, field_mappings['revenue'])
                    if revenue > 0:
                        revenues.append({'year': len(pivot_data) - i, 'value': revenue})
                
                print(f"   收集到的收入数据: {len(revenues)} 个")
                for rev in revenues:
                    print(f"     年份{rev['year']}: {rev['value']}")
                
                if len(revenues) >= 2:
                    print(f"   ✅ 收入数据足够计算CAGR")
                    
                    revenues.reverse()
                    start_revenue = revenues[0]['value']
                    end_revenue = revenues[-1]['value']
                    years = len(revenues) - 1
                    
                    if start_revenue > 0:
                        revenue_cagr = (pow(end_revenue / start_revenue, 1 / years) - 1) * 100
                        print(f"   收入CAGR: {revenue_cagr}%")
                        
                        if revenue_cagr > 0:
                            peg_ratio = pe_ratio / revenue_cagr
                            print(f"   PEG比率: {peg_ratio}")
                            print(f"   ✅ 所有条件都满足，应该能计算valuation_bubble_signal")
                        else:
                            print(f"   ❌ 收入CAGR <= 0，无法计算PEG")
                    else:
                        print(f"   ❌ 起始收入 <= 0")
                else:
                    print(f"   ❌ 收入数据不足 (<2年)")
            else:
                print(f"   ❌ 历史数据不足 (<3年)")
        else:
            print(f"   ❌ 基本条件不满足")
            if net_income <= 0:
                print(f"     净利润 <= 0")
            if shares <= 0:
                print(f"     股本 <= 0")
            if not price_data:
                print(f"     缺少价格数据")
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            client.disconnect()
            print("🔌 数据库连接已关闭")

if __name__ == "__main__":
    debug_final_two_issues()
