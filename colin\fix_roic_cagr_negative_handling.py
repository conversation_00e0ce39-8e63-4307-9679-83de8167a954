from clickhouse_driver import Client
import pandas as pd
import numpy as np
from datetime import datetime

def connect_to_ap_research():
    """连接到ap_research数据库"""
    client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')
    print("✅ 连接ap_research数据库成功")
    return client

def calculate_robust_roic_cagr(stock_symbol, client):
    """计算健壮的ROIC CAGR，处理负净利润问题"""
    
    print(f"🔍 修复 {stock_symbol} 的ROIC CAGR:")
    
    # 获取年报数据
    query = f"""
    SELECT 
        stock_symbol,
        financial_period_absolute,
        statement_type,
        item_name,
        value,
        period_end_date
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = '{stock_symbol}'
      AND financial_period_absolute REGEXP '^FY[0-9]{{4}}$'
    ORDER BY financial_period_absolute ASC
    """
    
    result = client.execute(query)
    if not result:
        print(f"   ❌ {stock_symbol}: 无年报数据")
        return None
        
    # 转换为DataFrame
    df = pd.DataFrame(result, columns=[
        'stock_symbol', 'financial_period_absolute', 'statement_type', 'item_name', 'value', 'period_end_date'
    ])
    
    # 透视数据
    pivot_df = df.pivot_table(
        index=['stock_symbol', 'financial_period_absolute', 'period_end_date'],
        columns='item_name',
        values='value',
        aggfunc='first'
    ).reset_index()
    
    pivot_df['fiscal_year'] = pivot_df['financial_period_absolute'].str.extract(r'FY(\d{4})').astype(int)
    pivot_df = pivot_df.sort_values('fiscal_year')
    
    print(f"   📊 获取到 {len(pivot_df)} 年年报数据")
    
    # 计算每年的ROIC
    roic_data = []
    
    for idx, row in pivot_df.iterrows():
        year = row['fiscal_year']
        
        # 净利润
        net_income = row.get('Normalized Net Income - Bottom Line', 0) or 0
        
        # 债务
        total_debt = row.get('Debt - Total', 0) or 0
        
        # 股东权益（多字段fallback）
        total_equity = (row.get('Shareholders\' Equity - Attributable to Parent Shareholders - Total', 0) or 
                       row.get('Common Equity - Total', 0) or 
                       row.get('Common Equity Attributable to Parent Shareholders', 0) or 0)
        
        # 计算投入资本
        invested_capital = total_debt + total_equity
        
        # 计算ROIC
        if invested_capital > 0 and net_income > 0:  # 只考虑正净利润
            roic = (net_income / invested_capital) * 100
            roic_data.append({
                'year': year,
                'net_income': net_income,
                'invested_capital': invested_capital,
                'roic': roic
            })
            print(f"   FY{year}: ROIC = {roic:.2f}% (NI={net_income:,.0f}, IC={invested_capital:,.0f})")
        else:
            if net_income <= 0:
                print(f"   FY{year}: 跳过 (负净利润: {net_income:,.0f})")
            else:
                print(f"   FY{year}: 跳过 (投入资本: {invested_capital:,.0f})")
    
    if len(roic_data) < 2:
        print(f"   ❌ ROIC CAGR: 正ROIC数据不足 ({len(roic_data)}年)")
        return None
    
    # 寻找最长连续正ROIC区间
    print(f"   🔍 寻找最长连续正ROIC区间...")
    
    # 按年份排序
    roic_data.sort(key=lambda x: x['year'])
    
    # 寻找连续区间
    continuous_periods = []
    current_period = []
    
    for i, data in enumerate(roic_data):
        if i == 0 or data['year'] == roic_data[i-1]['year'] + 1:
            # 连续年份
            current_period.append(data)
        else:
            # 年份不连续
            if len(current_period) >= 2:
                continuous_periods.append(current_period)
            current_period = [data]
    
    # 添加最后一个区间
    if len(current_period) >= 2:
        continuous_periods.append(current_period)
    
    if not continuous_periods:
        print(f"   ❌ ROIC CAGR: 无连续正ROIC区间")
        return None
    
    # 选择最长区间
    longest_period = max(continuous_periods, key=len)
    
    print(f"   ✅ 最长连续正ROIC区间: {len(longest_period)}年")
    print(f"   📅 区间: FY{longest_period[0]['year']} - FY{longest_period[-1]['year']}")
    
    # 计算CAGR
    first_roic = longest_period[0]['roic']
    last_roic = longest_period[-1]['roic']
    years = len(longest_period) - 1
    
    if first_roic > 0 and last_roic > 0:
        roic_cagr = ((last_roic / first_roic) ** (1/years) - 1) * 100
        print(f"   🎯 ROIC CAGR: {roic_cagr:.2f}% ({years}年区间)")
        
        return {
            'stock_symbol': stock_symbol,
            'roic_cagr': roic_cagr,
            'years_used': years + 1,
            'first_roic': first_roic,
            'last_roic': last_roic,
            'period_start': longest_period[0]['year'],
            'period_end': longest_period[-1]['year']
        }
    else:
        print(f"   ❌ ROIC CAGR: ROIC值异常 (首={first_roic:.2f}%, 末={last_roic:.2f}%)")
        return None

def main():
    """修复ROIC CAGR算法"""
    
    print("=== 修复ROIC CAGR负净利润处理问题 ===")
    print()
    
    client = connect_to_ap_research()
    
    # 测试有负净利润问题的股票
    problem_stocks = ['AMZN', 'TSLA', 'AAPL', 'META']  # 包含已知问题股票和正常股票对比
    
    results = []
    
    for stock in problem_stocks:
        print(f"{'='*60}")
        result = calculate_robust_roic_cagr(stock, client)
        if result:
            results.append(result)
        print()
    
    print("📋 修复结果总结:")
    print("-" * 80)
    print(f"{'股票':<8} {'ROIC CAGR':<12} {'年限':<6} {'起始ROIC':<12} {'结束ROIC':<12} {'使用区间'}")
    print("-" * 80)
    
    for r in results:
        print(f"{r['stock_symbol']:<8} {r['roic_cagr']:>10.2f}% {r['years_used']:>4}年 "
              f"{r['first_roic']:>10.2f}% {r['last_roic']:>10.2f}% "
              f"FY{r['period_start']}-FY{r['period_end']}")
    
    print("-" * 80)
    print()
    print("🎯 修复要点:")
    print("   ✅ 只使用正净利润年份计算ROIC")
    print("   ✅ 寻找最长连续正ROIC区间")  
    print("   ✅ 避免因个别亏损年份导致整个CAGR失效")
    print("   ✅ 保持与FCF CAGR一致的处理逻辑")
    
    # 保存结果
    if results:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"colin/fixed_roic_cagr_{timestamp}.csv"
        
        df_results = pd.DataFrame(results)
        df_results.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"✅ 修复结果已保存到: {filename}")

if __name__ == "__main__":
    main()

