#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复后的因子标准化逻辑
核心思路：对每个历史期间重新计算因子，而不是直接查找
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from final_complete_24_factors_with_field_mapping import (
    connect_to_ap_research, 
    calculate_complete_24_factors
)
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def calculate_historical_factors_for_standardization(client, stock_symbol, calculation_date, lookback_years=5):
    """
    计算历史因子值用于标准化
    核心改进：对每个历史期间重新计算完整因子，而不是直接查找
    """
    print(f"🔄 开始计算 {stock_symbol} 的历史因子用于标准化...")
    
    # 计算历史日期范围
    end_date = pd.to_datetime(calculation_date)
    start_date = end_date - pd.DateOffset(years=lookback_years)
    
    # 生成历史计算日期（每年一次，避免计算量过大）
    historical_dates = []
    current_date = start_date
    while current_date < end_date:
        historical_dates.append(current_date.strftime('%Y-%m-%d'))
        current_date += pd.DateOffset(years=1)
    
    print(f"   历史计算日期: {len(historical_dates)} 个")
    
    # 对每个历史日期计算完整因子
    historical_factors = []
    
    for i, hist_date in enumerate(historical_dates):
        print(f"   计算历史期间 {i+1}/{len(historical_dates)}: {hist_date}")
        
        try:
            # 计算该历史日期的完整因子
            factors = calculate_complete_24_factors(client, stock_symbol, hist_date)
            
            if factors:
                # 添加计算日期
                factors['calculation_date'] = hist_date
                historical_factors.append(factors)
                print(f"     ✅ 成功")
            else:
                print(f"     ❌ 失败")
                
        except Exception as e:
            print(f"     ❌ 错误: {e}")
            continue
    
    if not historical_factors:
        print(f"   ❌ 无法获取 {stock_symbol} 的历史因子数据")
        return None
    
    print(f"   ✅ 成功获取 {len(historical_factors)} 个历史期间的因子数据")
    return historical_factors

def standardize_factors_fixed(factors_dict, client, stock_symbol, calculation_date):
    """
    修复后的因子标准化函数
    """
    print(f"🔄 开始标准化 {stock_symbol} 的24个因子（修复版）...")
    
    # 获取历史因子数据
    historical_factors = calculate_historical_factors_for_standardization(
        client, stock_symbol, calculation_date
    )
    
    if not historical_factors:
        print(f"   ❌ {stock_symbol}: 无法获取历史因子数据")
        return factors_dict
    
    # 定义因子分组和标准化方法 - 完整24因子版本
    factor_groups = {
        'min_max_factors': [
            'tech_premium', 'patent_density', 'ecosystem_cash_ratio',
            'financial_health', 'profit_revenue_ratio', 'operating_margin',
            'adjusted_roce', 'roic', 'dynamic_safety_margin'
        ],
        'zscore_factors': [
            'revenue_yoy', 'revenue_cagr', 'net_income_yoy', 'fcf_cagr', 'roic_cagr',
            'revenue_growth_continuity', 'operating_margin_std', 'effective_tax_rate_std',
            'roce_stability', 'fcf_per_share'
        ],
        'robust_scaling_factors': [
            'fcf_quality', 'cash_flow_coverage_ratio'
        ],
        'negative_factors': [
            'tech_gap_warning', 'valuation_bubble_signal', 'effective_tax_rate',
            'effective_tax_rate_improvement'
        ]
    }
    
    # 对每个因子进行标准化
    for group_name, factor_list in factor_groups.items():
        for factor in factor_list:
            if factor in factors_dict and factors_dict[factor] is not None:
                
                # 收集历史值
                historical_values = []
                for hist_factors in historical_factors:
                    if factor in hist_factors and hist_factors[factor] is not None:
                        historical_values.append(hist_factors[factor])
                
                if len(historical_values) >= 3:  # 至少需要3个历史值
                    current_value = factors_dict[factor]
                    
                    if group_name == 'min_max_factors':
                        # Min-Max标准化 [0,1]
                        hist_min = min(historical_values)
                        hist_max = max(historical_values)
                        
                        if hist_max > hist_min:
                            normalized = (current_value - hist_min) / (hist_max - hist_min)
                            factors_dict[f'{factor}_normalized'] = max(0, min(1, normalized))  # 限制在[0,1]
                            factors_dict[f'{factor}_standardization_method'] = 'Min-Max-Fixed'
                            factors_dict[f'{factor}_rolling_bounds'] = f"({hist_min:.2f}, {hist_max:.2f})"
                        else:
                            factors_dict[f'{factor}_normalized'] = 0.5
                            factors_dict[f'{factor}_standardization_method'] = 'Min-Max-ConstantHist'
                    
                    elif group_name == 'zscore_factors':
                        # Z-Score标准化
                        hist_mean = np.mean(historical_values)
                        hist_std = np.std(historical_values)
                        
                        if hist_std > 0:
                            normalized = (current_value - hist_mean) / hist_std
                            factors_dict[f'{factor}_normalized'] = normalized
                            factors_dict[f'{factor}_standardization_method'] = 'Z-Score-Fixed'
                            factors_dict[f'{factor}_rolling_stats'] = f"μ={hist_mean:.2f}, σ={hist_std:.2f}"
                        else:
                            factors_dict[f'{factor}_normalized'] = 0
                            factors_dict[f'{factor}_standardization_method'] = 'Z-Score-ZeroStd'
                    
                    elif group_name == 'robust_scaling_factors':
                        # Robust Scaling
                        hist_median = np.median(historical_values)
                        hist_q75 = np.percentile(historical_values, 75)
                        hist_q25 = np.percentile(historical_values, 25)
                        hist_iqr = hist_q75 - hist_q25
                        
                        if hist_iqr > 0:
                            normalized = (current_value - hist_median) / hist_iqr
                            factors_dict[f'{factor}_normalized'] = normalized
                            factors_dict[f'{factor}_standardization_method'] = 'Robust-Scaling-Fixed'
                            factors_dict[f'{factor}_rolling_stats'] = f"M={hist_median:.2f}, IQR={hist_iqr:.2f}"
                        else:
                            factors_dict[f'{factor}_normalized'] = 0
                            factors_dict[f'{factor}_standardization_method'] = 'Robust-Scaling-ZeroIQR'
                    
                    elif group_name == 'negative_factors':
                        # 负向因子：先翻转再Z-Score标准化
                        hist_mean = np.mean(historical_values)
                        hist_std = np.std(historical_values)
                        
                        if hist_std > 0:
                            # 翻转当前值和历史值
                            flipped_current = -current_value
                            flipped_mean = -hist_mean
                            
                            normalized = (flipped_current - flipped_mean) / hist_std
                            factors_dict[f'{factor}_normalized'] = normalized
                            factors_dict[f'{factor}_standardization_method'] = 'Z-Score-Negative-Fixed'
                            factors_dict[f'{factor}_rolling_stats'] = f"μ={hist_mean:.2f}, σ={hist_std:.2f}"
                        else:
                            factors_dict[f'{factor}_normalized'] = 0
                            factors_dict[f'{factor}_standardization_method'] = 'Z-Score-Negative-ZeroStd'
                
                else:
                    # 历史数据不足
                    factors_dict[f'{factor}_normalized'] = 0.5 if group_name == 'min_max_factors' else 0
                    factors_dict[f'{factor}_standardization_method'] = f'{group_name}-InsufficientData'
    
    print(f"   ✅ {stock_symbol}: 24因子标准化完成（修复版）")
    return factors_dict
