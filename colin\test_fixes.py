#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from final_complete_24_factors_with_field_mapping import (
    connect_to_ap_research, 
    calculate_complete_24_factors
)
import pandas as pd

def test_fixes():
    """测试修复效果"""
    print("🔍 测试修复效果...")
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        print("❌ 无法连接到数据库")
        return
    
    try:
        test_stock = 'AAPL'
        calculation_date = '2024-01-15'
        
        print(f"📊 测试股票: {test_stock}")
        print(f"📅 计算日期: {calculation_date}")
        
        # 计算因子
        factors = calculate_complete_24_factors(client, test_stock, calculation_date)
        
        if not factors:
            print("❌ 因子计算失败")
            return
        
        # 检查关键问题因子
        problem_factors = [
            'patent_density',
            'valuation_bubble_signal', 
            'effective_tax_rate',
            'roic'
        ]
        
        print(f"\n📊 关键问题因子检查:")
        print("-" * 60)
        
        for factor in problem_factors:
            if factor in factors:
                value = factors[factor]
                flag_key = f"{factor}_field_flag"
                flag = factors.get(flag_key, "无标志")
                
                print(f"{factor}:")
                print(f"   值: {value}")
                print(f"   标志: {flag}")
                
                # 分析状态
                if value is None:
                    print(f"   ❌ 仍然是None")
                elif pd.isna(value):
                    print(f"   ❌ 仍然是NaN")
                elif value == 0:
                    print(f"   ⚠️ 是零值")
                else:
                    print(f"   ✅ 有正常值")
                print()
        
        # 统计整体改善情况
        total_factors = 0
        none_count = 0
        nan_count = 0
        zero_count = 0
        valid_count = 0
        
        for key, value in factors.items():
            if not key.endswith('_field_flag') and not key.endswith('_calculation_details'):
                if key not in ['stock_symbol', 'calculation_date', 'fiscal_year', 'period_end_date', 'effective_date']:
                    total_factors += 1
                    
                    if value is None:
                        none_count += 1
                    elif pd.isna(value):
                        nan_count += 1
                    elif value == 0 or value == 0.0:
                        zero_count += 1
                    else:
                        valid_count += 1
        
        print(f"📊 修复后整体统计:")
        print(f"   总因子数: {total_factors}")
        print(f"   有效值: {valid_count} ({valid_count/total_factors*100:.1f}%)")
        print(f"   None值: {none_count} ({none_count/total_factors*100:.1f}%)")
        print(f"   NaN值: {nan_count} ({nan_count/total_factors*100:.1f}%)")
        print(f"   零值: {zero_count} ({zero_count/total_factors*100:.1f}%)")
        
        if none_count + nan_count + zero_count == 0:
            print(f"\n🎉 完美！没有任何None、NaN或零值问题！")
        else:
            print(f"\n⚠️ 还有 {none_count + nan_count + zero_count} 个问题需要进一步修复")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            client.disconnect()
            print("🔌 数据库连接已关闭")

if __name__ == "__main__":
    test_fixes()
