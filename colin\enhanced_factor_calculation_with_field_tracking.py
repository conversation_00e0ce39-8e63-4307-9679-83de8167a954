import pandas as pd
import numpy as np
from datetime import datetime
from clickhouse_driver import Client

def connect_to_ap_research():
    """连接到ap_research数据库"""
    try:
        client = Client(
            host='************',
            port=9000,
            user='default',
            password='5ur2pK8WZQdy2',
            database='ap_research'
        )
        print("✅ 连接ap_research数据库成功")
        return client
    except Exception as e:
        print(f"❌ 连接数据库失败: {e}")
        return None

def get_field_with_priority(data, field_config, stock_symbol, factor_name):
    """
    根据字段优先级获取数据，并记录使用的字段
    
    Args:
        data: 数据字典
        field_config: 字段配置，格式为 {'field_name': [field1, field2, field3], 'priority': 1}
        stock_symbol: 股票代码
        factor_name: 因子名称
    
    Returns:
        tuple: (value, used_field_info)
    """
    field_name = field_config['field_name']
    field_list = field_config['fields']
    priority = field_config.get('priority', 1)
    
    for i, field in enumerate(field_list):
        value = data.get(field, None)
        if value is not None and value != 0:
            # 记录使用的字段信息
            field_info = {
                'field_name': field_name,
                'used_field': field,
                'priority_rank': i + 1,
                'is_primary': i == 0,
                'factor_name': factor_name,
                'stock_symbol': stock_symbol
            }
            return value, field_info
    
    # 如果所有字段都没有数据
    field_info = {
        'field_name': field_name,
        'used_field': None,
        'priority_rank': None,
        'is_primary': False,
        'factor_name': factor_name,
        'stock_symbol': stock_symbol
    }
    return 0, field_info

# 定义字段优先级配置
FIELD_CONFIGS = {
    'shareholders_equity': {
        'field_name': 'Shareholders_Equity',
        'fields': [
            "Shareholders' Equity - Attributable to Parent Shareholders - Total",
            "Common Equity - Total", 
            "Common Equity Attributable to Parent Shareholders",
            "Total Shareholders' Equity - including Minority Interest & Hybrid Debt"
        ],
        'priority': 1
    },
    'net_income': {
        'field_name': 'Net_Income',
        'fields': [
            "Normalized Net Income - Bottom Line",
            "Net Income",
            "Net Income Available to Common Shareholders"
        ],
        'priority': 1
    },
    'total_debt': {
        'field_name': 'Total_Debt',
        'fields': [
            "Debt - Total",
            "Total Debt",
            "Long-term Debt - Total"
        ],
        'priority': 2
    },
    'free_cash_flow': {
        'field_name': 'Free_Cash_Flow',
        'fields': [
            "Free Cash Flow",
            "Operating Cash Flow",
            "Cash Flow from Operations"
        ],
        'priority': 1
    },
    'revenue': {
        'field_name': 'Revenue',
        'fields': [
            "Revenue from Business Activities - Total",
            "Total Revenue",
            "Net Sales"
        ],
        'priority': 1
    },
    'operating_income': {
        'field_name': 'Operating_Income',
        'fields': [
            "Operating Income",
            "Income from Operations",
            "Operating Profit"
        ],
        'priority': 1
    },
    'diluted_shares': {
        'field_name': 'Diluted_Shares',
        'fields': [
            "Shares used to calculate Diluted EPS - Total",
            "Shares Outstanding - Diluted Average",
            "Weighted Average Shares Outstanding - Diluted"
        ],
        'priority': 1
    },
    'current_assets': {
        'field_name': 'Current_Assets',
        'fields': [
            "Total Current Assets",
            "Current Assets - Total",
            "Current Assets"
        ],
        'priority': 1
    },
    'current_liabilities': {
        'field_name': 'Current_Liabilities',
        'fields': [
            "Total Current Liabilities",
            "Current Liabilities - Total",
            "Current Liabilities"
        ],
        'priority': 1
    },
    'diluted_eps': {
        'field_name': 'Diluted_EPS',
        'fields': [
            "EPS - Diluted - including Extraordinary Items Applicable to Common - Total",
            "Earnings Per Share - Diluted",
            "Diluted EPS"
        ],
        'priority': 1
    },
    'tax_expense': {
        'field_name': 'Tax_Expense',
        'fields': [
            "Income Taxes",
            "Tax Expense",
            "Provision for Income Taxes"
        ],
        'priority': 2
    },
    'ebit': {
        'field_name': 'EBIT',
        'fields': [
            "Earnings before Interest & Taxes (EBIT)",
            "Operating Income",
            "EBIT"
        ],
        'priority': 2
    }
}

def calculate_factor_with_field_tracking(stock_symbol, stock_data, listing_years):
    """
    计算因子并跟踪字段使用情况
    """
    factors = {'stock_symbol': stock_symbol}
    field_usage = []  # 记录字段使用情况
    
    if len(stock_data) == 0:
        return factors, field_usage
    
    latest_data = stock_data.iloc[-1]
    
    # 1. 调整后ROCE (adjusted_roce)
    try:
        net_income, ni_field_info = get_field_with_priority(
            latest_data, FIELD_CONFIGS['net_income'], stock_symbol, 'adjusted_roce'
        )
        equity, eq_field_info = get_field_with_priority(
            latest_data, FIELD_CONFIGS['shareholders_equity'], stock_symbol, 'adjusted_roce'
        )
        
        if equity > 0:
            factors['adjusted_roce'] = (net_income / equity) * 100
            field_usage.extend([ni_field_info, eq_field_info])
        else:
            factors['adjusted_roce'] = None
            field_usage.extend([ni_field_info, eq_field_info])
    except:
        factors['adjusted_roce'] = None
    
    # 2. ROIC (roic) 
    try:
        net_income, ni_field_info = get_field_with_priority(
            latest_data, FIELD_CONFIGS['net_income'], stock_symbol, 'roic'
        )
        debt, debt_field_info = get_field_with_priority(
            latest_data, FIELD_CONFIGS['total_debt'], stock_symbol, 'roic'
        )
        equity, eq_field_info = get_field_with_priority(
            latest_data, FIELD_CONFIGS['shareholders_equity'], stock_symbol, 'roic'
        )
        
        invested_capital = debt + equity
        if invested_capital > 0:
            factors['roic'] = (net_income / invested_capital) * 100
            field_usage.extend([ni_field_info, debt_field_info, eq_field_info])
        else:
            factors['roic'] = None
            field_usage.extend([ni_field_info, debt_field_info, eq_field_info])
    except:
        factors['roic'] = None
    
    # 3. FCF CAGR (fcf_cagr)
    try:
        if listing_years >= 3:
            years_to_use = min(8, listing_years)
            
            # 当前FCF
            current_fcf, current_fcf_info = get_field_with_priority(
                latest_data, FIELD_CONFIGS['free_cash_flow'], stock_symbol, 'fcf_cagr'
            )
            
            # 过去FCF
            past_data = stock_data.iloc[-years_to_use]
            past_fcf, past_fcf_info = get_field_with_priority(
                past_data, FIELD_CONFIGS['free_cash_flow'], stock_symbol, 'fcf_cagr'
            )
            
            if past_fcf > 0 and current_fcf > 0:
                cagr = ((current_fcf / past_fcf) ** (1/(years_to_use-1)) - 1) * 100
                factors['fcf_cagr'] = cagr
                field_usage.extend([current_fcf_info, past_fcf_info])
            else:
                factors['fcf_cagr'] = None
                field_usage.extend([current_fcf_info, past_fcf_info])
        else:
            factors['fcf_cagr'] = None
    except:
        factors['fcf_cagr'] = None
    
    # 4. ROIC CAGR (roic_cagr)
    try:
        if listing_years >= 3:
            years_to_use = min(8, listing_years)
            current_roic = factors.get('roic', 0) or 0
            
            # 计算过去的ROIC
            past_data = stock_data.iloc[-years_to_use]
            past_ni, past_ni_info = get_field_with_priority(
                past_data, FIELD_CONFIGS['net_income'], stock_symbol, 'roic_cagr'
            )
            past_debt, past_debt_info = get_field_with_priority(
                past_data, FIELD_CONFIGS['total_debt'], stock_symbol, 'roic_cagr'
            )
            past_equity, past_eq_info = get_field_with_priority(
                past_data, FIELD_CONFIGS['shareholders_equity'], stock_symbol, 'roic_cagr'
            )
            
            past_invested_capital = past_debt + past_equity
            if past_invested_capital > 0:
                past_roic = (past_ni / past_invested_capital) * 100
                
                if past_roic > 0 and current_roic > 0:
                    cagr = ((current_roic / past_roic) ** (1/(years_to_use-1)) - 1) * 100
                    factors['roic_cagr'] = cagr
                    field_usage.extend([past_ni_info, past_debt_info, past_eq_info])
                else:
                    factors['roic_cagr'] = None
                    field_usage.extend([past_ni_info, past_debt_info, past_eq_info])
            else:
                factors['roic_cagr'] = None
                field_usage.extend([past_ni_info, past_debt_info, past_eq_info])
        else:
            factors['roic_cagr'] = None
    except:
        factors['roic_cagr'] = None
    
    return factors, field_usage

def main():
    """主函数"""
    print("=== 增强版因子计算（带字段跟踪）===")
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        return
    
    # 测试Mega7股票
    mega7_stocks = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META']
    
    print(f"\n=== 测试Mega7股票的字段使用情况 ===")
    
    # 获取基本面数据
    print("\n1. 获取基本面数据...")
    query = """
    SELECT 
        stock_symbol,
        financial_period_absolute,
        item_name,
        value,
        statement_type
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol IN ('AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META')
      AND substring(financial_period_absolute, 1, 2) = 'FY'
    ORDER BY stock_symbol, financial_period_absolute
    """
    
    try:
        result = client.execute(query)
        df = pd.DataFrame(result, columns=['stock_symbol', 'period', 'item_name', 'value', 'statement_type'])
        print(f"   📊 获取到 {len(df)} 条基本面记录")
    except Exception as e:
        print(f"❌ 获取基本面数据失败: {e}")
        return
    
    # 转换为透视表
    pivot_df = df.pivot_table(
        index=['stock_symbol', 'period'], 
        columns='item_name', 
        values='value', 
        aggfunc='first'
    ).reset_index()
    
    all_factors = []
    all_field_usage = []
    
    # 计算每只股票的因子
    for stock in mega7_stocks:
        print(f"\n🔍 分析 {stock}:")
        
        stock_data = pivot_df[pivot_df['stock_symbol'] == stock].copy()
        if len(stock_data) == 0:
            print(f"   ❌ 未找到 {stock} 的数据")
            continue
        
        # 按期间排序
        stock_data = stock_data.sort_values('period')
        listing_years = len(stock_data)
        
        # 计算因子
        factors, field_usage = calculate_factor_with_field_tracking(stock, stock_data, listing_years)
        
        all_factors.append(factors)
        all_field_usage.extend(field_usage)
        
        # 显示结果
        print(f"   📊 {listing_years}年数据")
        for factor_name, value in factors.items():
            if factor_name != 'stock_symbol':
                if value is not None:
                    print(f"   ✅ {factor_name}: {value:.4f}")
                else:
                    print(f"   ❌ {factor_name}: N/A")
    
    # 创建因子结果DataFrame
    factors_df = pd.DataFrame(all_factors)
    
    # 创建字段使用情况DataFrame
    field_usage_df = pd.DataFrame(all_field_usage)
    
    # 保存结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 保存因子数据
    factors_file = f"colin/mega7_factors_with_tracking_{timestamp}.csv"
    factors_df.to_csv(factors_file, index=False, encoding='utf-8-sig')
    print(f"\n✅ 因子数据已保存到: {factors_file}")
    
    # 保存字段使用情况
    if len(field_usage_df) > 0:
        field_usage_file = f"colin/field_usage_tracking_{timestamp}.csv"
        field_usage_df.to_csv(field_usage_file, index=False, encoding='utf-8-sig')
        print(f"✅ 字段使用情况已保存到: {field_usage_file}")
        
        # 分析字段使用统计
        print(f"\n=== 字段使用统计 ===")
        
        # 按股票统计非主字段使用情况
        non_primary_usage = field_usage_df[field_usage_df['is_primary'] == False]
        if len(non_primary_usage) > 0:
            print(f"\n⚠️  使用备用字段的情况:")
            for _, row in non_primary_usage.iterrows():
                if row['used_field'] is not None:
                    print(f"   {row['stock_symbol']} - {row['factor_name']}: 使用 '{row['used_field']}' (优先级 {row['priority_rank']})")
                else:
                    print(f"   {row['stock_symbol']} - {row['factor_name']}: 所有字段都无数据")
        
        # 按字段类型统计
        field_type_stats = field_usage_df.groupby(['field_name', 'is_primary']).size().reset_index(name='count')
        print(f"\n📊 字段类型使用统计:")
        for field_name in field_type_stats['field_name'].unique():
            field_stats = field_type_stats[field_type_stats['field_name'] == field_name]
            primary_count = field_stats[field_stats['is_primary'] == True]['count'].sum()
            backup_count = field_stats[field_stats['is_primary'] == False]['count'].sum()
            print(f"   {field_name}: 主字段 {primary_count}次, 备用字段 {backup_count}次")
    
    client.disconnect()
    print(f"\n🎯 分析完成！现在可以看到每个因子具体使用了哪些字段！")

if __name__ == "__main__":
    main()

