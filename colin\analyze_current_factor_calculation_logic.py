import re

def analyze_factor_calculation_logic():
    """分析当前因子计算的时间窗口和滚动逻辑"""
    
    print("=== 分析当前因子计算逻辑 ===")
    print()
    
    # 读取因子计算文件
    try:
        with open('colin/calculate_26_factors_from_database.py', 'r', encoding='utf-8') as f:
            content = f.read()
    except:
        print("❌ 无法读取因子计算文件")
        return
    
    # 分析每个因子的计算逻辑
    factors_analysis = {}
    
    # 定义因子列表和其计算特征
    factors_info = [
        ('tech_premium', '技术溢价', 'latest_data'),
        ('tech_gap_warning', '技术差距警告', 'prev_data'),
        ('patent_density', '专利密度', 'latest_data'),
        ('ecosystem_cash_ratio', '生态现金比率', 'latest_data'),
        ('adjusted_roce', '调整后ROCE', 'latest_data'),
        ('fcf_quality', '自由现金流质量', 'latest_data'),
        ('dynamic_safety_margin', '动态安全边际', 'latest_data'),
        ('revenue_growth_continuity', '营收增长连续性', 'rolling'),
        ('effective_tax_rate_improvement', '有效税率改善', 'latest_data'),
        ('financial_health', '财务健康度', 'latest_data'),
        ('valuation_bubble_signal', '估值泡沫信号', 'latest_data'),
        ('roce_stability', 'ROCE稳定性', 'rolling'),
        ('revenue_yoy', '营收同比增长', 'prev_data'),
        ('revenue_cagr', '营收CAGR', 'cagr'),
        ('net_income_yoy', '净利润同比增长', 'prev_data'),
        ('profit_revenue_ratio', '利润营收比', 'latest_data'),
        ('fcf_per_share', '每股自由现金流', 'latest_data'),
        ('fcf_cagr', '自由现金流CAGR', 'cagr'),
        ('operating_margin', '营业利润率', 'latest_data'),
        ('operating_margin_std', '营业利润率标准差', 'rolling'),
        ('roic', 'ROIC', 'latest_data'),
        ('roic_cagr', 'ROIC CAGR', 'cagr'),
        ('effective_tax_rate', '有效税率', 'latest_data'),
        ('effective_tax_rate_std', '有效税率标准差', 'rolling'),
    ]
    
    print("📊 因子计算逻辑分析:")
    print("-" * 100)
    print(f"{'因子名称':<25} {'中文名':<15} {'计算方式':<12} {'时间窗口':<15} {'数据要求':<10} {'负值处理'}")
    print("-" * 100)
    
    for factor_en, factor_cn, expected_type in factors_info:
        # 查找因子计算代码段
        pattern = rf"# \d+\. {factor_cn}.*?\n(.*?)except:"
        matches = re.findall(pattern, content, re.DOTALL)
        
        if matches:
            factor_code = matches[0]
            
            # 分析计算方式
            calc_type = "未知"
            time_window = "未知"
            data_req = "未知"
            negative_handling = "无"
            
            # 1. 检查是否使用latest_data
            if 'latest_data' in factor_code:
                calc_type = "最新数据"
                time_window = "当前年"
                data_req = "1年"
            
            # 2. 检查是否使用prev_data
            if 'prev_data' in factor_code or 'stock_data.iloc[-2]' in factor_code:
                calc_type = "同比计算"
                time_window = "当前vs前1年"
                data_req = "≥2年"
            
            # 3. 检查是否使用CAGR计算
            if 'years_to_use' in factor_code and 'cagr' in factor_code.lower():
                calc_type = "CAGR计算"
                # 查找years_to_use的设置
                years_match = re.search(r'years_to_use = min\((\d+), listing_years\)', factor_code)
                if years_match:
                    max_years = years_match.group(1)
                    time_window = f"最多{max_years}年"
                else:
                    time_window = "动态年限"
                
                # 查找最低要求
                req_match = re.search(r'if listing_years >= (\d+):', factor_code)
                if req_match:
                    min_req = req_match.group(1)
                    data_req = f"≥{min_req}年"
            
            # 4. 检查是否使用滚动计算
            if 'for i in range' in factor_code and 'stock_data.iloc[-i' in factor_code:
                calc_type = "滚动计算"
                # 查找滚动窗口
                range_match = re.search(r'for i in range\(.*?min\((\d+), listing_years\)', factor_code)
                if range_match:
                    window_size = range_match.group(1)
                    time_window = f"滚动{window_size}年"
                else:
                    time_window = "动态滚动"
                
                # 查找最低要求
                req_match = re.search(r'if listing_years >= (\d+):', factor_code)
                if req_match:
                    min_req = req_match.group(1)
                    data_req = f"≥{min_req}年"
            
            # 5. 检查负值处理
            if '> 0' in factor_code:
                negative_handling = "过滤负值"
            elif '!= 0' in factor_code:
                negative_handling = "避免除零"
            elif 'abs(' in factor_code:
                negative_handling = "绝对值"
            
            print(f"{factor_en:<25} {factor_cn:<15} {calc_type:<12} {time_window:<15} {data_req:<10} {negative_handling}")
        else:
            print(f"{factor_en:<25} {factor_cn:<15} {'未找到':<12} {'未知':<15} {'未知':<10} {'未知'}")
    
    print("-" * 100)
    print()
    
    # 分析具体的问题
    print("🔍 详细问题分析:")
    print()
    
    # 1. CAGR因子的计算逻辑
    print("1. CAGR因子计算逻辑:")
    cagr_factors = ['revenue_cagr', 'fcf_cagr', 'roic_cagr']
    
    for factor in cagr_factors:
        print(f"   📊 {factor}:")
        
        # 查找具体的CAGR计算代码
        pattern = rf"# \d+\..*?{factor}.*?\n(.*?)except:"
        matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
        
        if matches:
            code = matches[0]
            
            # 分析时间窗口设置
            years_match = re.search(r'years_to_use = min\((\d+), listing_years\)', code)
            min_req_match = re.search(r'if listing_years >= (\d+):', code)
            
            if years_match and min_req_match:
                max_years = years_match.group(1)
                min_years = min_req_match.group(1)
                print(f"     ⏱️  时间窗口: 最多{max_years}年，最少{min_years}年")
            
            # 检查负值处理
            if 'past_' in code and '> 0' in code and 'current_' in code and '> 0' in code:
                print(f"     ✅ 负值处理: 要求起始值和结束值都>0")
            else:
                print(f"     ❌ 负值处理: 可能存在问题")
            
            # 检查计算公式
            if '** (1/(years_to_use-1))' in code:
                print(f"     ✅ CAGR公式: 标准复合增长率公式")
            else:
                print(f"     ❌ CAGR公式: 可能有问题")
            
            # 检查是否是简单的首尾比较
            if 'stock_data.iloc[-years_to_use]' in code:
                print(f"     ⚠️  计算方式: 简单首尾比较（不是滚动平均）")
            
        print()
    
    # 2. 滚动计算因子
    print("2. 滚动计算因子:")
    rolling_factors = ['revenue_growth_continuity', 'roce_stability', 'operating_margin_std', 'effective_tax_rate_std']
    
    for factor in rolling_factors:
        print(f"   📊 {factor}:")
        
        pattern = rf"# \d+\..*?{factor}.*?\n(.*?)except:"
        matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
        
        if matches:
            code = matches[0]
            
            # 检查滚动窗口
            if 'for i in range' in code:
                range_match = re.search(r'for i in range\((.*?)\)', code)
                if range_match:
                    range_expr = range_match.group(1)
                    print(f"     ⏱️  滚动窗口: {range_expr}")
                
                # 检查是否真正滚动
                if 'stock_data.iloc[-i-1]' in code or 'stock_data.iloc[-i]' in code:
                    print(f"     ✅ 滚动方式: 逐年向前滚动")
                else:
                    print(f"     ❌ 滚动方式: 可能有问题")
            else:
                print(f"     ❌ 滚动逻辑: 未发现滚动计算")
        
        print()
    
    # 3. 总结当前问题
    print("🎯 当前计算逻辑总结:")
    print("   ✅ 优点:")
    print("     • 大部分因子使用最新年报数据")
    print("     • CAGR因子有动态时间窗口")
    print("     • 滚动因子有历史数据平滑")
    print()
    print("   ❌ 问题:")
    print("     • CAGR计算是简单首尾比较，不是真正的滚动")
    print("     • 负值处理不够健壮")
    print("     • 缺乏异常值过滤")
    print("     • 时间窗口可能过于固定")
    print()
    print("   🔧 建议改进:")
    print("     • CAGR应该寻找最长连续正值区间")
    print("     • 增加异常值检测和过滤")
    print("     • 标准差计算应该剔除极端值")
    print("     • 税率计算需要合理性检查")

if __name__ == "__main__":
    analyze_factor_calculation_logic()

