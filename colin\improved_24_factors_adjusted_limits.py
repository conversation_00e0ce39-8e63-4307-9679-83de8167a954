from clickhouse_driver import Client
import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def connect_to_ap_research():
    """连接到ap_research数据库"""
    client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')
    print("✅ 连接ap_research数据库成功")
    return client

def get_stock_data(client, stock_symbol):
    """获取股票的基本面数据"""
    query = f"""
    SELECT 
        stock_symbol,
        financial_period_absolute,
        period_end_date,
        item_name,
        value,
        statement_type
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = '{stock_symbol}'
      AND statement_type IN ('income_statement', 'balance_sheet_history', 'cash_flow')
      AND financial_period_absolute REGEXP '^FY[0-9]{{4}}$'
    ORDER BY financial_period_absolute ASC
    """
    
    result = client.execute(query)
    df = pd.DataFrame(result, columns=[
        'stock_symbol', 'financial_period_absolute', 'period_end_date', 
        'item_name', 'value', 'statement_type'
    ])
    
    return df

def get_price_data(client, stock_symbol):
    """获取股票的价格数据"""
    query = f"""
    SELECT 
        stock_symbol,
        trade_date,
        close,
        volume
    FROM priority_quality_stock_hfq
    WHERE stock_symbol = '{stock_symbol}'
    ORDER BY trade_date DESC
    LIMIT 1
    """
    
    result = client.execute(query)
    if result:
        return {
            'close_price': result[0][2],
            'volume': result[0][3]
        }
    return None

def find_longest_positive_range(values_list):
    """找到最长的连续正值范围用于CAGR计算"""
    if not values_list:
        return None, None, "no_data"
    
    # 找到所有正值的连续区间
    positive_ranges = []
    current_range = []
    
    for i, item in enumerate(values_list):
        if item['value'] > 0:
            current_range.append(item)
        else:
            if len(current_range) >= 2:  # 至少需要2个点计算CAGR
                positive_ranges.append(current_range)
            current_range = []
    
    # 添加最后一个区间
    if len(current_range) >= 2:
        positive_ranges.append(current_range)
    
    if not positive_ranges:
        # 如果没有连续正值区间，尝试找首尾正值
        positive_values = [item for item in values_list if item['value'] > 0]
        if len(positive_values) >= 2:
            first_positive = positive_values[0]
            last_positive = positive_values[-1]
            return first_positive, last_positive, "turnaround_growth"
        return None, None, "insufficient_positive_data"
    
    # 选择最长的区间
    longest_range = max(positive_ranges, key=len)
    return longest_range[0], longest_range[-1], "continuous_positive"

def calculate_improved_factors(client, stock_symbol):
    """计算调整截断限制后的24个改进因子"""
    
    print(f"\n🔄 开始计算 {stock_symbol} 的改进因子...")
    
    # 获取数据
    stock_data = get_stock_data(client, stock_symbol)
    price_data = get_price_data(client, stock_symbol)
    
    if stock_data.empty:
        print(f"   ❌ {stock_symbol}: 无基本面数据")
        return None
    
    # 透视数据
    pivot_data = stock_data.pivot_table(
        index=['financial_period_absolute', 'period_end_date'],
        columns='item_name',
        values='value',
        aggfunc='first'
    ).reset_index()
    
    if pivot_data.empty:
        print(f"   ❌ {stock_symbol}: 数据透视失败")
        return None
    
    # 获取最新数据
    latest_data = pivot_data.iloc[-1]
    fiscal_year = int(latest_data['financial_period_absolute'].replace('FY', ''))
    
    factors = {
        'stock_symbol': stock_symbol,
        'fiscal_year': fiscal_year,
        'period_end_date': latest_data['period_end_date']
    }
    
    # 多字段优先级函数
    def get_field_with_priority(data, field_priority_list):
        """按优先级获取字段值"""
        for field in field_priority_list:
            value = data.get(field, 0) or 0
            if value != 0:
                return value, field
        return 0, None
    
    # 1. tech_premium (R&D强度) - 调整上限到80%
    try:
        rd_expense = latest_data.get('Research & Development Expense', 0) or 0
        rd_supplemental = latest_data.get('Research & Development Expense - Supplemental', 0) or 0
        total_rd = rd_expense + rd_supplemental
        revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
        
        if revenue > 0:
            tech_premium = (total_rd / revenue) * 100
            factors['tech_premium'] = min(tech_premium, 80)  # 调整上限到80%
        else:
            factors['tech_premium'] = None
    except:
        factors['tech_premium'] = None
    
    # 2. tech_gap_warning (R&D强度变化率)
    try:
        if len(pivot_data) >= 2:
            prev_data = pivot_data.iloc[-2]
            prev_rd_expense = prev_data.get('Research & Development Expense', 0) or 0
            prev_rd_supplemental = prev_data.get('Research & Development Expense - Supplemental', 0) or 0
            prev_total_rd = prev_rd_expense + prev_rd_supplemental
            prev_revenue = prev_data.get('Revenue from Business Activities - Total', 0) or 0
            
            current_rd_expense = latest_data.get('Research & Development Expense', 0) or 0
            current_rd_supplemental = latest_data.get('Research & Development Expense - Supplemental', 0) or 0
            current_total_rd = current_rd_expense + current_rd_supplemental
            current_revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
            
            if prev_revenue > 0 and current_revenue > 0:
                prev_tech_premium = (prev_total_rd / prev_revenue) * 100
                current_tech_premium = (current_total_rd / current_revenue) * 100
                
                if prev_tech_premium > 0:
                    change_rate = (current_tech_premium - prev_tech_premium) / prev_tech_premium * 100
                    factors['tech_gap_warning'] = np.clip(change_rate, -100, 200)
                else:
                    factors['tech_gap_warning'] = None
            else:
                factors['tech_gap_warning'] = None
        else:
            factors['tech_gap_warning'] = None
    except:
        factors['tech_gap_warning'] = None
    
    # 3. patent_density (专利密度 - 使用无形资产/收入作为代理)
    try:
        intangible_assets = latest_data.get('Intangible Assets - Total', 0) or 0
        revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
        
        if revenue > 0:
            patent_density = (intangible_assets / revenue) * 100
            factors['patent_density'] = min(patent_density, 100)
        else:
            factors['patent_density'] = None
    except:
        factors['patent_density'] = None
    
    # 4. ecosystem_cash_ratio (现金生态比率)
    try:
        # 尝试多个现金字段
        cash_equivalents = (latest_data.get('Cash & Cash Equivalents', 0) or
                          latest_data.get('Cash & Cash Equivalents - Total', 0) or
                          latest_data.get('Cash & Short Term Investments - Total', 0) or 0)
        
        # 尝试多个总资产字段
        total_assets = (latest_data.get('Assets - Total', 0) or
                       latest_data.get('Total Assets', 0) or
                       latest_data.get('Total Current Assets', 0) + latest_data.get('Total Non-Current Assets', 0) or 0)
        
        if total_assets > 0:
            cash_ratio = (cash_equivalents / total_assets) * 100
            factors['ecosystem_cash_ratio'] = min(cash_ratio, 100)
        else:
            factors['ecosystem_cash_ratio'] = None
    except:
        factors['ecosystem_cash_ratio'] = None
    
    # 5. adjusted_roe (调整后ROE) - 调整限制到-80%到100%
    try:
        net_income = latest_data.get('Normalized Net Income - Bottom Line', 0) or 0
        equity_priority = [
            'Shareholders\' Equity - Attributable to Parent Shareholders - Total',
            'Common Equity - Total',
            'Common Equity Attributable to Parent Shareholders'
        ]
        total_equity, equity_field = get_field_with_priority(latest_data, equity_priority)
        
        if total_equity > 0:
            roe = (net_income / total_equity) * 100
            factors['adjusted_roe'] = np.clip(roe, -80, 100)  # 调整限制
            factors['adjusted_roe_field'] = equity_field or 'PRIMARY'
        else:
            factors['adjusted_roe'] = None
            factors['adjusted_roe_field'] = 'NA'
    except:
        factors['adjusted_roe'] = None
        factors['adjusted_roe_field'] = 'NA'
    
    # 6. fcf_quality (FCF质量)
    try:
        fcf = latest_data.get('Free Cash Flow', 0) or 0
        net_income = latest_data.get('Normalized Net Income - Bottom Line', 0) or 0
        
        if net_income != 0:
            fcf_quality = fcf / net_income
            factors['fcf_quality'] = np.clip(fcf_quality, -5, 5)
        else:
            factors['fcf_quality'] = None
    except:
        factors['fcf_quality'] = None
    
    # 7. dynamic_safety_margin (动态安全边际)
    try:
        if factors.get('tech_premium') is not None and factors.get('ecosystem_cash_ratio') is not None:
            factors['dynamic_safety_margin'] = factors['tech_premium'] + factors['ecosystem_cash_ratio']
        else:
            factors['dynamic_safety_margin'] = None
    except:
        factors['dynamic_safety_margin'] = None
    
    # 8. revenue_growth_continuity (收入增长连续性)
    try:
        if len(pivot_data) >= 3:
            recent_years = min(3, len(pivot_data))
            revenues = []
            for i in range(recent_years):
                data = pivot_data.iloc[-(i+1)]
                revenue = data.get('Revenue from Business Activities - Total', 0) or 0
                revenues.append(revenue)
            
            revenues.reverse()  # 按时间顺序
            
            if all(r > 0 for r in revenues):
                growth_rates = []
                for i in range(1, len(revenues)):
                    growth_rate = (revenues[i] - revenues[i-1]) / revenues[i-1] * 100
                    growth_rates.append(growth_rate)
                
                if growth_rates:
                    factors['revenue_growth_continuity'] = np.std(growth_rates)
                else:
                    factors['revenue_growth_continuity'] = None
            else:
                factors['revenue_growth_continuity'] = None
        else:
            factors['revenue_growth_continuity'] = None
    except:
        factors['revenue_growth_continuity'] = None
    
    # 9. effective_tax_rate_improvement (有效税率改善)
    try:
        if len(pivot_data) >= 2:
            current_ebit = latest_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
            current_tax = latest_data.get('Income Taxes', 0) or 0
            
            prev_data = pivot_data.iloc[-2]
            prev_ebit = prev_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
            prev_tax = prev_data.get('Income Taxes', 0) or 0
            
            if current_ebit > 0 and prev_ebit > 0:
                current_tax_rate = (current_tax / current_ebit) * 100
                prev_tax_rate = (prev_tax / prev_ebit) * 100
                
                # 合理税率范围检查
                if -50 <= current_tax_rate <= 100 and -50 <= prev_tax_rate <= 100:
                    improvement = current_tax_rate - prev_tax_rate
                    factors['effective_tax_rate_improvement'] = np.clip(improvement, -50, 50)
                else:
                    factors['effective_tax_rate_improvement'] = None
            else:
                factors['effective_tax_rate_improvement'] = None
        else:
            factors['effective_tax_rate_improvement'] = None
    except:
        factors['effective_tax_rate_improvement'] = None
    
    # 10. financial_health (财务健康度)
    try:
        # 尝试多个流动资产字段
        current_assets = (latest_data.get('Current Assets - Total', 0) or
                         latest_data.get('Total Current Assets', 0) or 0)
        
        # 尝试多个流动负债字段
        current_liabilities = (latest_data.get('Current Liabilities - Total', 0) or
                              latest_data.get('Total Current Liabilities', 0) or 0)
        
        if current_liabilities > 0:
            current_ratio = current_assets / current_liabilities
            factors['financial_health'] = min(current_ratio, 10)
        else:
            factors['financial_health'] = None
    except:
        factors['financial_health'] = None
    
    # 11. valuation_bubble_signal (估值泡沫信号 - PEG比率)
    try:
        net_income = latest_data.get('Normalized Net Income - Bottom Line', 0) or 0
        # 尝试多个股份字段
        shares = (latest_data.get('Shares Outstanding - Diluted Average', 0) or
                 latest_data.get('Shares used to calculate Diluted EPS - Total', 0) or
                 latest_data.get('Common Shares - Outstanding - Total', 0) or 0)
        
        if net_income > 0 and shares > 0 and price_data:
            eps = net_income / shares
            pe_ratio = price_data['close_price'] / eps
            
            # 需要计算收入CAGR作为增长率
            if len(pivot_data) >= 3:
                revenues = []
                for i in range(min(5, len(pivot_data))):
                    data = pivot_data.iloc[-(i+1)]
                    revenue = data.get('Revenue from Business Activities - Total', 0) or 0
                    if revenue > 0:
                        revenues.append({'year': len(pivot_data) - i, 'value': revenue})
                
                revenues.reverse()
                
                if len(revenues) >= 2:
                    start_revenue = revenues[0]['value']
                    end_revenue = revenues[-1]['value']
                    years = len(revenues) - 1
                    
                    if start_revenue > 0:
                        revenue_cagr = (pow(end_revenue / start_revenue, 1 / years) - 1) * 100
                        
                        if revenue_cagr > 0:
                            peg_ratio = pe_ratio / revenue_cagr
                            factors['valuation_bubble_signal'] = min(peg_ratio, 5)
                        else:
                            factors['valuation_bubble_signal'] = None
                    else:
                        factors['valuation_bubble_signal'] = None
                else:
                    factors['valuation_bubble_signal'] = None
            else:
                factors['valuation_bubble_signal'] = None
        else:
            factors['valuation_bubble_signal'] = None
    except:
        factors['valuation_bubble_signal'] = None
    
    # 12. roce_stability (ROCE稳定性)
    try:
        if len(pivot_data) >= 3:
            roce_values = []
            for i in range(min(5, len(pivot_data))):
                data = pivot_data.iloc[-(i+1)]
                net_income = data.get('Normalized Net Income - Bottom Line', 0) or 0
                debt = data.get('Debt - Total', 0) or 0
                equity_priority = [
                    'Shareholders\' Equity - Attributable to Parent Shareholders - Total',
                    'Common Equity - Total',
                    'Common Equity Attributable to Parent Shareholders'
                ]
                total_equity, _ = get_field_with_priority(data, equity_priority)
                capital_employed = debt + total_equity
                
                if capital_employed > 0:
                    roce = (net_income / capital_employed) * 100
                    roce_values.append(roce)
            
            if len(roce_values) >= 3:
                factors['roce_stability'] = np.std(roce_values)
            else:
                factors['roce_stability'] = None
        else:
            factors['roce_stability'] = None
    except:
        factors['roce_stability'] = None
    
    # 13. revenue_yoy (收入同比增长)
    try:
        if len(pivot_data) >= 2:
            current_revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
            prev_data = pivot_data.iloc[-2]
            prev_revenue = prev_data.get('Revenue from Business Activities - Total', 0) or 0
            
            if prev_revenue > 0:
                revenue_yoy = (current_revenue - prev_revenue) / prev_revenue * 100
                factors['revenue_yoy'] = np.clip(revenue_yoy, -100, 500)
            else:
                factors['revenue_yoy'] = None
        else:
            factors['revenue_yoy'] = None
    except:
        factors['revenue_yoy'] = None
    
    # 14. revenue_cagr (收入CAGR) - 改进的负值处理
    try:
        revenues = []
        for i, row in pivot_data.iterrows():
            revenue = row.get('Revenue from Business Activities - Total', 0) or 0
            fiscal_year = int(row['financial_period_absolute'].replace('FY', ''))
            if revenue > 0:
                revenues.append({'year': fiscal_year, 'value': revenue})
        
        if len(revenues) >= 2:
            start_revenue, end_revenue, calculation_type = find_longest_positive_range(revenues)
            
            if start_revenue and end_revenue:
                years = end_revenue['year'] - start_revenue['year']
                if years > 0:
                    if calculation_type == "turnaround_growth":
                        # 扭亏增长：使用算术平均
                        revenue_cagr = (end_revenue['value'] - start_revenue['value']) / start_revenue['value'] / years * 100
                    else:
                        # 正常CAGR计算
                        revenue_cagr = (pow(end_revenue['value'] / start_revenue['value'], 1 / years) - 1) * 100
                    
                    factors['revenue_cagr'] = np.clip(revenue_cagr, -50, 100)
                else:
                    factors['revenue_cagr'] = None
            else:
                factors['revenue_cagr'] = None
        else:
            factors['revenue_cagr'] = None
    except:
        factors['revenue_cagr'] = None
    
    # 15. net_income_yoy (净利润同比增长)
    try:
        if len(pivot_data) >= 2:
            current_ni = latest_data.get('Normalized Net Income - Bottom Line', 0) or 0
            prev_data = pivot_data.iloc[-2]
            prev_ni = prev_data.get('Normalized Net Income - Bottom Line', 0) or 0
            
            if prev_ni != 0:
                ni_yoy = (current_ni - prev_ni) / abs(prev_ni) * 100
                factors['net_income_yoy'] = np.clip(ni_yoy, -200, 500)
            else:
                factors['net_income_yoy'] = None
        else:
            factors['net_income_yoy'] = None
    except:
        factors['net_income_yoy'] = None
    
    # 16. profit_revenue_ratio (利润收入比)
    try:
        net_income = latest_data.get('Normalized Net Income - Bottom Line', 0) or 0
        revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
        
        if revenue > 0:
            profit_margin = (net_income / revenue) * 100
            factors['profit_revenue_ratio'] = np.clip(profit_margin, -80, 80)  # 调整限制
        else:
            factors['profit_revenue_ratio'] = None
    except:
        factors['profit_revenue_ratio'] = None
    
    # 17. fcf_per_share (每股自由现金流)
    try:
        fcf = latest_data.get('Free Cash Flow', 0) or 0
        # 尝试多个股份字段
        shares = (latest_data.get('Shares Outstanding - Diluted Average', 0) or
                 latest_data.get('Shares used to calculate Diluted EPS - Total', 0) or
                 latest_data.get('Common Shares - Outstanding - Total', 0) or 0)
        
        if shares > 0:
            fcf_per_share = fcf / shares
            factors['fcf_per_share'] = np.clip(fcf_per_share, -100, 100)
        else:
            factors['fcf_per_share'] = None
    except:
        factors['fcf_per_share'] = None
    
    # 18. fcf_cagr (自由现金流CAGR) - 改进的负值处理
    try:
        fcf_values = []
        for i, row in pivot_data.iterrows():
            fcf = row.get('Free Cash Flow', 0) or 0
            fiscal_year = int(row['financial_period_absolute'].replace('FY', ''))
            fcf_values.append({'year': fiscal_year, 'value': fcf})
        
        if len(fcf_values) >= 2:
            start_fcf, end_fcf, calculation_type = find_longest_positive_range(fcf_values)
            
            if start_fcf and end_fcf:
                years = end_fcf['year'] - start_fcf['year']
                if years > 0:
                    if calculation_type == "turnaround_growth":
                        # 扭亏增长：使用算术平均
                        fcf_cagr = (end_fcf['value'] - start_fcf['value']) / abs(start_fcf['value']) / years * 100
                    else:
                        # 正常CAGR计算
                        fcf_cagr = (pow(end_fcf['value'] / start_fcf['value'], 1 / years) - 1) * 100
                    
                    factors['fcf_cagr'] = np.clip(fcf_cagr, -100, 200)
                else:
                    factors['fcf_cagr'] = None
            else:
                factors['fcf_cagr'] = None
        else:
            factors['fcf_cagr'] = None
    except:
        factors['fcf_cagr'] = None
    
    # 19. operating_margin (营业利润率)
    try:
        operating_income = latest_data.get('Operating Income', 0) or 0
        revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
        
        if revenue > 0:
            operating_margin = (operating_income / revenue) * 100
            factors['operating_margin'] = np.clip(operating_margin, -100, 100)
        else:
            factors['operating_margin'] = None
    except:
        factors['operating_margin'] = None
    
    # 20. operating_margin_std (营业利润率稳定性)
    try:
        if len(pivot_data) >= 3:
            margins = []
            for i in range(min(4, len(pivot_data))):
                data = pivot_data.iloc[-(i+1)]
                operating_income = data.get('Operating Income', 0) or 0
                revenue = data.get('Revenue from Business Activities - Total', 0) or 0
                
                if revenue > 0:
                    margin = (operating_income / revenue) * 100
                    margins.append(margin)
            
            if len(margins) >= 3:
                factors['operating_margin_std'] = np.std(margins)
            else:
                factors['operating_margin_std'] = None
        else:
            factors['operating_margin_std'] = None
    except:
        factors['operating_margin_std'] = None
    
    # 21. roic (投资回报率) - 调整限制到-50%到100%
    try:
        net_income = latest_data.get('Normalized Net Income - Bottom Line', 0) or 0
        debt = latest_data.get('Debt - Total', 0) or 0
        equity_priority = [
            'Shareholders\' Equity - Attributable to Parent Shareholders - Total',
            'Common Equity - Total',
            'Common Equity Attributable to Parent Shareholders'
        ]
        total_equity, equity_field = get_field_with_priority(latest_data, equity_priority)
        invested_capital = debt + total_equity
        
        if invested_capital > 0 and net_income > 0:
            roic = (net_income / invested_capital) * 100
            factors['roic'] = np.clip(roic, -50, 100)  # 调整限制
            factors['roic_field'] = equity_field or 'PRIMARY'
        else:
            factors['roic'] = None
            factors['roic_field'] = 'NA'
    except:
        factors['roic'] = None
        factors['roic_field'] = 'NA'
    
    # 22. roic_cagr (ROIC CAGR) - 改进的负值处理
    try:
        roic_values = []
        for i, row in pivot_data.iterrows():
            net_income = row.get('Normalized Net Income - Bottom Line', 0) or 0
            debt = row.get('Debt - Total', 0) or 0
            equity_priority = [
                'Shareholders\' Equity - Attributable to Parent Shareholders - Total',
                'Common Equity - Total',
                'Common Equity Attributable to Parent Shareholders'
            ]
            total_equity, _ = get_field_with_priority(row, equity_priority)
            invested_capital = debt + total_equity
            fiscal_year = int(row['financial_period_absolute'].replace('FY', ''))
            
            if invested_capital > 0 and net_income > 0:
                roic = (net_income / invested_capital) * 100
                roic_values.append({'year': fiscal_year, 'value': roic})
            else:
                roic_values.append({'year': fiscal_year, 'value': np.nan})
        
        # 过滤掉NaN值
        valid_roic = [item for item in roic_values if not np.isnan(item['value']) and item['value'] > 0]
        
        if len(valid_roic) >= 2:
            start_roic, end_roic, calculation_type = find_longest_positive_range(valid_roic)
            
            if start_roic and end_roic:
                years = end_roic['year'] - start_roic['year']
                if years > 0:
                    if calculation_type == "turnaround_growth":
                        # 扭亏增长：使用算术平均
                        roic_cagr = (end_roic['value'] - start_roic['value']) / start_roic['value'] / years * 100
                    else:
                        # 正常CAGR计算
                        roic_cagr = (pow(end_roic['value'] / start_roic['value'], 1 / years) - 1) * 100
                    
                    factors['roic_cagr'] = np.clip(roic_cagr, -100, 200)
                else:
                    factors['roic_cagr'] = None
            else:
                factors['roic_cagr'] = None
        else:
            factors['roic_cagr'] = None
    except:
        factors['roic_cagr'] = None
    
    # 23. effective_tax_rate (有效税率)
    try:
        ebit = latest_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
        tax_expense = latest_data.get('Income Taxes', 0) or 0
        
        if ebit > 0:
            tax_rate = (tax_expense / ebit) * 100
            # 合理税率范围检查
            if -50 <= tax_rate <= 100:
                factors['effective_tax_rate'] = tax_rate
            else:
                factors['effective_tax_rate'] = None
        else:
            factors['effective_tax_rate'] = None
    except:
        factors['effective_tax_rate'] = None
    
    # 24. effective_tax_rate_std (有效税率稳定性)
    try:
        if len(pivot_data) >= 3:
            tax_rates = []
            for i in range(min(4, len(pivot_data))):
                data = pivot_data.iloc[-(i+1)]
                ebit = data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
                tax_expense = data.get('Income Taxes', 0) or 0
                
                if ebit > 0:
                    tax_rate = (tax_expense / ebit) * 100
                    # 合理税率范围检查
                    if -50 <= tax_rate <= 100:
                        tax_rates.append(tax_rate)
            
            if len(tax_rates) >= 3:
                factors['effective_tax_rate_std'] = np.std(tax_rates)
            else:
                factors['effective_tax_rate_std'] = None
        else:
            factors['effective_tax_rate_std'] = None
    except:
        factors['effective_tax_rate_std'] = None
    
    print(f"   ✅ {stock_symbol}: 因子计算完成")
    return factors

def main():
    """主函数 - 计算调整截断限制后的Mega7因子"""
    
    print("🚀 开始计算调整截断限制后的24因子（Mega7测试）")
    print("=" * 80)
    
    # 连接数据库
    client = connect_to_ap_research()
    
    # Mega7股票
    mega7_stocks = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META']
    
    all_factors = []
    
    for stock in mega7_stocks:
        try:
            factors = calculate_improved_factors(client, stock)
            if factors:
                all_factors.append(factors)
        except Exception as e:
            print(f"   ❌ {stock}: 计算失败 - {str(e)}")
    
    # 转换为DataFrame并保存
    if all_factors:
        df = pd.DataFrame(all_factors)
        
        # 生成时间戳文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"mega7_adjusted_limits_factors_{timestamp}.csv"
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        
        print(f"\n📊 结果保存到: {filename}")
        print(f"📈 成功计算 {len(all_factors)} 只股票的因子")
        
        # 显示关键因子的调整效果
        print("\n🎯 关键因子调整效果对比:")
        print("=" * 80)
        
        key_factors = ['adjusted_roe', 'roic', 'profit_revenue_ratio', 'tech_premium']
        
        for factor in key_factors:
            if factor in df.columns:
                print(f"\n📊 {factor}:")
                for _, row in df.iterrows():
                    value = row.get(factor)
                    if value is not None:
                        print(f"   {row['stock_symbol']}: {value:.2f}%")
                    else:
                        print(f"   {row['stock_symbol']}: N/A")
        
        # 计算覆盖率
        print(f"\n📈 因子覆盖率统计:")
        print("-" * 60)
        
        coverage_stats = []
        for col in df.columns:
            if col not in ['stock_symbol', 'fiscal_year', 'period_end_date', 'adjusted_roe_field', 'roic_field']:
                non_null_count = df[col].count()
                total_count = len(df)
                coverage_rate = (non_null_count / total_count) * 100
                coverage_stats.append({
                    'factor': col,
                    'coverage_count': non_null_count,
                    'total_count': total_count,
                    'coverage_rate': coverage_rate
                })
        
        coverage_df = pd.DataFrame(coverage_stats)
        coverage_df = coverage_df.sort_values('coverage_rate', ascending=False)
        
        for _, row in coverage_df.iterrows():
            print(f"   {row['factor']:<30}: {row['coverage_count']}/{row['total_count']} ({row['coverage_rate']:.1f}%)")
    
    else:
        print("❌ 未能计算任何股票的因子")

if __name__ == "__main__":
    main()
