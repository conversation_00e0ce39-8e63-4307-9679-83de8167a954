from clickhouse_driver import Client

# 连接ClickHouse数据库
client = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 季报完整性趋势分析 ===")

# 分析最近8年的季报完整性
years_to_analyze = list(range(2024, 2016, -1))

print("\n各年份季报完整性详细分析:")
print("年份   Q1股票数  Q2股票数  Q3股票数  Q4股票数  最少季度  完整性")
print("-" * 65)

yearly_stats = []

for year in years_to_analyze:
    # 查询该年份各季度的股票数
    quarter_counts = {}
    
    for quarter in ['Q1', 'Q2', 'Q3', 'Q4']:
        query_quarter = f"""
        SELECT COUNT(DISTINCT stock_symbol) as stocks
        FROM priority_quality_fundamental_data_complete_deduped
        WHERE financial_period_absolute = 'FY{year}{quarter}'
        """
        
        result = client.execute(query_quarter)
        quarter_counts[quarter] = result[0][0]
    
    # 计算统计信息
    q1, q2, q3, q4 = quarter_counts['Q1'], quarter_counts['Q2'], quarter_counts['Q3'], quarter_counts['Q4']
    min_stocks = min(q1, q2, q3, q4)
    max_stocks = max(q1, q2, q3, q4)
    
    # 计算完整性（以最少的季度数为基准）
    if max_stocks > 0:
        completeness = min_stocks / max_stocks * 100
    else:
        completeness = 0
    
    yearly_stats.append({
        'year': year,
        'q1': q1, 'q2': q2, 'q3': q3, 'q4': q4,
        'min_stocks': min_stocks,
        'completeness': completeness
    })
    
    print(f"{year}   {q1:8d}  {q2:8d}  {q3:8d}  {q4:8d}  {min_stocks:8d}  {completeness:7.1f}%")

# 分析趋势
print(f"\n趋势分析:")
print(f"  最高完整性: {max(stat['completeness'] for stat in yearly_stats):.1f}%")
print(f"  最低完整性: {min(stat['completeness'] for stat in yearly_stats):.1f}%")

# 找出完整性最好和最差的年份
best_year = max(yearly_stats, key=lambda x: x['completeness'])
worst_year = min(yearly_stats, key=lambda x: x['completeness'])

print(f"  完整性最好年份: {best_year['year']} ({best_year['completeness']:.1f}%)")
print(f"  完整性最差年份: {worst_year['year']} ({worst_year['completeness']:.1f}%)")

# 分析股票数量变化趋势
print(f"\n股票数量趋势:")
for i, stat in enumerate(yearly_stats):
    if i > 0:
        prev_stat = yearly_stats[i-1]
        change = stat['min_stocks'] - prev_stat['min_stocks']
        change_str = f"({change:+d})" if change != 0 else "(无变化)"
        print(f"  {stat['year']}: {stat['min_stocks']}只股票 {change_str}")
    else:
        print(f"  {stat['year']}: {stat['min_stocks']}只股票")

print("\n=== 分析完成 ===")

