import pandas as pd
from clickhouse_driver import Client
import time

# 连接ClickHouse
client = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

def create_table_if_not_exists():
    """创建表（如果不存在）"""
    print("正在创建表...")
    
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS ap_research.fundamental_data_with_announcement_dates
    (
        stock_symbol String,
        instrument String,
        financial_period_absolute String,
        report_date DateTime,
        period_end_date DateTime,
        period_type String,
        statement_type String,
        item_name String,
        value Float64,
        currency String,
        scale String,
        original_announcement_date_time DateTime,
        effective_date DateTime,
        created_at_original DateTime,
        updated_at_original DateTime,
        create_date DateTime
    )
    ENGINE = MergeTree()
    ORDER BY (stock_symbol, financial_period_absolute, statement_type, item_name)
    PARTITION BY toYear(report_date)
    """
    
    try:
        client.execute(create_table_sql)
        print("✅ 表创建成功或已存在")
        return True
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False

def import_data_from_csv():
    """从CSV文件导入数据到数据库"""
    print("正在从CSV文件导入数据...")
    
    # 读取CSV文件
    csv_file = 'ap_research_fundamental_with_announcement_dates_matched.csv'
    
    try:
        print(f"正在读取文件: {csv_file}")
        df = pd.read_csv(csv_file)
        print(f"✅ 成功读取 {len(df):,} 条记录")
        
        # 处理日期字段
        date_columns = [
            'report_date', 'period_end_date', 'original_announcement_date_time', 
            'effective_date', 'created_at_original', 'updated_at_original', 'create_date'
        ]
        
        for col in date_columns:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors='coerce')
        
        # 处理数值字段
        if 'value' in df.columns:
            df['value'] = pd.to_numeric(df['value'], errors='coerce')
        
        # 处理字符串字段，确保不为None
        string_columns = [
            'stock_symbol', 'instrument', 'financial_period_absolute', 'period_type',
            'statement_type', 'item_name', 'currency', 'scale'
        ]
        
        for col in string_columns:
            if col in df.columns:
                df[col] = df[col].fillna('').astype(str)
        
        print("✅ 数据预处理完成")
        
        # 分批插入数据
        batch_size = 10000
        total_batches = (len(df) + batch_size - 1) // batch_size
        
        print(f"开始分批插入，共 {total_batches} 批，每批 {batch_size} 条记录")
        
        for i in range(0, len(df), batch_size):
            batch_df = df.iloc[i:i+batch_size]
            batch_num = i // batch_size + 1
            
            print(f"正在插入第 {batch_num}/{total_batches} 批 ({len(batch_df)} 条记录)...")
            
            # 准备插入数据
            insert_data = []
            for _, row in batch_df.iterrows():
                insert_data.append([
                    row.get('stock_symbol', ''),
                    row.get('instrument', ''),
                    row.get('financial_period_absolute', ''),
                    row.get('report_date'),
                    row.get('period_end_date'),
                    row.get('period_type', ''),
                    row.get('statement_type', ''),
                    row.get('item_name', ''),
                    row.get('value'),
                    row.get('currency', ''),
                    row.get('scale', ''),
                    row.get('original_announcement_date_time'),
                    row.get('effective_date'),
                    row.get('created_at_original'),
                    row.get('updated_at_original'),
                    row.get('create_date')
                ])
            
            # 执行插入
            try:
                client.execute(
                    """
                    INSERT INTO ap_research.fundamental_data_with_announcement_dates
                    (
                        stock_symbol, instrument, financial_period_absolute, report_date,
                        period_end_date, period_type, statement_type, item_name, value,
                        currency, scale, original_announcement_date_time, effective_date,
                        created_at_original, updated_at_original, create_date
                    )
                    VALUES
                    """,
                    insert_data
                )
                print(f"  ✅ 第 {batch_num} 批插入成功")
                
            except Exception as e:
                print(f"  ❌ 第 {batch_num} 批插入失败: {e}")
                continue
            
            # 添加延迟避免数据库压力过大
            time.sleep(0.1)
        
        print("✅ 所有数据插入完成")
        return True
        
    except FileNotFoundError:
        print(f"❌ 文件 {csv_file} 不存在")
        return False
    except Exception as e:
        print(f"❌ 导入数据失败: {e}")
        return False

def verify_imported_data():
    """验证导入的数据"""
    print("正在验证导入的数据...")
    
    try:
        # 检查记录数
        count_result = client.execute("SELECT COUNT(*) FROM ap_research.fundamental_data_with_announcement_dates")
        total_count = count_result[0][0]
        print(f"✅ 总记录数: {total_count:,}")
        
        # 检查股票数
        stock_count = client.execute("SELECT COUNT(DISTINCT stock_symbol) FROM ap_research.fundamental_data_with_announcement_dates")
        unique_stocks = stock_count[0][0]
        print(f"✅ 涉及股票数: {unique_stocks}")
        
        # 检查时间范围
        date_range = client.execute("""
            SELECT 
                MIN(report_date) as min_date,
                MAX(report_date) as max_date
            FROM ap_research.fundamental_data_with_announcement_dates
        """)
        min_date, max_date = date_range[0]
        print(f"✅ 时间范围: {min_date} 到 {max_date}")
        
        # 按报表类型统计
        stmt_stats = client.execute("""
            SELECT 
                statement_type,
                COUNT(*) as record_count
            FROM ap_research.fundamental_data_with_announcement_dates
            GROUP BY statement_type
            ORDER BY record_count DESC
        """)
        
        print("✅ 按报表类型统计:")
        for stmt_type, count in stmt_stats:
            print(f"  {stmt_type}: {count:,} 条记录")
        
        # 检查有公告日期的记录数
        announcement_count = client.execute("""
            SELECT COUNT(*) 
            FROM ap_research.fundamental_data_with_announcement_dates
            WHERE original_announcement_date_time IS NOT NULL
        """)
        announcement_records = announcement_count[0][0]
        print(f"✅ 有公告日期的记录: {announcement_records:,}")
        print(f"✅ 匹配率: {announcement_records/total_count*100:.2f}%")
        
        # 显示样本数据
        sample_data = client.execute("""
            SELECT 
                stock_symbol, instrument, financial_period_absolute, 
                statement_type, item_name, value, original_announcement_date_time
            FROM ap_research.fundamental_data_with_announcement_dates
            LIMIT 5
        """)
        
        print("✅ 样本数据:")
        for row in sample_data:
            print(f"  {row}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证数据失败: {e}")
        return False

def main():
    """主函数"""
    print("="*80)
    print("将匹配结果导入到ap_research数据库")
    print("="*80)
    
    try:
        # 1. 创建表
        if not create_table_if_not_exists():
            return
        
        # 2. 导入数据
        if not import_data_from_csv():
            return
        
        # 3. 验证数据
        if not verify_imported_data():
            return
        
        print("\n" + "="*80)
        print("✅ 数据导入完成！")
        print("="*80)
        print("表名: ap_research.fundamental_data_with_announcement_dates")
        print("包含字段: stock_symbol, instrument, financial_period_absolute, report_date, period_end_date, period_type, statement_type, item_name, value, currency, scale, original_announcement_date_time, effective_date, created_at_original, updated_at_original, create_date")
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    main()







