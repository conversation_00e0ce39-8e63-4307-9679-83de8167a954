from clickhouse_driver import Client
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 连接ClickHouse数据库
client = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== <PERSON> 26因子实现（修正最终版）===")
print("严格按照因子规范和时间窗口要求实现")

def get_stock_listing_years():
    """获取每只股票的上市年限"""
    print("\n1. 获取股票上市年限...")
    
    query = """
    SELECT 
        stock_symbol,
        MIN(financial_period_absolute) as first_period,
        MAX(financial_period_absolute) as last_period,
        COUNT(DISTINCT financial_period_absolute) as periods_count
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE statement_type = 'Income Statement'
    GROUP BY stock_symbol
    """
    
    result = client.execute(query)
    df_listing = pd.DataFrame(result, columns=['stock_symbol', 'first_period', 'last_period', 'periods_count'])
    
    # 计算上市年限（基于财务期间）
    def calculate_years(first_period, last_period):
        try:
            # 提取年份信息
            first_year = int(first_period[2:6])  # FY2020Q1 -> 2020
            last_year = int(last_period[2:6])    # FY2024Q4 -> 2024
            return last_year - first_year + 1
        except:
            return 1
    
    df_listing['listing_years'] = df_listing.apply(
        lambda x: calculate_years(x['first_period'], x['last_period']), axis=1
    )
    
    print(f"  获取到 {len(df_listing)} 只股票的上市年限信息")
    return df_listing

def get_financial_data():
    """获取所有财务数据"""
    print("\n2. 获取财务数据...")
    
    query = """
    SELECT 
        stock_symbol,
        financial_period_absolute,
        statement_type,
        item_name,
        value,
        announcement_date,
        effective_date
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE item_name IN (
        -- 利润表项目
        'Revenue from Business Activities - Total',
        'Revenue from Goods & Services',
        'Cost of Revenues - Total',
        'Cost of Operating Revenue',
        'Research & Development Expense',
        'Research & Development Expense - Supplemental',
        'Operating Profit before Non-Recurring Income/Expense',
        'Income before Taxes',
        'Earnings before Interest & Taxes (EBIT)',
        'Earnings before Interest, Taxes, Depreciation & Amortization (EBITDA)',
        'Net Income - Basic - including Extraordinary Items Applicable to Common - Total',
        'Normalized Net Income - Bottom Line',
        'Income Taxes',
        'Gross Profit - Industrials/Property - Total',
        
        -- 资产负债表项目
        'Total Assets',
        'Shareholders'' Equity - Attributable to Parent Shareholders - Total',
        'Total Shareholders'' Equity - including Minority Interest & Hybrid Debt',
        'Common Equity - Total',
        'Total Liabilities',
        'Debt - Total',
        'Net Debt',
        'Cash & Cash Equivalents - Total',
        'Cash & Short Term Investments - Total',
        'Intangible Assets - Total - Net',
        'Intangible Assets - excluding Goodwill - Net - Total',
        'Common Shares - Outstanding - Total',
        'Total Shares Outstanding',
        
        -- 现金流量表项目
        'Net Cash Flow from Operating Activities',
        'Capital Expenditures - Total',
        'Income Taxes - Paid/(Reimbursed) - Cash Flow',
        'Income Taxes - Paid/(Reimbursed) - Cash Flow - Supplemental',
        'Free Cash Flow',
        'Cash Flow from Operations - Total'
    )
    AND effective_date IS NOT NULL
    ORDER BY stock_symbol, financial_period_absolute, statement_type, item_name
    """
    
    result = client.execute(query)
    df_financial = pd.DataFrame(result, columns=['stock_symbol', 'financial_period_absolute', 'statement_type', 'item_name', 'value', 'announcement_date', 'effective_date'])
    
    print(f"  获取到 {len(df_financial):,} 条财务数据记录")
    return df_financial

def get_market_data():
    """获取市场数据（股价等）"""
    print("\n3. 获取市场数据...")
    
    query = """
    SELECT 
        stock_symbol,
        trade_date,
        close_price,
        volume,
        turnover
    FROM priority_quality_stock_hfq
    ORDER BY stock_symbol, trade_date
    """
    
    result = client.execute(query)
    df_market = pd.DataFrame(result, columns=['stock_symbol', 'trade_date', 'close_price', 'volume', 'turnover'])
    
    print(f"  获取到 {len(df_market):,} 条市场数据记录")
    return df_market

def analyze_missing_annual_data(df_financial, df_annual):
    """分析年报数据缺失情况"""
    print("\n4. 分析年报数据缺失情况...")
    
    # 获取所有股票
    all_stocks = df_financial['stock_symbol'].unique()
    stocks_with_annual = df_annual['stock_symbol'].unique()
    
    missing_stocks = set(all_stocks) - set(stocks_with_annual)
    
    print(f"  总股票数: {len(all_stocks):,}")
    print(f"  有年报数据的股票数: {len(stocks_with_annual):,}")
    print(f"  缺失年报数据的股票数: {len(missing_stocks):,}")
    
    if len(missing_stocks) > 0:
        print(f"  缺失年报数据的股票列表（前10只）:")
        for i, stock in enumerate(list(missing_stocks)[:10]):
            print(f"    {i+1}. {stock}")
        if len(missing_stocks) > 10:
            print(f"    ... 还有 {len(missing_stocks) - 10} 只股票")
    
    # 分析每只有年报数据的股票的年份分布
    year_distribution = df_annual.groupby('stock_symbol')['fiscal_year'].agg(['count', 'min', 'max']).reset_index()
    year_distribution.columns = ['stock_symbol', 'year_count', 'min_year', 'max_year']
    
    print(f"\n  年报数据年份分布:")
    print(f"    平均年份数: {year_distribution['year_count'].mean():.1f}")
    print(f"    最少年份数: {year_distribution['year_count'].min()}")
    print(f"    最多年份数: {year_distribution['year_count'].max()}")
    
    # 统计不同年份数的股票数量
    year_count_stats = year_distribution['year_count'].value_counts().sort_index()
    print(f"    年份数分布:")
    for year_count, stock_count in year_count_stats.items():
        print(f"      {year_count}年: {stock_count}只股票")
    
    return missing_stocks, year_distribution

def process_financial_data(df_financial):
    """处理财务数据，转换为宽表格式"""
    print("\n5. 处理财务数据...")
    
    # 只保留年报数据（Q4或年度数据）
    df_annual_only = df_financial[
        (df_financial['financial_period_absolute'].str.contains('Q4')) |
        (df_financial['financial_period_absolute'].str.contains('FY\d{4}$'))
    ].copy()
    
    print(f"  年报数据记录数: {len(df_annual_only):,}")
    
    # 数据透视
    df_pivot = df_annual_only.pivot_table(
        index=['stock_symbol', 'financial_period_absolute', 'announcement_date', 'effective_date'],
        columns='item_name',
        values='value',
        aggfunc='first'
    ).reset_index()
    
    # 提取财年信息
    df_pivot['fiscal_year'] = df_pivot['financial_period_absolute'].str.extract(r'FY(\d{4})').astype(float)
    df_pivot['quarter'] = df_pivot['financial_period_absolute'].str.extract(r'Q(\d)').astype(float)
    
    print(f"  处理后的年报数据记录数: {len(df_pivot):,}")
    return df_pivot

def calculate_annual_data(df_pivot):
    """计算年度汇总数据"""
    print("\n6. 计算年度汇总数据...")
    
    annual_data = []
    
    for stock_symbol in df_pivot['stock_symbol'].unique():
        stock_data = df_pivot[df_pivot['stock_symbol'] == stock_symbol].copy()
        
        for year in stock_data['fiscal_year'].unique():
            year_data = stock_data[stock_data['fiscal_year'] == year]
            
            # 计算年度汇总数据
            annual_row = {
                'stock_symbol': stock_symbol,
                'fiscal_year': year,
                'effective_date': year_data['effective_date'].iloc[0] if len(year_data) > 0 else None
            }
            
            # 所有财务指标都取最新值（因为已经是年报数据）
            all_cols = [
                'Revenue from Business Activities - Total', 'Revenue from Goods & Services',
                'Cost of Revenues - Total', 'Cost of Operating Revenue',
                'Research & Development Expense', 'Research & Development Expense - Supplemental',
                'Operating Profit before Non-Recurring Income/Expense',
                'Income before Taxes',
                'Earnings before Interest & Taxes (EBIT)',
                'Earnings before Interest, Taxes, Depreciation & Amortization (EBITDA)',
                'Net Income - Basic - including Extraordinary Items Applicable to Common - Total',
                'Normalized Net Income - Bottom Line',
                'Income Taxes',
                'Gross Profit - Industrials/Property - Total',
                'Total Assets',
                'Shareholders'' Equity - Attributable to Parent Shareholders - Total',
                'Total Shareholders'' Equity - including Minority Interest & Hybrid Debt',
                'Common Equity - Total',
                'Total Liabilities',
                'Debt - Total',
                'Net Debt',
                'Cash & Cash Equivalents - Total',
                'Cash & Short Term Investments - Total',
                'Intangible Assets - Total - Net',
                'Intangible Assets - excluding Goodwill - Net - Total',
                'Common Shares - Outstanding - Total',
                'Total Shares Outstanding',
                'Net Cash Flow from Operating Activities',
                'Capital Expenditures - Total',
                'Income Taxes - Paid/(Reimbursed) - Cash Flow',
                'Income Taxes - Paid/(Reimbursed) - Cash Flow - Supplemental',
                'Free Cash Flow',
                'Cash Flow from Operations - Total'
            ]
            
            for col in all_cols:
                if col in year_data.columns:
                    values = year_data[col].dropna()
                    if len(values) > 0:
                        annual_row[col] = values.iloc[-1]  # 取最新值
            
            annual_data.append(annual_row)
    
    df_annual = pd.DataFrame(annual_data)
    print(f"  年度汇总数据记录数: {len(df_annual):,}")
    return df_annual

def calculate_26_factors_for_date(stock_data, target_date, listing_years):
    """计算指定日期的26个因子，严格按照规范"""
    factors = {}
    
    try:
        # 筛选到目标日期为止的数据
        available_data = stock_data[stock_data['effective_date'] <= target_date].copy()
        available_data = available_data.sort_values('fiscal_year')
        
        if len(available_data) < 1:
            return factors
        
        # 获取最新数据
        latest_data = available_data.iloc[-1]
        prev_data = available_data.iloc[-2] if len(available_data) >= 2 else None
        
        # 重新计算上市年限（基于可用数据）
        if len(available_data) >= 2:
            first_year = available_data['fiscal_year'].min()
            last_year = available_data['fiscal_year'].max()
            available_listing_years = last_year - first_year + 1
        else:
            available_listing_years = 1
        
        # 1. 技术溢价 (tech_premium) - 始终用最新年度数据，最低1年
        try:
            rd_expense = latest_data.get('Research & Development Expense', 0) or 0
            rd_supplemental = latest_data.get('Research & Development Expense - Supplemental', 0) or 0
            total_rd = rd_expense + rd_supplemental
            revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
            
            if revenue > 0:
                factors['tech_premium'] = (total_rd / revenue) * 100
            else:
                factors['tech_premium'] = None
        except:
            factors['tech_premium'] = None
        
                 # 2. 技术差距警告 (tech_gap_warning) - 上市≥2年：t年与t-1年，上市<2年：因子缺失
         try:
             if available_listing_years >= 2 and prev_data is not None:
                prev_rd = prev_data.get('Research & Development Expense', 0) or 0
                prev_rd_supp = prev_data.get('Research & Development Expense - Supplemental', 0) or 0
                prev_total_rd = prev_rd + prev_rd_supp
                prev_revenue = prev_data.get('Revenue from Business Activities - Total', 0) or 0
                
                if prev_revenue > 0 and revenue > 0:
                    prev_rd_ratio = (prev_total_rd / prev_revenue) * 100
                    curr_rd_ratio = factors.get('tech_premium', 0) or 0
                    
                    if curr_rd_ratio < prev_rd_ratio * 0.8:  # 研发强度下降20%以上
                        factors['tech_gap_warning'] = 1
                    else:
                        factors['tech_gap_warning'] = 0
                else:
                    factors['tech_gap_warning'] = 0
            else:
                factors['tech_gap_warning'] = None
        except:
            factors['tech_gap_warning'] = None
        
        # 3. 专利密度 (patent_density) - 始终用最新年度数据，最低1年
        try:
            intangible_assets = latest_data.get('Intangible Assets - Total - Net', 0) or 0
            total_assets = latest_data.get('Total Assets', 0) or 0
            
            if total_assets > 0:
                factors['patent_density'] = intangible_assets / total_assets
            else:
                factors['patent_density'] = None
        except:
            factors['patent_density'] = None
        
        # 4. 生态系统现金比率 (ecosystem_cash_ratio) - 始终用最新年度数据，最低1年
        try:
            cash = latest_data.get('Cash & Cash Equivalents - Total', 0) or 0
            total_assets = latest_data.get('Total Assets', 0) or 0
            
            if total_assets > 0:
                factors['ecosystem_cash_ratio'] = cash / total_assets
            else:
                factors['ecosystem_cash_ratio'] = None
        except:
            factors['ecosystem_cash_ratio'] = None
        
        # 5. 调整后ROCE (adjusted_roce) - 始终用最新年度数据，最低1年
        try:
            operating_profit = latest_data.get('Operating Profit before Non-Recurring Income/Expense', 0) or 0
            total_assets = latest_data.get('Total Assets', 0) or 0
            cash = latest_data.get('Cash & Cash Equivalents - Total', 0) or 0
            
            denominator = total_assets - cash
            if denominator > 0:
                factors['adjusted_roce'] = operating_profit / denominator
            else:
                factors['adjusted_roce'] = None
        except:
            factors['adjusted_roce'] = None
        
        # 6. FCF质量 (fcf_quality) - 始终用最新年度数据，最低1年
        try:
            operating_cash_flow = latest_data.get('Net Cash Flow from Operating Activities', 0) or 0
            capex = abs(latest_data.get('Capital Expenditures - Total', 0) or 0)
            net_income = latest_data.get('Net Income - Basic - including Extraordinary Items Applicable to Common - Total', 0) or 0
            
            if net_income > 0:
                fcf = operating_cash_flow - capex
                fcf_ratio = fcf / net_income
                
                if fcf_ratio > 1.2:
                    factors['fcf_quality'] = 3  # 优秀
                elif fcf_ratio > 0.8:
                    factors['fcf_quality'] = 2  # 良好
                elif fcf_ratio > 0:
                    factors['fcf_quality'] = 1  # 一般
                else:
                    factors['fcf_quality'] = 0  # 差
            else:
                factors['fcf_quality'] = 0
        except:
            factors['fcf_quality'] = 0
        
        # 7. 动态安全边际 (dynamic_safety_margin) - 始终用最新年度数据，依赖因子1、4均非缺失
        try:
            tech_premium = factors.get('tech_premium')
            ecosystem_cash = factors.get('ecosystem_cash_ratio')
            
            if tech_premium is not None and ecosystem_cash is not None:
                # 基于技术溢价和现金比率的综合评分
                if tech_premium > 10 and ecosystem_cash > 0.1:
                    factors['dynamic_safety_margin'] = 10  # 最高分
                elif tech_premium > 5 and ecosystem_cash > 0.05:
                    factors['dynamic_safety_margin'] = 7   # 高分
                elif tech_premium > 2 and ecosystem_cash > 0.02:
                    factors['dynamic_safety_margin'] = 4   # 中等
                else:
                    factors['dynamic_safety_margin'] = 1   # 低分
            else:
                factors['dynamic_safety_margin'] = None
        except:
            factors['dynamic_safety_margin'] = None
        
                 # 8. 收入增长连续性 (revenue_growth_continuity) - 动态时间窗口
         try:
             if available_listing_years >= 3:
                 # 上市≥3年：近3年（t-2至t年）
                 recent_years = available_data.tail(3)
                growth_positive = 0
                for i in range(1, len(recent_years)):
                    prev_rev = recent_years.iloc[i-1].get('Revenue from Business Activities - Total', 0) or 0
                    curr_rev = recent_years.iloc[i].get('Revenue from Business Activities - Total', 0) or 0
                    if prev_rev > 0 and curr_rev > prev_rev:
                        growth_positive += 1
                
                if growth_positive >= 2:
                    factors['revenue_growth_continuity'] = 1
                else:
                    factors['revenue_growth_continuity'] = 0
                         elif available_listing_years == 2:
                 # 上市2年：近2年（t-1至t年）
                 if prev_data is not None:
                    prev_rev = prev_data.get('Revenue from Business Activities - Total', 0) or 0
                    curr_rev = latest_data.get('Revenue from Business Activities - Total', 0) or 0
                    if prev_rev > 0 and curr_rev > prev_rev:
                        factors['revenue_growth_continuity'] = 1
                    else:
                        factors['revenue_growth_continuity'] = 0
                else:
                    factors['revenue_growth_continuity'] = 0
            else:
                # 上市<2年：因子缺失
                factors['revenue_growth_continuity'] = None
        except:
            factors['revenue_growth_continuity'] = None
        
        # 9. 有效税率改善 (effective_tax_rate_improvement) - 始终用最新年度数据，需税前利润>0
        try:
            if prev_data is not None:
                prev_tax = prev_data.get('Income Taxes', 0) or 0
                prev_income = prev_data.get('Income before Taxes', 0) or 0
                curr_tax = latest_data.get('Income Taxes', 0) or 0
                curr_income = latest_data.get('Income before Taxes', 0) or 0
                
                if prev_income > 0 and curr_income > 0:
                    prev_rate = prev_tax / prev_income
                    curr_rate = curr_tax / curr_income
                    if curr_rate < prev_rate:
                        factors['effective_tax_rate_improvement'] = 1
                    else:
                        factors['effective_tax_rate_improvement'] = 0
                else:
                    factors['effective_tax_rate_improvement'] = 0
            else:
                factors['effective_tax_rate_improvement'] = 0
        except:
            factors['effective_tax_rate_improvement'] = 0
        
        # 10. 财务健康 (financial_health) - 始终用最新年度数据，最低1年
        try:
            debt = latest_data.get('Debt - Total', 0) or 0
            ebitda = latest_data.get('Earnings before Interest, Taxes, Depreciation & Amortization (EBITDA)', 0) or 0
            cash = latest_data.get('Cash & Cash Equivalents - Total', 0) or 0
            total_liabilities = latest_data.get('Total Liabilities', 0) or 0
            
            # 债务EBITDA比率
            debt_ebitda_ratio = debt / ebitda if ebitda > 0 else float('inf')
            
            # 现金负债比率
            cash_liability_ratio = cash / total_liabilities if total_liabilities > 0 else 0
            
            # 综合评分
            if debt_ebitda_ratio < 3 and cash_liability_ratio > 0.15:
                factors['financial_health'] = 3  # 优秀
            elif debt_ebitda_ratio < 5 and cash_liability_ratio > 0.1:
                factors['financial_health'] = 2  # 良好
            elif debt_ebitda_ratio < 8:
                factors['financial_health'] = 1  # 一般
            else:
                factors['financial_health'] = 0  # 差
        except:
            factors['financial_health'] = 0
        
        # 11. 估值泡沫信号 (valuation_bubble_signal) - 始终用最新季度数据，最低1年
        try:
            # 这里需要实现估值泡沫检测算法
            # 暂时使用简化的P/E和P/B比率判断
            net_income = latest_data.get('Net Income - Basic - including Extraordinary Items Applicable to Common - Total', 0) or 0
            equity = latest_data.get('Common Equity - Total', 0) or 0
            shares = latest_data.get('Common Shares - Outstanding - Total', 0) or 0
            
            if net_income > 0 and shares > 0:
                eps = net_income / shares
                # 假设股价为100（需要从市场数据获取）
                assumed_price = 100
                pe_ratio = assumed_price / eps if eps > 0 else float('inf')
                
                if pe_ratio > 50:
                    factors['valuation_bubble_signal'] = 1  # 泡沫信号
                else:
                    factors['valuation_bubble_signal'] = 0  # 正常
            else:
                factors['valuation_bubble_signal'] = 0
        except:
            factors['valuation_bubble_signal'] = 0
        
        # 12. 研发强度 (rd_intensity) - 始终用最新年度数据，最低1年
        try:
            rd_expense = latest_data.get('Research & Development Expense', 0) or 0
            rd_supplemental = latest_data.get('Research & Development Expense - Supplemental', 0) or 0
            total_rd = rd_expense + rd_supplemental
            revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
            
            if revenue > 0:
                factors['rd_intensity'] = (total_rd / revenue) * 100
            else:
                factors['rd_intensity'] = None
        except:
            factors['rd_intensity'] = None
        
                 # 13. ROCE稳定性 (roce_stability) - 动态时间窗口
         try:
             if available_listing_years >= 5:
                 # 上市≥5年：近5年（t-4至t年）
                 recent_years = available_data.tail(5)
             elif available_listing_years >= 3:
                 # 上市3-4年：近3-4年（实际可得年限）
                 recent_years = available_data.tail(available_listing_years)
            else:
                # 上市<3年：因子缺失
                factors['roce_stability'] = None
                continue
            
            roce_values = []
            for _, year_data in recent_years.iterrows():
                ebit = year_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
                equity = year_data.get('Common Equity - Total', 0) or 0
                if equity > 0:
                    roce = ebit / equity
                    roce_values.append(roce)
            
            if len(roce_values) >= 2:
                roce_mean = np.mean(roce_values)
                roce_std = np.std(roce_values)
                # 使用变异系数（标准差/均值）
                if roce_mean > 0:
                    factors['roce_stability'] = roce_std / roce_mean
                else:
                    factors['roce_stability'] = None
            else:
                factors['roce_stability'] = None
        except:
            factors['roce_stability'] = None
        
                 # 14. 营收同比增长 (revenue_yoy) - 动态时间窗口
         try:
             if available_listing_years >= 2 and prev_data is not None:
                prev_revenue = prev_data.get('Revenue from Business Activities - Total', 0) or 0
                current_revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
                
                if prev_revenue > 0:
                    factors['revenue_yoy'] = ((current_revenue - prev_revenue) / prev_revenue) * 100
                else:
                    factors['revenue_yoy'] = None
            else:
                factors['revenue_yoy'] = None
        except:
            factors['revenue_yoy'] = None
        
                 # 15. 营收复合增长率 (revenue_cagr) - 动态时间窗口
         try:
             if available_listing_years >= 5:
                 # 上市≥5年：5年（t-4至t年）
                 years = 5
                 past_data = available_data.iloc[-5]
             elif available_listing_years >= 2:
                 # 上市2-4年：实际年限
                 years = available_listing_years
                 past_data = available_data.iloc[-available_listing_years]
            else:
                # 上市<2年：因子缺失
                factors['revenue_cagr'] = None
                continue
            
            current_revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
            past_revenue = past_data.get('Revenue from Business Activities - Total', 0) or 0
            
            if past_revenue > 0 and current_revenue > 0:
                cagr = ((current_revenue / past_revenue) ** (1/years) - 1) * 100
                factors['revenue_cagr'] = cagr
            else:
                factors['revenue_cagr'] = None
        except:
            factors['revenue_cagr'] = None
        
                 # 16. 净利润同比增长 (net_income_yoy) - 动态时间窗口
         try:
             if available_listing_years >= 2 and prev_data is not None:
                prev_net_income = prev_data.get('Net Income - Basic - including Extraordinary Items Applicable to Common - Total', 0) or 0
                current_net_income = latest_data.get('Net Income - Basic - including Extraordinary Items Applicable to Common - Total', 0) or 0
                
                if prev_net_income > 0:
                    factors['net_income_yoy'] = ((current_net_income - prev_net_income) / prev_net_income) * 100
                else:
                    factors['net_income_yoy'] = None
            else:
                factors['net_income_yoy'] = None
        except:
            factors['net_income_yoy'] = None
        
        # 17. 利润率 (profit_revenue_ratio) - 依赖因子14、16非缺失
        try:
            revenue_yoy = factors.get('revenue_yoy')
            net_income_yoy = factors.get('net_income_yoy')
            
            if revenue_yoy is not None and net_income_yoy is not None:
                net_income = latest_data.get('Net Income - Basic - including Extraordinary Items Applicable to Common - Total', 0) or 0
                revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
                
                if revenue > 0:
                    factors['profit_revenue_ratio'] = (net_income / revenue) * 100
                else:
                    factors['profit_revenue_ratio'] = None
            else:
                factors['profit_revenue_ratio'] = None
        except:
            factors['profit_revenue_ratio'] = None
        
        # 18. 每股自由现金流 (fcf_per_share) - 始终用最新年度数据，最低1年
        try:
            operating_cash_flow = latest_data.get('Net Cash Flow from Operating Activities', 0) or 0
            capex = abs(latest_data.get('Capital Expenditures - Total', 0) or 0)
            shares = latest_data.get('Common Shares - Outstanding - Total', 0) or 0
            
            if shares > 0:
                fcf = operating_cash_flow - capex
                factors['fcf_per_share'] = fcf / shares
            else:
                factors['fcf_per_share'] = None
        except:
            factors['fcf_per_share'] = None
        
                 # 19. 自由现金流复合增长率 (fcf_cagr) - 同15因子（营收CAGR）
         try:
             if available_listing_years >= 5:
                 years = 5
                 past_data = available_data.iloc[-5]
             elif available_listing_years >= 2:
                 years = available_listing_years
                 past_data = available_data.iloc[-available_listing_years]
            else:
                factors['fcf_cagr'] = None
                continue
            
            # 计算当前FCF
            curr_operating_cash = latest_data.get('Net Cash Flow from Operating Activities', 0) or 0
            curr_capex = abs(latest_data.get('Capital Expenditures - Total', 0) or 0)
            current_fcf = curr_operating_cash - curr_capex
            
            # 计算过去的FCF
            past_operating_cash = past_data.get('Net Cash Flow from Operating Activities', 0) or 0
            past_capex = abs(past_data.get('Capital Expenditures - Total', 0) or 0)
            past_fcf = past_operating_cash - past_capex
            
            if past_fcf > 0 and current_fcf > 0:
                cagr = ((current_fcf / past_fcf) ** (1/years) - 1) * 100
                factors['fcf_cagr'] = cagr
            else:
                factors['fcf_cagr'] = None
        except:
            factors['fcf_cagr'] = None
        
        # 20. 自由现金流净利润比率 (fcf_net_income_ratio) - 始终用最新年度数据，最低1年
        try:
            operating_cash_flow = latest_data.get('Net Cash Flow from Operating Activities', 0) or 0
            capex = abs(latest_data.get('Capital Expenditures - Total', 0) or 0)
            net_income = latest_data.get('Net Income - Basic - including Extraordinary Items Applicable to Common - Total', 0) or 0
            
            if net_income > 0:
                fcf = operating_cash_flow - capex
                factors['fcf_net_income_ratio'] = (fcf / net_income) * 100
            else:
                factors['fcf_net_income_ratio'] = None
        except:
            factors['fcf_net_income_ratio'] = None
        
        # 21. 营业利润率 (operating_margin) - 始终用最新年度数据，最低1年
        try:
            operating_profit = latest_data.get('Operating Profit before Non-Recurring Income/Expense', 0) or 0
            revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
            
            if revenue > 0:
                factors['operating_margin'] = (operating_profit / revenue) * 100
            else:
                factors['operating_margin'] = None
        except:
            factors['operating_margin'] = None
        
                 # 22. 营业利润率标准差 (operating_margin_std) - 动态时间窗口
         try:
             if available_listing_years >= 4:
                 # 上市≥4年：近4年（t-3至t年）
                 recent_years = available_data.tail(4)
             elif available_listing_years >= 2:
                 # 上市2-3年：近2-3年（实际可得年限）
                 recent_years = available_data.tail(available_listing_years)
            else:
                # 上市<2年：因子缺失
                factors['operating_margin_std'] = None
                continue
            
            margins = []
            for _, year_data in recent_years.iterrows():
                operating_profit = year_data.get('Operating Profit before Non-Recurring Income/Expense', 0) or 0
                revenue = year_data.get('Revenue from Business Activities - Total', 0) or 0
                if revenue > 0:
                    margin = (operating_profit / revenue) * 100
                    margins.append(margin)
            
            if len(margins) >= 2:
                margin_mean = np.mean(margins)
                margin_std = np.std(margins)
                # 使用变异系数
                if margin_mean > 0:
                    factors['operating_margin_std'] = margin_std / margin_mean
                else:
                    factors['operating_margin_std'] = None
            else:
                factors['operating_margin_std'] = None
        except:
            factors['operating_margin_std'] = None
        
        # 23. ROIC (roic) - 始终用最新年度数据，最低1年
        try:
            ebit = latest_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
            tax_expense = latest_data.get('Income Taxes', 0) or 0
            equity = latest_data.get('Common Equity - Total', 0) or 0
            debt = latest_data.get('Debt - Total', 0) or 0
            
            if equity > 0:
                # 使用税前利润计算有效税率
                pretax_income = latest_data.get('Income before Taxes', 0) or 0
                effective_tax_rate = tax_expense / pretax_income if pretax_income > 0 else 0.25
                nopat = ebit * (1 - effective_tax_rate)
                invested_capital = equity + debt
                
                if invested_capital > 0:
                    factors['roic'] = (nopat / invested_capital) * 100
                else:
                    factors['roic'] = None
            else:
                factors['roic'] = None
        except:
            factors['roic'] = None
        
                 # 24. ROIC复合增长率 (roic_cagr) - 同15因子（营收CAGR）
         try:
             if available_listing_years >= 5:
                 years = 5
                 past_data = available_data.iloc[-5]
             elif available_listing_years >= 2:
                 years = available_listing_years
                 past_data = available_data.iloc[-available_listing_years]
            else:
                factors['roic_cagr'] = None
                continue
            
            current_roic = factors.get('roic', 0) or 0
            
            # 计算过去的ROIC
            past_ebit = past_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
            past_tax = past_data.get('Income Taxes', 0) or 0
            past_equity = past_data.get('Common Equity - Total', 0) or 0
            past_debt = past_data.get('Debt - Total', 0) or 0
            
            if past_equity > 0:
                past_pretax = past_data.get('Income before Taxes', 0) or 0
                past_effective_tax_rate = past_tax / past_pretax if past_pretax > 0 else 0.25
                past_nopat = past_ebit * (1 - past_effective_tax_rate)
                past_invested_capital = past_equity + past_debt
                
                if past_invested_capital > 0:
                    past_roic = (past_nopat / past_invested_capital) * 100
                    
                    if past_roic > 0 and current_roic > 0:
                        cagr = ((current_roic / past_roic) ** (1/years) - 1) * 100
                        factors['roic_cagr'] = cagr
                    else:
                        factors['roic_cagr'] = None
                else:
                    factors['roic_cagr'] = None
            else:
                factors['roic_cagr'] = None
        except:
            factors['roic_cagr'] = None
        
        # 25. 有效税率 (effective_tax_rate) - 始终用最新年度数据，需EBIT>0
        try:
            tax_expense = latest_data.get('Income Taxes', 0) or 0
            pretax_income = latest_data.get('Income before Taxes', 0) or 0
            
            if pretax_income > 0:
                factors['effective_tax_rate'] = (tax_expense / pretax_income) * 100
            else:
                factors['effective_tax_rate'] = None
        except:
            factors['effective_tax_rate'] = None
        
                 # 26. 有效税率标准差 (effective_tax_rate_std) - 同22因子（营业利润率稳定性）
         try:
             if available_listing_years >= 4:
                 recent_years = available_data.tail(4)
             elif available_listing_years >= 2:
                 recent_years = available_data.tail(available_listing_years)
            else:
                factors['effective_tax_rate_std'] = None
                continue
            
            tax_rates = []
            for _, year_data in recent_years.iterrows():
                tax_expense = year_data.get('Income Taxes', 0) or 0
                pretax_income = year_data.get('Income before Taxes', 0) or 0
                if pretax_income > 0:
                    tax_rate = (tax_expense / pretax_income) * 100
                    tax_rates.append(tax_rate)
            
            if len(tax_rates) >= 2:
                tax_rate_mean = np.mean(tax_rates)
                tax_rate_std = np.std(tax_rates)
                # 使用变异系数
                if tax_rate_mean > 0:
                    factors['effective_tax_rate_std'] = tax_rate_std / tax_rate_mean
                else:
                    factors['effective_tax_rate_std'] = None
            else:
                factors['effective_tax_rate_std'] = None
        except:
            factors['effective_tax_rate_std'] = None
            
    except Exception as e:
        print(f"计算因子时出错: {e}")
    
    return factors

def calculate_rolling_factors(df_annual, df_listing):
    """滚动计算历史因子值"""
    print("\n7. 滚动计算历史因子值...")
    
    all_factor_data = []
    
    # 获取所有有效的effective_date
    all_dates = sorted(df_annual['effective_date'].unique())
    print(f"  总共有 {len(all_dates)} 个不同的有效日期")
    
    for stock_symbol in df_annual['stock_symbol'].unique():
        stock_data = df_annual[df_annual['stock_symbol'] == stock_symbol].copy()
        stock_data = stock_data.sort_values('fiscal_year')
        
        if len(stock_data) < 1:
            continue
        
        # 获取上市年限
        listing_info = df_listing[df_listing['stock_symbol'] == stock_symbol]
        listing_years = listing_info['listing_years'].iloc[0] if len(listing_info) > 0 else 1
        
        # 对每个有效日期计算因子
        for target_date in all_dates:
            # 计算该日期的因子
            factors = calculate_26_factors_for_date(stock_data, target_date, listing_years)
            
            if factors:  # 如果有因子值
                factors['stock_symbol'] = stock_symbol
                factors['effective_date'] = target_date
                factors['listing_years'] = listing_years
                all_factor_data.append(factors)
    
    df_factors = pd.DataFrame(all_factor_data)
    print(f"  滚动计算完成的因子记录数: {len(df_factors):,}")
    return df_factors

def analyze_factor_coverage(df_factors):
    """分析因子覆盖率"""
    print("\n8. 分析因子覆盖率...")
    
    total_records = len(df_factors)
    unique_stocks = df_factors['stock_symbol'].nunique()
    unique_dates = df_factors['effective_date'].nunique()
    
    coverage_stats = {}
    
    # 第一组13个因子（技术相关）
    factor_columns_group1 = [
        'tech_premium', 'tech_gap_warning', 'patent_density', 'ecosystem_cash_ratio',
        'adjusted_roce', 'fcf_quality', 'dynamic_safety_margin', 'revenue_growth_continuity',
        'effective_tax_rate_improvement', 'financial_health', 'valuation_bubble_signal',
        'rd_intensity', 'roce_stability'
    ]
    
    # 第二组13个因子（财务相关）
    factor_columns_group2 = [
        'revenue_yoy', 'revenue_cagr', 'net_income_yoy', 'profit_revenue_ratio',
        'fcf_per_share', 'fcf_cagr', 'fcf_net_income_ratio',
        'operating_margin', 'operating_margin_std',
        'roic', 'roic_cagr',
        'effective_tax_rate', 'effective_tax_rate_std'
    ]
    
    # 合并所有因子
    all_factor_columns = factor_columns_group1 + factor_columns_group2
    
    print(f"\n=== 因子覆盖率分析（总共{len(all_factor_columns)}个因子）===")
    print(f"总记录数: {total_records:,}")
    print(f"唯一股票数: {unique_stocks:,}")
    print(f"唯一日期数: {unique_dates:,}")
    
    for factor in all_factor_columns:
        if factor in df_factors.columns:
            coverage = df_factors[factor].notna().sum()
            coverage_pct = (coverage / total_records) * 100
            coverage_stats[factor] = {
                'coverage': coverage,
                'coverage_pct': coverage_pct
            }
            print(f"  {factor}: {coverage} 条记录 ({coverage_pct:.1f}%)")
        else:
            print(f"  {factor}: 未找到该因子列")
    
    return coverage_stats

def save_results(df_factors, coverage_stats):
    """保存结果"""
    print("\n9. 保存结果...")
    
    # 保存因子结果
    output_file = 'mclean_factors_corrected_final_results.csv'
    df_factors.to_csv(output_file, index=False, encoding='utf-8')
    print(f"  因子结果已保存到: {output_file}")
    
    # 保存覆盖率统计
    coverage_file = 'mclean_factors_corrected_final_coverage.csv'
    df_coverage = pd.DataFrame.from_dict(coverage_stats, orient='index')
    df_coverage.to_csv(coverage_file, encoding='utf-8')
    print(f"  覆盖率统计已保存到: {coverage_file}")
    
    # 生成报告
    report_file = 'mclean_factors_corrected_final_report.txt'
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("=== Colin McLean 26因子实现报告（修正最终版）===\n")
        f.write(f"计算时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总记录数: {len(df_factors):,}\n")
        f.write(f"唯一股票数: {df_factors['stock_symbol'].nunique():,}\n")
        f.write(f"唯一日期数: {df_factors['effective_date'].nunique():,}\n\n")
        
        f.write("因子覆盖率统计:\n")
        for factor, stats in coverage_stats.items():
            f.write(f"  {factor}: {stats['coverage']} 条记录 ({stats['coverage_pct']:.1f}%)\n")
    
    print(f"  分析报告已保存到: {report_file}")

def main():
    """主函数"""
    try:
        # 获取数据
        df_listing = get_stock_listing_years()
        df_financial = get_financial_data()
        df_market = get_market_data()
        
        # 处理数据
        df_pivot = process_financial_data(df_financial)
        df_annual = calculate_annual_data(df_pivot)
        
        # 分析年报数据缺失情况
        missing_stocks, year_distribution = analyze_missing_annual_data(df_financial, df_annual)
        
        # 保存缺失数据统计
        missing_stats_file = 'missing_annual_data_stats.txt'
        with open(missing_stats_file, 'w', encoding='utf-8') as f:
            f.write("=== 年报数据缺失统计 ===\n")
            f.write(f"总股票数: {len(df_financial['stock_symbol'].unique()):,}\n")
            f.write(f"有年报数据的股票数: {len(df_annual['stock_symbol'].unique()):,}\n")
            f.write(f"缺失年报数据的股票数: {len(missing_stocks):,}\n\n")
            
            if len(missing_stocks) > 0:
                f.write("缺失年报数据的股票列表:\n")
                for stock in sorted(missing_stocks):
                    f.write(f"  {stock}\n")
            
            f.write(f"\n年报数据年份分布:\n")
            f.write(f"平均年份数: {year_distribution['year_count'].mean():.1f}\n")
            f.write(f"最少年份数: {year_distribution['year_count'].min()}\n")
            f.write(f"最多年份数: {year_distribution['year_count'].max()}\n\n")
            
            year_count_stats = year_distribution['year_count'].value_counts().sort_index()
            f.write("年份数分布:\n")
            for year_count, stock_count in year_count_stats.items():
                f.write(f"  {year_count}年: {stock_count}只股票\n")
        
        print(f"  缺失数据统计已保存到: {missing_stats_file}")
        
        # 滚动计算历史因子值
        df_factors = calculate_rolling_factors(df_annual, df_listing)
        
        # 分析覆盖率
        coverage_stats = analyze_factor_coverage(df_factors)
        
        # 保存结果
        save_results(df_factors, coverage_stats)
        
        print("\n=== Colin McLean 26因子计算完成 ===")
        
    except Exception as e:
        print(f"执行过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
