from clickhouse_driver import Client
import pandas as pd

def connect_to_ap_research():
    """连接到ap_research数据库"""
    client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')
    print("✅ 连接ap_research数据库成功")
    return client

def debug_zero_coverage_factors():
    """调试0%覆盖率的因子"""
    
    print("🔍 调试0%覆盖率的因子")
    print("=" * 80)
    
    client = connect_to_ap_research()
    
    # 获取NVDA的数据作为示例
    query = """
    SELECT 
        item_name,
        value,
        statement_type
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = 'NVDA'
      AND statement_type IN ('income_statement', 'balance_sheet_history', 'cash_flow')
      AND financial_period_absolute = 'FY2024'
    ORDER BY statement_type, item_name
    """
    
    result = client.execute(query)
    df = pd.DataFrame(result, columns=['item_name', 'value', 'statement_type'])
    
    print(f"📊 NVDA FY2024数据总数: {len(df)}")
    print()
    
    # 1. 检查 ecosystem_cash_ratio 相关字段
    print("1. 🏦 ecosystem_cash_ratio 相关字段检查:")
    print("-" * 60)
    
    cash_keywords = ['cash', 'Cash', 'CASH']
    asset_keywords = ['asset', 'Asset', 'ASSET', 'total', 'Total', 'TOTAL']
    
    print("💰 现金相关字段:")
    cash_fields = df[df['item_name'].str.contains('|'.join(cash_keywords), na=False)]
    for _, row in cash_fields.iterrows():
        print(f"   {row['item_name']:<50} = {row['value']:>15,.0f}")
    
    print("\n📈 资产相关字段:")
    asset_fields = df[df['item_name'].str.contains('|'.join(asset_keywords), na=False)]
    for _, row in asset_fields.head(10).iterrows():  # 只显示前10个
        print(f"   {row['item_name']:<50} = {row['value']:>15,.0f}")
    
    print()
    
    # 2. 检查 financial_health 相关字段
    print("2. 💊 financial_health 相关字段检查:")
    print("-" * 60)
    
    current_keywords = ['current', 'Current', 'CURRENT']
    liability_keywords = ['liabilit', 'Liabilit', 'LIABILIT']
    
    print("📊 流动资产相关字段:")
    current_asset_fields = df[df['item_name'].str.contains('|'.join(current_keywords), na=False)]
    for _, row in current_asset_fields.iterrows():
        print(f"   {row['item_name']:<50} = {row['value']:>15,.0f}")
    
    print("\n📉 流动负债相关字段:")
    current_liability_fields = df[df['item_name'].str.contains('|'.join(liability_keywords), na=False)]
    for _, row in current_liability_fields.head(10).iterrows():
        print(f"   {row['item_name']:<50} = {row['value']:>15,.0f}")
    
    print()
    
    # 3. 检查 fcf_per_share 相关字段
    print("3. 💸 fcf_per_share 相关字段检查:")
    print("-" * 60)
    
    fcf_keywords = ['free', 'Free', 'FREE', 'cash flow', 'Cash Flow']
    share_keywords = ['share', 'Share', 'SHARE', 'outstanding', 'Outstanding']
    
    print("💰 自由现金流相关字段:")
    fcf_fields = df[df['item_name'].str.contains('|'.join(fcf_keywords), na=False)]
    for _, row in fcf_fields.iterrows():
        print(f"   {row['item_name']:<50} = {row['value']:>15,.0f}")
    
    print("\n📊 股份相关字段:")
    share_fields = df[df['item_name'].str.contains('|'.join(share_keywords), na=False)]
    for _, row in share_fields.iterrows():
        print(f"   {row['item_name']:<50} = {row['value']:>15,.0f}")
    
    print()
    
    # 4. 检查 valuation_bubble_signal 相关字段
    print("4. 📈 valuation_bubble_signal 相关字段检查:")
    print("-" * 60)
    
    print("💰 净利润字段:")
    ni_fields = df[df['item_name'].str.contains('Net Income|net income', na=False)]
    for _, row in ni_fields.iterrows():
        print(f"   {row['item_name']:<50} = {row['value']:>15,.0f}")
    
    print("\n📊 股份字段:")
    for _, row in share_fields.iterrows():
        print(f"   {row['item_name']:<50} = {row['value']:>15,.0f}")
    
    print()
    
    # 5. 检查价格数据
    print("5. 💲 价格数据检查:")
    print("-" * 60)
    
    price_query = """
    SELECT 
        stock_symbol,
        trade_date,
        close,
        volume
    FROM priority_quality_stock_hfq
    WHERE stock_symbol = 'NVDA'
    ORDER BY trade_date DESC
    LIMIT 5
    """
    
    price_result = client.execute(price_query)
    if price_result:
        print("📊 最新价格数据:")
        for row in price_result:
            print(f"   {row[0]} | {row[1]} | 收盘价: ${row[2]:,.2f} | 成交量: {row[3]:,}")
    else:
        print("   ❌ 无价格数据")
    
    print()
    
    # 6. 建议的字段映射修复
    print("6. 💡 建议的字段映射修复:")
    print("-" * 60)
    
    print("🔧 可能的正确字段名:")
    
    # 搜索包含关键词的所有字段
    all_items = df['item_name'].unique()
    
    print("\n💰 现金相关字段建议:")
    cash_suggestions = [item for item in all_items if any(keyword.lower() in item.lower() for keyword in ['cash', 'equivalent'])]
    for item in cash_suggestions[:5]:
        print(f"   {item}")
    
    print("\n📈 总资产字段建议:")
    asset_suggestions = [item for item in all_items if any(keyword.lower() in item.lower() for keyword in ['total', 'asset']) and 'total' in item.lower() and 'asset' in item.lower()]
    for item in asset_suggestions[:3]:
        print(f"   {item}")
    
    print("\n📊 流动资产/负债字段建议:")
    current_suggestions = [item for item in all_items if 'current' in item.lower()]
    for item in current_suggestions[:5]:
        print(f"   {item}")
    
    print("\n💸 自由现金流字段建议:")
    fcf_suggestions = [item for item in all_items if 'free' in item.lower() and 'cash' in item.lower()]
    for item in fcf_suggestions[:3]:
        print(f"   {item}")

if __name__ == "__main__":
    debug_zero_coverage_factors()

