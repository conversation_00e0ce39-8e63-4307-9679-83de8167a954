from clickhouse_driver import Client
import pandas as pd

def connect_to_ap_research():
    """连接到ap_research数据库"""
    client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')
    print("✅ 连接ap_research数据库成功")
    return client

def analyze_all_field_mappings():
    """全面分析所有因子涉及的字段映射"""
    
    print("🔍 全面分析所有因子字段映射")
    print("=" * 100)
    
    client = connect_to_ap_research()
    
    # 获取所有可用的财报科目
    query = """
    SELECT DISTINCT item_name, statement_type, COUNT(*) as frequency
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE financial_period_absolute REGEXP '^FY[0-9]{4}$'
    GROUP BY item_name, statement_type
    ORDER BY frequency DESC
    """
    
    result = client.execute(query)
    all_items_df = pd.DataFrame(result, columns=['item_name', 'statement_type', 'frequency'])
    
    print(f"📊 总共有 {len(all_items_df)} 个不同的财报科目")
    print()
    
    # 定义所有因子需要的字段及其搜索关键词
    factor_fields = {
        # 技术相关因子
        'R&D费用': {
            'keywords': ['research', 'development', 'R&D'],
            'current_mapping': ['Research & Development Expense', 'Research & Development Expense - Supplemental'],
            'statement_type': 'income_statement'
        },
        
        '收入': {
            'keywords': ['revenue', 'sales', 'income from business'],
            'current_mapping': ['Revenue from Business Activities - Total'],
            'statement_type': 'income_statement'
        },
        
        '无形资产': {
            'keywords': ['intangible', 'patent', 'intellectual'],
            'current_mapping': ['Intangible Assets - Total'],
            'statement_type': 'balance_sheet_history'
        },
        
        # 现金相关
        '现金及现金等价物': {
            'keywords': ['cash', 'cash equivalent', 'short term investment'],
            'current_mapping': ['Cash & Cash Equivalents', 'Cash & Cash Equivalents - Total', 'Cash & Short Term Investments - Total'],
            'statement_type': 'balance_sheet_history'
        },
        
        '总资产': {
            'keywords': ['total assets', 'assets total'],
            'current_mapping': ['Assets - Total', 'Total Assets'],
            'statement_type': 'balance_sheet_history'
        },
        
        # 股东权益
        '股东权益': {
            'keywords': ['shareholders equity', 'common equity', 'stockholders equity'],
            'current_mapping': ['Shareholders\' Equity - Attributable to Parent Shareholders - Total', 'Common Equity - Total', 'Common Equity Attributable to Parent Shareholders'],
            'statement_type': 'balance_sheet_history'
        },
        
        # 净利润
        '净利润': {
            'keywords': ['net income', 'net earnings', 'bottom line'],
            'current_mapping': ['Normalized Net Income - Bottom Line'],
            'statement_type': 'income_statement'
        },
        
        # 自由现金流
        '自由现金流': {
            'keywords': ['free cash flow', 'operating cash flow'],
            'current_mapping': ['Free Cash Flow'],
            'statement_type': 'cash_flow'
        },
        
        # 流动资产/负债
        '流动资产': {
            'keywords': ['current assets', 'current asset'],
            'current_mapping': ['Current Assets - Total', 'Total Current Assets'],
            'statement_type': 'balance_sheet_history'
        },
        
        '流动负债': {
            'keywords': ['current liabilities', 'current liability'],
            'current_mapping': ['Current Liabilities - Total', 'Total Current Liabilities'],
            'statement_type': 'balance_sheet_history'
        },
        
        # 股份数
        '流通股数': {
            'keywords': ['shares outstanding', 'common shares', 'diluted shares', 'shares used'],
            'current_mapping': ['Shares Outstanding - Diluted Average', 'Shares used to calculate Diluted EPS - Total', 'Common Shares - Outstanding - Total'],
            'statement_type': 'balance_sheet_history'
        },
        
        # 债务
        '总债务': {
            'keywords': ['debt total', 'total debt', 'debt'],
            'current_mapping': ['Debt - Total'],
            'statement_type': 'balance_sheet_history'
        },
        
        # 营业收入
        '营业收入': {
            'keywords': ['operating income', 'operating profit'],
            'current_mapping': ['Operating Income'],
            'statement_type': 'income_statement'
        },
        
        # 税前利润
        'EBIT': {
            'keywords': ['earnings before interest', 'EBIT', 'operating income'],
            'current_mapping': ['Earnings before Interest & Taxes (EBIT)'],
            'statement_type': 'income_statement'
        },
        
        # 所得税
        '所得税': {
            'keywords': ['income tax', 'tax expense', 'provision for income tax'],
            'current_mapping': ['Income Taxes'],
            'statement_type': 'income_statement'
        }
    }
    
    # 分析每个字段的映射情况
    for field_name, field_info in factor_fields.items():
        print(f"🔍 分析字段: {field_name}")
        print("-" * 80)
        
        keywords = field_info['keywords']
        current_mapping = field_info['current_mapping']
        statement_type = field_info['statement_type']
        
        # 搜索匹配的字段
        matching_items = all_items_df[
            (all_items_df['item_name'].str.contains('|'.join(keywords), case=False, na=False)) &
            (all_items_df['statement_type'] == statement_type)
        ].sort_values('frequency', ascending=False)
        
        print(f"📊 当前映射: {current_mapping}")
        print(f"🔍 搜索关键词: {keywords}")
        print(f"📋 报表类型: {statement_type}")
        print()
        
        if not matching_items.empty:
            print(f"✅ 找到 {len(matching_items)} 个匹配项:")
            for i, (_, row) in enumerate(matching_items.head(10).iterrows()):
                status = "✅ 已映射" if row['item_name'] in current_mapping else "🆕 新发现"
                print(f"   {i+1:2d}. {row['item_name']:<60} (频次: {row['frequency']:>6,}) {status}")
        else:
            print("❌ 未找到匹配项")
        
        print()
        
        # 检查当前映射字段是否存在
        print("🔍 当前映射字段验证:")
        for mapping in current_mapping:
            exists = not all_items_df[
                (all_items_df['item_name'] == mapping) & 
                (all_items_df['statement_type'] == statement_type)
            ].empty
            
            if exists:
                freq = all_items_df[
                    (all_items_df['item_name'] == mapping) & 
                    (all_items_df['statement_type'] == statement_type)
                ]['frequency'].iloc[0]
                print(f"   ✅ {mapping} (频次: {freq:,})")
            else:
                print(f"   ❌ {mapping} - 不存在!")
        
        print()
        print("=" * 100)
        print()
    
    # 生成建议的完整映射
    print("💡 建议的完整字段映射优先级:")
    print("=" * 100)
    
    suggested_mappings = {}
    
    for field_name, field_info in factor_fields.items():
        keywords = field_info['keywords']
        statement_type = field_info['statement_type']
        
        # 搜索匹配的字段
        matching_items = all_items_df[
            (all_items_df['item_name'].str.contains('|'.join(keywords), case=False, na=False)) &
            (all_items_df['statement_type'] == statement_type)
        ].sort_values('frequency', ascending=False)
        
        if not matching_items.empty:
            # 取前5个最频繁的字段作为建议
            top_matches = matching_items.head(5)['item_name'].tolist()
            suggested_mappings[field_name] = top_matches
            
            print(f"🔧 {field_name}:")
            for i, item in enumerate(top_matches):
                priority = f"P{i+1}" if i > 0 else "PRIMARY"
                print(f"   {priority:<8}: '{item}'")
            print()
    
    return suggested_mappings

if __name__ == "__main__":
    mappings = analyze_all_field_mappings()

