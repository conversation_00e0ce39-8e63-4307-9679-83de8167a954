import pandas as pd

# 读取900多只股票列表
stock_list_file = 'stock_performance_2020_2025_cumulative.csv'
stock_df = pd.read_csv(stock_list_file)

# 兼容不同列名，取第一列为股票代码
stock_col = stock_df.columns[0]
stock_list = stock_df[stock_col].astype(str).str.upper().str.replace(r'\..*$', '', regex=True).unique().tolist()

# 读取全市场因子结果
factor_file = 'mclean_factors_combined_results.csv'
factor_df = pd.read_csv(factor_file)

# 股票代码标准化（去后缀）
def normalize_code(code):
    return str(code).split('.')[0].upper()
factor_df['base_code'] = factor_df['stock_code'].apply(normalize_code)

# 只保留在stock_list中的股票
filtered = factor_df[factor_df['base_code'].isin(stock_list)].copy()

# 统计覆盖率
factor_cols = [col for col in filtered.columns if col not in ['stock_code', 'base_code']]
coverage = {}
total = filtered.shape[0]
for col in factor_cols:
    non_null = filtered[col].notnull().sum()
    coverage[col] = {'count': non_null, 'percent': round(non_null/total*100, 2)}

# 输出覆盖率统计
print(f'总股票数: {total}')
print('因子	有值股票数	覆盖率(%)')
for col, stat in coverage.items():
    print(f'{col}\t{stat["count"]}\t{stat["percent"]}')