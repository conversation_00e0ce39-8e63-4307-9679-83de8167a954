#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断为什么只有部分因子被标准化
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from final_complete_24_factors_with_field_mapping import (
    connect_to_ap_research, 
    calculate_complete_24_factors
)

def debug_missing_factors():
    """调试缺失的因子"""
    print("🔍 调试为什么只有部分因子被标准化...")
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        print("❌ 无法连接到数据库")
        return
    
    try:
        test_stock = 'AAPL'
        calculation_date = '2024-01-15'
        
        print(f"📊 测试股票: {test_stock}")
        print(f"📅 计算日期: {calculation_date}")
        
        # 计算原始因子
        factors = calculate_complete_24_factors(client, test_stock, calculation_date)
        
        if not factors:
            print("❌ 无法计算原始因子")
            return
        
        # 完整的24因子列表
        expected_factors = [
            'tech_premium', 'tech_gap_warning', 'patent_density', 'ecosystem_cash_ratio',
            'adjusted_roce', 'fcf_quality', 'dynamic_safety_margin', 'revenue_growth_continuity',
            'effective_tax_rate_improvement', 'financial_health', 'valuation_bubble_signal',
            'roce_stability', 'revenue_yoy', 'revenue_cagr', 'net_income_yoy', 'profit_revenue_ratio',
            'fcf_per_share', 'fcf_cagr', 'operating_margin', 'operating_margin_std',
            'roic', 'roic_cagr', 'effective_tax_rate', 'effective_tax_rate_std'
        ]
        
        print(f"\n📋 完整24因子存在性检查:")
        print("-" * 60)
        
        existing_factors = []
        missing_factors = []
        null_factors = []
        
        for factor in expected_factors:
            if factor in factors:
                if factors[factor] is not None:
                    existing_factors.append(factor)
                    print(f"✅ {factor:<30} = {factors[factor]}")
                else:
                    null_factors.append(factor)
                    print(f"⚠️ {factor:<30} = None")
            else:
                missing_factors.append(factor)
                print(f"❌ {factor:<30} = 不存在")
        
        print(f"\n📊 统计结果:")
        print(f"   存在且有值的因子: {len(existing_factors)}/24")
        print(f"   存在但为None的因子: {len(null_factors)}/24")
        print(f"   完全不存在的因子: {len(missing_factors)}/24")
        
        if missing_factors:
            print(f"\n❌ 完全不存在的因子:")
            for factor in missing_factors:
                print(f"   - {factor}")
        
        if null_factors:
            print(f"\n⚠️ 存在但为None的因子:")
            for factor in null_factors:
                print(f"   - {factor}")
        
        # 检查标准化逻辑中的因子分组
        print(f"\n🔍 检查标准化逻辑中的因子分组:")
        
        factor_groups = {
            'min_max_factors': [
                'tech_premium', 'patent_density', 'ecosystem_cash_ratio', 
                'financial_health', 'profit_revenue_ratio', 'operating_margin',
                'adjusted_roce', 'roic', 'dynamic_safety_margin'
            ],
            'zscore_factors': [
                'revenue_yoy', 'revenue_cagr', 'net_income_yoy', 'fcf_cagr', 'roic_cagr',
                'revenue_growth_continuity', 'operating_margin_std', 'effective_tax_rate_std',
                'roce_stability', 'fcf_per_share'
            ],
            'robust_scaling_factors': [
                'fcf_quality', 'cash_flow_coverage_ratio'
            ],
            'negative_factors': [
                'tech_gap_warning', 'valuation_bubble_signal', 'effective_tax_rate',
                'effective_tax_rate_improvement'
            ]
        }
        
        for group_name, factor_list in factor_groups.items():
            print(f"\n📋 {group_name}:")
            for factor in factor_list:
                if factor in factors:
                    if factors[factor] is not None:
                        print(f"   ✅ {factor} = {factors[factor]}")
                    else:
                        print(f"   ⚠️ {factor} = None (不会被标准化)")
                else:
                    print(f"   ❌ {factor} = 不存在 (不会被标准化)")
        
        # 检查cash_flow_coverage_ratio问题
        print(f"\n🔍 特别检查 cash_flow_coverage_ratio:")
        if 'cash_flow_coverage_ratio' in factors:
            print(f"   存在: {factors['cash_flow_coverage_ratio']}")
        else:
            print(f"   不存在，但标准化逻辑中包含此因子")
            print(f"   这可能是因子名称不匹配的问题")
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            client.disconnect()
            print("🔌 数据库连接已关闭")

if __name__ == "__main__":
    debug_missing_factors()
