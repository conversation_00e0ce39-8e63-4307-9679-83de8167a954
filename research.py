from AlgorithmImports import *
import pandas as pd
from datetime import timedelta
import datetime
import time

# 1. 获取全市场股票列表
qb = QuantBook()
all_symbols = [x for x in qb.Securities.Keys if x.SecurityType == SecurityType.Equity and x.Market == Market.USA]

# 2. 设置时间区间
start = qb.Time - timedelta(days=3*365)
end = qb.Time

# 3. 存储每个条件满足的股票数量
condition_counts = {f'Cond{i+1}': [] for i in range(6)}
monthly_top10 = []

# 4. 遍历每个月
current = start
while current < end:
    qb.SetTime(current)
    # 获取当前所有有财报的股票
    fine = [x for x in qb.Securities.Values if hasattr(x, 'Fundamentals') and x.Fundamentals is not None]
    cond1, cond2, cond3, cond4, cond5, cond6 = [], [], [], [], [], []
    for sec in fine:
        f = sec.Fundamentals.FinancialStatements
        price = sec.Price if hasattr(sec, 'Price') else None
        # A. 每股销售成长率递增
        try:
            income = f.IncomeStatement
            if len(income) >= 3:
                rev0 = income[0].TotalRevenue.OneYear
                rev1 = income[1].TotalRevenue.OneYear
                rev2 = income[2].TotalRevenue.OneYear
                if rev1 > 0 and rev2 > 0:
                    growth0 = (rev0 - rev1) / rev1
                    growth1 = (rev1 - rev2) / rev2
                    if growth0 > growth1 and growth0 > 0:
                        cond1.append(sec.Symbol)
        except: pass
        # B. 最近三年自由现金流都为正
        try:
            cf = f.CashFlowStatement
            if len(cf) >= 3:
                ok = True
                for i in range(3):
                    ocf = cf[i].OperatingCashFlow.OneYear
                    capex = cf[i].CapitalExpenditure.OneYear
                    if ocf is None or capex is None or (ocf - abs(capex)) <= 0:
                        ok = False
                        break
                if ok:
                    cond2.append(sec.Symbol)
        except: pass
        # C. 近四季营业利润率>=10%
        try:
            income = f.IncomeStatement
            if len(income) >= 4:
                ok = True
                for i in range(4):
                    op = income[i].OperatingIncome.OneYear
                    rev = income[i].TotalRevenue.OneYear
                    if op is None or rev is None or rev <= 0 or op/rev < 0.10:
                        ok = False
                        break
                if ok:
                    cond3.append(sec.Symbol)
        except: pass
        # D. 近四季资本报酬率>=10%
        try:
            income = f.IncomeStatement
            balance = f.BalanceSheet
            if len(income) >= 4 and len(balance) >= 4:
                ok = True
                for i in range(4):
                    op = income[i].OperatingIncome.OneYear
                    equity = balance[i].TotalEquity.OneYear
                    debt = balance[i].TotalDebt.OneYear
                    capital = (equity if equity else 0) + (debt if debt else 0)
                    if op is None or capital <= 0 or op/capital < 0.10:
                        ok = False
                        break
                if ok:
                    cond4.append(sec.Symbol)
        except: pass
        # E. 最近年度有效税率>=5%
        try:
            income = f.IncomeStatement
            if len(income) >= 1:
                tax = income[0].TaxProvision.OneYear
                pretax = income[0].PretaxIncome.OneYear
                if tax is not None and pretax is not None and pretax > 0 and tax/pretax >= 0.05:
                    cond5.append(sec.Symbol)
        except: pass
        # F. 经济价值：高估/低估指数<=1
        try:
            income = f.IncomeStatement
            if len(income) >= 1:
                sales_per_share = income[0].TotalRevenuePerShare.OneYear
                op = income[0].OperatingIncome.OneYear
                rev = income[0].TotalRevenue.OneYear
                if price and sales_per_share and op and rev and rev > 0:
                    margin = op / rev
                    index = (price / sales_per_share) * margin
                    if index <= 1:
                        cond6.append(sec.Symbol)
        except: pass
    # 记录每个条件的数量
    condition_counts['Cond1'].append(len(cond1))
    condition_counts['Cond2'].append(len(cond2))
    condition_counts['Cond3'].append(len(cond3))
    condition_counts['Cond4'].append(len(cond4))
    condition_counts['Cond5'].append(len(cond5))
    condition_counts['Cond6'].append(len(cond6))
    # 记录前10只股票
    monthly_top10.append(cond1[:10])
    # 下一个月
    current += pd.DateOffset(months=1)

# 展示统计结果
print('每个条件每月满足股票数量:')
for k, v in condition_counts.items():
    print(k, v)
print('每月前10只股票:')
for i, top10 in enumerate(monthly_top10):
    print(f"Month {i+1}: {top10}") 

## 推荐方案：分批拉取全市场美股财报数据

### 1. 获取全美股Symbol列表
```python
qb = QuantBook()
all_symbols = [x for x in qb.Securities.Keys if x.SecurityType == SecurityType.Equity and x.Market == Market.USA]
```

### 2. 分批拉取（每批500只，防止超时/内存溢出）
```python
from datetime import datetime, timedelta
import pandas as pd
import time

end = datetime.now()
start = end - timedelta(days=3*365)

batch_size = 500
all_dfs = []
for i in range(0, len(all_symbols), batch_size):
    batch = all_symbols[i:i+batch_size]
    print(f"拉取第{i//batch_size+1}批, 共{len(batch)}只股票")
    for symbol in batch:
        try:
            fundamentals = qb.GetFundamental(symbol, start, end)
            df = fundamentals.to_dataframe()
            all_dfs.append(df)
        except Exception as e:
            print(f"{symbol}: {e}")
    time.sleep(1)  # 防止API速率限制

df = pd.concat(all_dfs)
print(df.head())
```

### 3. 后续处理
- 拼接好df后，直接用前面给你的pandas代码做6大条件统计、分组、筛选等。

---

## 注意事项
- **分批拉取**是唯一可行的全市场研究方式，虽然慢，但不会报错。
- 如果你有更高的API速率权限，可以适当调大batch_size。
- 如果你只关心大盘股，可以先用市值筛选一遍all_symbols再分批。

---

## 总结
- **不要一次性传递几千只symbol给GetFundamental**，要分批。
- 拼接所有批次结果后再做统计分析。

---

如需**自动化分批拉取+6大条件统计的完整脚本**，请回复“需要全自动分批统计脚本”。  
如遇到内存溢出、API限流等问题，也可以告诉我，我会帮你优化！ 