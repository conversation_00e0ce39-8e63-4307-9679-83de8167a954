from clickhouse_driver import Client

# 连接到lseg数据库
client_lseg = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='lseg'
)

# 连接到ap_research数据库
client_ap = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 检查缺失股票在lseg原始数据中的情况 ===")

# 我们发现的缺失股票
missing_stocks_2024 = ['AU', 'CCEP', 'POST']
missing_stocks_2023 = ['CCEP', 'MRP', 'POST', 'SIG', 'VG']

# 合并去重
all_missing_stocks = list(set(missing_stocks_2024 + missing_stocks_2023))
print(f"需要检查的股票: {', '.join(all_missing_stocks)}")

# 原始财报表
financial_tables = ['income_statement', 'balance_sheet_history', 'cash_flow']

for stock in all_missing_stocks:
    print(f"\n{'='*50}")
    print(f"检查股票: {stock}")
    print(f"{'='*50}")
    
    # 1. 先在ap_research中确认这个股票的基本信息
    print(f"\n1. 在ap_research中查找{stock}的基本信息...")
    query_ap_info = f"""
    SELECT DISTINCT 
        stock_symbol, 
        original_stock_code, 
        instrument,
        turnover_type
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = '{stock}'
    LIMIT 5
    """
    
    result_ap_info = client_ap.execute(query_ap_info)
    if result_ap_info:
        print(f"  在ap_research中找到{stock}:")
        for symbol, orig_code, instrument, turnover in result_ap_info:
            print(f"    stock_symbol: {symbol}, original_code: {orig_code}")
            print(f"    instrument: {instrument}, turnover_type: {turnover}")
            
            # 提取可能的匹配模式
            possible_instruments = [instrument, f"{stock}.O", f"{stock}.K", f"{stock}.NB", f"{stock}.N"]
            
    else:
        print(f"  在ap_research中未找到{stock}")
        possible_instruments = [f"{stock}.O", f"{stock}.K", f"{stock}.NB", f"{stock}.N"]
    
    # 2. 在lseg原始表中查找
    print(f"\n2. 在lseg原始表中查找{stock}...")
    
    for table in financial_tables:
        print(f"\n  检查表: {table}")
        
        for instrument_pattern in possible_instruments:
            # 查找该股票在原始表中的数据
            query_lseg = f"""
            SELECT 
                instrument,
                financial_period_absolute,
                COUNT(*) as record_count
            FROM {table}
            WHERE instrument LIKE '%{stock}%' 
               OR instrument = '{instrument_pattern}'
            GROUP BY instrument, financial_period_absolute
            ORDER BY instrument, financial_period_absolute
            LIMIT 20
            """
            
            try:
                result_lseg = client_lseg.execute(query_lseg)
                if result_lseg:
                    print(f"    在{table}中找到匹配 '{instrument_pattern}':")
                    for instr, period, count in result_lseg:
                        print(f"      {instr} - {period}: {count}条记录")
                    break  # 找到就不继续查其他模式了
            except Exception as e:
                print(f"    查询{table}时出错: {e}")
        
        if not result_lseg:
            print(f"    在{table}中未找到{stock}的数据")

# 3. 特别检查2023年和2024年的季报数据
print(f"\n{'='*60}")
print("特别检查缺失的季报期间")
print(f"{'='*60}")

check_periods = [
    ('CCEP', ['FY2023Q1', 'FY2023Q2', 'FY2024Q1', 'FY2024Q2']),
    ('POST', ['FY2023Q1', 'FY2023Q2', 'FY2023Q3', 'FY2024Q1', 'FY2024Q2', 'FY2024Q3']),
    ('MRP', ['FY2023Q1', 'FY2023Q2']),
    ('VG', ['FY2023Q1', 'FY2023Q2']),
    ('SIG', ['FY2023Q1']),
    ('AU', ['FY2024Q1', 'FY2024Q2'])
]

for stock, periods in check_periods:
    print(f"\n检查{stock}的特定缺失期间:")
    
    # 先获取该股票的instrument格式
    query_instrument = f"""
    SELECT DISTINCT instrument
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = '{stock}'
    LIMIT 1
    """
    
    result_instrument = client_ap.execute(query_instrument)
    if result_instrument:
        instrument = result_instrument[0][0]
        print(f"  使用instrument: {instrument}")
        
        for period in periods:
            print(f"    检查期间: {period}")
            found_in_any_table = False
            
            for table in financial_tables:
                query_period = f"""
                SELECT COUNT(*) as count
                FROM {table}
                WHERE instrument = '{instrument}'
                  AND financial_period_absolute = '{period}'
                """
                
                try:
                    result_period = client_lseg.execute(query_period)
                    count = result_period[0][0]
                    if count > 0:
                        print(f"      在{table}中找到{count}条记录")
                        found_in_any_table = True
                except Exception as e:
                    print(f"      查询{table}出错: {e}")
            
            if not found_in_any_table:
                print(f"      在所有原始表中都未找到{period}的数据")

print(f"\n=== 检查完成 ===")

