from itertools import groupby

def revenue_growth_continuity(growth_rates, weight_a=0.4, weight_b=0.6):
    n = len(growth_rates)
    if n == 0:
        return None
    # 严格连续得分
    L = 0
    current = 0
    for g in growth_rates:
        if g > 0:
            current += 1
            L = max(L, current)
        else:
            current = 0
    T = max(int(n * 0.6), 1)
    strict_score = min(L / T, 1)
    # 趋势占比得分
    G = sum([g > 0 for g in growth_rates])
    R = max(int(n * 0.8), 1)
    trend_score = min(G / R, 1)
    # 综合分
    return strict_score * weight_a + trend_score * weight_b
# 在revenue_growth_continuity因子处调用该函数，动态窗口长度由上市年限决定，权重可配置