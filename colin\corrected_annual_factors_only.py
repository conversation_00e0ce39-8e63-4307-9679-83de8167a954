import pandas as pd
import numpy as np
from datetime import datetime
from clickhouse_driver import Client

def connect_to_ap_research():
    """连接到ap_research数据库"""
    try:
        client = Client(
            host='************',
            port=9000,
            user='default',
            password='5ur2pK8WZQdy2',
            database='ap_research'
        )
        print("✅ 连接ap_research数据库成功")
        return client
    except Exception as e:
        print(f"❌ 连接数据库失败: {e}")
        return None

def get_pure_annual_data(client, stock_symbol):
    """
    获取纯年报数据，严格排除季报
    """
    query = f"""
    SELECT 
        financial_period_absolute,
        item_name,
        value
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = '{stock_symbol}'
      AND financial_period_absolute REGEXP '^FY[0-9]{{4}}$'  -- 严格匹配：只要FY+4位数字
      AND value IS NOT NULL
    ORDER BY financial_period_absolute
    """
    
    try:
        result = client.execute(query)
        df = pd.DataFrame(result, columns=['period', 'item_name', 'value'])
        
        if len(df) == 0:
            return pd.DataFrame()
        
        # 转换为透视表
        pivot_df = df.pivot_table(
            index='period', 
            columns='item_name', 
            values='value', 
            aggfunc='first'
        ).reset_index()
        
        return pivot_df
    except Exception as e:
        print(f"❌ 获取{stock_symbol}年报数据失败: {e}")
        return pd.DataFrame()

def calculate_annual_fcf_cagr(annual_data, stock_symbol):
    """
    基于纯年报数据计算FCF CAGR
    """
    if len(annual_data) < 3:
        return None, "年报数据不足3年"
    
    # 寻找FCF字段
    fcf_column = None
    for col in ['Free Cash Flow', 'Operating Cash Flow']:
        if col in annual_data.columns:
            fcf_column = col
            break
    
    if fcf_column is None:
        return None, "未找到FCF字段"
    
    # 过滤有效数据
    valid_data = annual_data[annual_data[fcf_column].notna()].copy()
    
    if len(valid_data) < 3:
        return None, f"有效年报FCF数据不足({len(valid_data)}年)"
    
    # 寻找最长连续正值区间
    fcf_values = valid_data[fcf_column].values
    periods = valid_data['period'].values
    
    best_start = best_end = -1
    best_length = 0
    
    current_start = -1
    for i, fcf in enumerate(fcf_values):
        if fcf > 0:
            if current_start == -1:
                current_start = i
            current_end = i
            current_length = current_end - current_start + 1
            if current_length > best_length:
                best_start = current_start
                best_end = current_end
                best_length = current_length
        else:
            current_start = -1
    
    if best_length >= 3:
        start_fcf = fcf_values[best_start]
        end_fcf = fcf_values[best_end]
        years = best_end - best_start
        
        cagr = ((end_fcf / start_fcf) ** (1/years) - 1) * 100
        period_info = f"{periods[best_start]}-{periods[best_end]}({best_length}年)"
        return cagr, f"年报连续正值区间{period_info}"
    
    # 如果没有连续正值区间，尝试首尾正值
    if fcf_values[0] > 0 and fcf_values[-1] > 0:
        years = len(fcf_values) - 1
        cagr = ((fcf_values[-1] / fcf_values[0]) ** (1/years) - 1) * 100
        period_info = f"{periods[0]}-{periods[-1]}({len(fcf_values)}年)"
        return cagr, f"年报首尾正值{period_info}"
    
    return None, "年报FCF无法找到合适的正值区间"

def main():
    """主函数"""
    print("=== 修正年报因子计算（严格年报数据）===")
    print()
    print("📋 修正原则:")
    print("   ✅ 只使用纯年报数据（FY+4位年份）")
    print("   ✅ 完全排除季报数据")
    print("   ✅ 年报FCF CAGR只基于年报FCF")
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        return
    
    # 测试Mega7股票
    test_stocks = ['AMZN', 'TSLA', 'META', 'AAPL']
    
    results = []
    
    for stock in test_stocks:
        print(f"\n🔍 修正 {stock}:")
        
        # 获取纯年报数据
        annual_data = get_pure_annual_data(client, stock)
        print(f"   📊 纯年报数据: {len(annual_data)} 年")
        
        if len(annual_data) > 0:
            # 显示最近几年的FCF数据
            fcf_col = None
            for col in ['Free Cash Flow', 'Operating Cash Flow']:
                if col in annual_data.columns:
                    fcf_col = col
                    break
            
            if fcf_col:
                print(f"   💰 年报FCF数据 ({fcf_col}):")
                for _, row in annual_data.tail(6).iterrows():  # 显示最近6年
                    period = row['period']
                    fcf = row[fcf_col]
                    if pd.notna(fcf):
                        status = "✅" if fcf > 0 else "❌"
                        print(f"     {period}: {fcf:,.0f} {status}")
            
            # 计算年报FCF CAGR
            cagr, note = calculate_annual_fcf_cagr(annual_data, stock)
            
            if cagr is not None:
                print(f"   🎯 年报FCF CAGR: {cagr:.4f}% ({note})")
                results.append({
                    'stock_symbol': stock,
                    'annual_fcf_cagr': cagr,
                    'calculation_note': note,
                    'status': '成功'
                })
            else:
                print(f"   ❌ 年报FCF CAGR: 无法计算 ({note})")
                results.append({
                    'stock_symbol': stock,
                    'annual_fcf_cagr': None,
                    'calculation_note': note,
                    'status': '失败'
                })
        else:
            results.append({
                'stock_symbol': stock,
                'annual_fcf_cagr': None,
                'calculation_note': '无年报数据',
                'status': '失败'
            })
    
    # 保存结果
    results_df = pd.DataFrame(results)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f"colin/corrected_annual_fcf_cagr_{timestamp}.csv"
    results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"\n✅ 修正的年报FCF CAGR结果已保存到: {output_file}")
    
    # 显示修正结果
    print(f"\n📊 修正结果:")
    for _, row in results_df.iterrows():
        stock = row['stock_symbol']
        cagr = row['annual_fcf_cagr']
        if cagr is not None:
            print(f"   {stock}: {cagr:.4f}% ✅")
        else:
            print(f"   {stock}: 无法计算 ❌")
    
    client.disconnect()
    
    print(f"\n🎯 修正要点:")
    print(f"1. ✅ 严格使用年报数据（FY+4位数字）")
    print(f"2. ✅ 完全排除季报数据")
    print(f"3. ✅ 年报FCF CAGR基于年报FCF计算")
    print(f"4. ✅ 智能处理负值FCF（寻找正值区间）")

if __name__ == "__main__":
    main()

