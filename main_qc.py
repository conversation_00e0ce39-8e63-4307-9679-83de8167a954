# 柯林·麦克连成长价值优势投资法 - QuantConnect版本
# 基于聚宽社区策略改写
# 原文网址：https://www.joinquant.com/post/35642

from AlgorithmImports import *
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

class ColinMcLeanGrowthValueStrategy(QCAlgorithm):
    
    def Initialize(self):
        # 设置回测参数
        self.SetStartDate(2018, 1, 1)
        self.SetEndDate(2023, 12, 31)
        self.SetCash(10000000)  # 1000万初始资金
        
        # 设置基准 - 根据市场选择合适的基准
        self.SetBenchmark("SPY")  # 如果是美股市场
        
        # 设置手续费模型
        self.SetSecurityInitializer(lambda x: x.SetFeeModel(ConstantFeeModel(5)))
        
        # 选择股票市场
        self.AddUniverse(self.CoarseSelectionFunction, self.FineSelectionFunction)
        
        # 设置调仓频率 - 每月1日
        self.Schedule.On(self.DateRules.MonthStart(), self.TimeRules.At(9, 30), self.Rebalance)
        
        # 存储选中的股票
        self.selected_stocks = []
        self.last_selection_time = None
        
        # 设置热身期
        self.SetWarmUp(timedelta(days=365))
        
        # 记录调仓月份
        self.rebalance_months = [5, 9, 11]
        
    def CoarseSelectionFunction(self, coarse):
        """粗选：基本筛选条件"""
        if self.IsWarmingUp:
            return []
            
        # 过滤掉价格过低和成交量过小的股票
        filtered = [x for x in coarse if x.HasFundamentalData and x.Price > 5 and x.DollarVolume > 50000000]
        
        # 按成交量排序，选择前1000只
        sorted_by_volume = sorted(filtered, key=lambda x: x.DollarVolume, reverse=True)
        
        return [x.Symbol for x in sorted_by_volume[:1000]]
    
    def FineSelectionFunction(self, fine):
        """精选：实施柯林·麦克连选股标准"""
        if self.IsWarmingUp:
            return []
            
        # 只在5月、9月、11月进行选股
        if self.Time.month not in self.rebalance_months:
            return self.selected_stocks
            
        # 过滤掉没有财务数据的股票
        filtered_fine = [x for x in fine if x.FinancialStatements.IncomeStatement and 
                        x.FinancialStatements.CashFlowStatement and 
                        x.FinancialStatements.BalanceSheet]
        
        self.Log(f"开始选股筛选，候选股票数量: {len(filtered_fine)}")
        
        # 应用柯林·麦克连选股标准
        selected = self.ApplySelectionCriteria(filtered_fine)
        
        # 更新选中的股票列表
        self.selected_stocks = [x.Symbol for x in selected]
        self.last_selection_time = self.Time
        
        self.Log(f"选股完成，选中 {len(self.selected_stocks)} 只股票")
        
        return self.selected_stocks
    
    def ApplySelectionCriteria(self, fine):
        """应用柯林·麦克连的选股标准"""
        candidates = []
        
        for security in fine:
            try:
                # 获取财务数据
                fundamentals = security.FinancialStatements
                
                # 1. 年度营收成长率 > 前一年度营收成长率
                if not self.CheckRevenueGrowth(fundamentals):
                    continue
                    
                # 2. 最近三年每股自由现金流量皆 > 0
                if not self.CheckFreeCashFlow(fundamentals):
                    continue
                    
                # 3. 近四季营业利益率 >= 10%
                if not self.CheckOperatingMargin(fundamentals):
                    continue
                    
                # 4. 最近四季可运用资本报酬率 >= 10%
                if not self.CheckReturnOnCapital(fundamentals):
                    continue
                    
                # 5. 最近年度有效税率 >= 5%
                if not self.CheckTaxRate(fundamentals):
                    continue
                    
                candidates.append(security)
                
            except Exception as e:
                # 跳过有问题的股票
                self.Log(f"处理股票 {security.Symbol} 时出错: {str(e)}")
                continue
        
        # 限制选股数量，按市值排序
        candidates = sorted(candidates, key=lambda x: x.MarketCap, reverse=True)
        return candidates[:30]  # 最多选择30只股票
    
    def CheckRevenueGrowth(self, fundamentals):
        """检查营收成长率标准"""
        try:
            # 获取最近年度的营收数据
            income_statements = fundamentals.IncomeStatement
            if not income_statements or len(income_statements) < 3:
                return False
                
            # 计算营收成长率
            current_revenue = income_statements[0].TotalRevenue.Value
            previous_revenue = income_statements[1].TotalRevenue.Value
            prev_prev_revenue = income_statements[2].TotalRevenue.Value
            
            if previous_revenue <= 0 or prev_prev_revenue <= 0:
                return False
                
            current_growth = (current_revenue - previous_revenue) / previous_revenue
            previous_growth = (previous_revenue - prev_prev_revenue) / prev_prev_revenue
            
            return current_growth > previous_growth and current_growth > 0
            
        except:
            return False
    
    def CheckFreeCashFlow(self, fundamentals):
        """检查自由现金流标准"""
        try:
            cash_flow_statements = fundamentals.CashFlowStatement
            if not cash_flow_statements or len(cash_flow_statements) < 3:
                return False
                
            # 检查最近三年的自由现金流
            for i in range(3):
                operating_cash_flow = cash_flow_statements[i].OperatingCashFlow.Value
                capital_expenditure = cash_flow_statements[i].CapitalExpenditure.Value
                
                if operating_cash_flow is None or capital_expenditure is None:
                    return False
                    
                free_cash_flow = operating_cash_flow - abs(capital_expenditure)
                
                if free_cash_flow <= 0:
                    return False
                    
            return True
            
        except:
            return False
    
    def CheckOperatingMargin(self, fundamentals):
        """检查营业利益率标准"""
        try:
            income_statements = fundamentals.IncomeStatement
            if not income_statements or len(income_statements) < 4:
                return False
                
            # 检查最近四季的营业利益率
            for i in range(min(4, len(income_statements))):
                operating_income = income_statements[i].OperatingIncome.Value
                total_revenue = income_statements[i].TotalRevenue.Value
                
                if total_revenue is None or total_revenue <= 0 or operating_income is None:
                    return False
                    
                operating_margin = (operating_income / total_revenue) * 100
                
                if operating_margin < 10:
                    return False
                    
            return True
            
        except:
            return False
    
    def CheckReturnOnCapital(self, fundamentals):
        """检查可运用资本报酬率标准"""
        try:
            income_statements = fundamentals.IncomeStatement
            balance_sheets = fundamentals.BalanceSheet
            
            if not income_statements or not balance_sheets:
                return False
                
            if len(income_statements) < 4 or len(balance_sheets) < 4:
                return False
                
            total_ratio = 0
            
            # 计算最近四季的可运用资本报酬率
            for i in range(min(4, len(income_statements))):
                operating_income = income_statements[i].OperatingIncome.Value
                total_equity = balance_sheets[i].TotalEquity.Value
                total_debt = balance_sheets[i].TotalDebt.Value
                
                if (operating_income is None or total_equity is None or 
                    total_debt is None or total_equity <= 0):
                    return False
                    
                # 柯林·麦克连定义：营业利益/(股东权益+长期负债)
                capital_employed = total_equity + total_debt
                
                if capital_employed <= 0:
                    return False
                    
                roc = operating_income / capital_employed
                total_ratio += roc
                
            # 四季总和应该 >= 8%
            return total_ratio >= 0.08
            
        except:
            return False
    
    def CheckTaxRate(self, fundamentals):
        """检查有效税率标准"""
        try:
            income_statements = fundamentals.IncomeStatement
            if not income_statements or len(income_statements) < 1:
                return False
                
            # 检查最近一年的有效税率
            pretax_income = income_statements[0].PretaxIncome.Value
            tax_expense = income_statements[0].TaxProvision.Value
            
            if pretax_income is None or tax_expense is None or pretax_income <= 0:
                return False
                
            tax_rate = tax_expense / pretax_income
            
            return tax_rate >= 0.05
            
        except:
            return False
    
    def Rebalance(self):
        """调仓函数"""
        if self.IsWarmingUp:
            return
            
        # 只在5月、9月、11月进行调仓
        if self.Time.month not in self.rebalance_months:
            return
            
        self.Log(f"开始调仓，当前时间: {self.Time}")
        
        # 如果没有选中的股票，清空仓位
        if not self.selected_stocks:
            self.Liquidate()
            self.Log("没有选中的股票，清空所有仓位")
            return
        
        # 计算每只股票的权重
        weight = 1.0 / len(self.selected_stocks)
        
        # 获取当前持仓
        current_holdings = [x.Key for x in self.Portfolio if x.Value.Invested]
        
        # 卖出不在选股列表中的股票
        for symbol in current_holdings:
            if symbol not in self.selected_stocks:
                self.Liquidate(symbol)
                self.Log(f"卖出股票: {symbol}")
        
        # 买入选中的股票
        for symbol in self.selected_stocks:
            if symbol in self.ActiveSecurities:
                self.SetHoldings(symbol, weight)
                self.Log(f"买入股票: {symbol}, 权重: {weight:.2%}")
        
        self.Log(f"调仓完成，持仓股票数量: {len(self.selected_stocks)}")
    
    def OnData(self, data):
        """数据处理函数"""
        pass
    
    def OnSecuritiesChanged(self, changes):
        """股票变化处理"""
        for security in changes.AddedSecurities:
            security.SetLeverage(1)
            
        if changes.RemovedSecurities:
            for security in changes.RemovedSecurities:
                if security.Symbol in self.selected_stocks:
                    self.selected_stocks.remove(security.Symbol) 