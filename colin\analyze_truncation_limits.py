from clickhouse_driver import Client
import pandas as pd
import numpy as np

def connect_to_ap_research():
    """连接到ap_research数据库"""
    client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')
    print("✅ 连接ap_research数据库成功")
    return client

def analyze_truncation_limits():
    """分析截断限制是否合理"""
    
    print("=== 分析因子截断限制的合理性 ===")
    print()
    
    client = connect_to_ap_research()
    
    # 获取Mega7的原始数据
    mega7_stocks = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META']
    
    query = f"""
    SELECT 
        stock_symbol,
        financial_period_absolute,
        item_name,
        value
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE financial_period_absolute REGEXP '^FY[0-9]{{4}}$'
      AND stock_symbol IN {tuple(mega7_stocks)}
      AND financial_period_absolute = (
          SELECT MAX(financial_period_absolute) 
          FROM priority_quality_fundamental_data_complete_deduped fp2 
          WHERE fp2.stock_symbol = priority_quality_fundamental_data_complete_deduped.stock_symbol
          AND fp2.financial_period_absolute REGEXP '^FY[0-9]{{4}}$'
      )
    ORDER BY stock_symbol, item_name
    """
    
    result = client.execute(query)
    df = pd.DataFrame(result, columns=['stock_symbol', 'financial_period_absolute', 'item_name', 'value'])
    
    print(f"📊 获取到 {len(result)} 条最新年报数据")
    
    # 透视数据
    pivot_df = df.pivot_table(
        index=['stock_symbol', 'financial_period_absolute'],
        columns='item_name',
        values='value',
        aggfunc='first'
    ).reset_index()
    
    print("\n🔍 分析可能被截断的关键指标:")
    print("=" * 80)
    
    # 1. 分析ROE（当前限制：50%）
    print("1. ROE分析（当前限制：-50% 到 50%）")
    print("-" * 60)
    
    for _, row in pivot_df.iterrows():
        stock = row['stock_symbol']
        net_income = row.get('Normalized Net Income - Bottom Line', 0) or 0
        equity_1 = row.get('Shareholders\' Equity - Attributable to Parent Shareholders - Total', 0) or 0
        equity_2 = row.get('Common Equity - Total', 0) or 0
        equity_3 = row.get('Common Equity Attributable to Parent Shareholders', 0) or 0
        
        total_equity = equity_1 or equity_2 or equity_3
        
        if total_equity > 0:
            raw_roe = (net_income / total_equity) * 100
            truncated_roe = np.clip(raw_roe, -50, 50)
            
            if abs(raw_roe) > 50:
                print(f"   {stock}: 原始ROE = {raw_roe:.2f}%, 截断后 = {truncated_roe:.2f}% ⚠️  被截断")
            else:
                print(f"   {stock}: ROE = {raw_roe:.2f}%")
    
    print()
    
    # 2. 分析ROIC（当前限制：50%）
    print("2. ROIC分析（当前限制：-30% 到 50%）")
    print("-" * 60)
    
    for _, row in pivot_df.iterrows():
        stock = row['stock_symbol']
        net_income = row.get('Normalized Net Income - Bottom Line', 0) or 0
        debt = row.get('Debt - Total', 0) or 0
        equity_1 = row.get('Shareholders\' Equity - Attributable to Parent Shareholders - Total', 0) or 0
        equity_2 = row.get('Common Equity - Total', 0) or 0
        equity_3 = row.get('Common Equity Attributable to Parent Shareholders', 0) or 0
        
        total_equity = equity_1 or equity_2 or equity_3
        invested_capital = debt + total_equity
        
        if invested_capital > 0:
            raw_roic = (net_income / invested_capital) * 100
            truncated_roic = np.clip(raw_roic, -30, 50)
            
            if raw_roic > 50 or raw_roic < -30:
                print(f"   {stock}: 原始ROIC = {raw_roic:.2f}%, 截断后 = {truncated_roic:.2f}% ⚠️  被截断")
            else:
                print(f"   {stock}: ROIC = {raw_roic:.2f}%")
    
    print()
    
    # 3. 分析净利润率（当前限制：50%）
    print("3. 净利润率分析（当前限制：-50% 到 50%）")
    print("-" * 60)
    
    for _, row in pivot_df.iterrows():
        stock = row['stock_symbol']
        net_income = row.get('Normalized Net Income - Bottom Line', 0) or 0
        revenue = row.get('Revenue from Business Activities - Total', 0) or 0
        
        if revenue > 0:
            raw_margin = (net_income / revenue) * 100
            truncated_margin = np.clip(raw_margin, -50, 50)
            
            if abs(raw_margin) > 50:
                print(f"   {stock}: 原始净利润率 = {raw_margin:.2f}%, 截断后 = {truncated_margin:.2f}% ⚠️  被截断")
            else:
                print(f"   {stock}: 净利润率 = {raw_margin:.2f}%")
    
    print()
    
    # 4. 分析R&D强度（当前限制：50%）
    print("4. R&D强度分析（当前限制：上限 50%）")
    print("-" * 60)
    
    for _, row in pivot_df.iterrows():
        stock = row['stock_symbol']
        rd_expense = row.get('Research & Development Expense', 0) or 0
        rd_supplemental = row.get('Research & Development Expense - Supplemental', 0) or 0
        total_rd = rd_expense + rd_supplemental
        revenue = row.get('Revenue from Business Activities - Total', 0) or 0
        
        if revenue > 0:
            raw_rd_ratio = (total_rd / revenue) * 100
            truncated_rd_ratio = min(raw_rd_ratio, 50)
            
            if raw_rd_ratio > 50:
                print(f"   {stock}: 原始R&D强度 = {raw_rd_ratio:.2f}%, 截断后 = {truncated_rd_ratio:.2f}% ⚠️  被截断")
            else:
                print(f"   {stock}: R&D强度 = {raw_rd_ratio:.2f}%")
    
    print()
    
    # 5. 建议新的截断限制
    print("🎯 建议的新截断限制:")
    print("=" * 80)
    
    # 获取更大样本的分布情况
    sample_query = """
    SELECT 
        stock_symbol,
        item_name,
        value
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE financial_period_absolute REGEXP '^FY[0-9]{4}$'
      AND financial_period_absolute = (
          SELECT MAX(financial_period_absolute) 
          FROM priority_quality_fundamental_data_complete_deduped fp2 
          WHERE fp2.stock_symbol = priority_quality_fundamental_data_complete_deduped.stock_symbol
          AND fp2.financial_period_absolute REGEXP '^FY[0-9]{{4}}$'
      )
      AND item_name IN (
          'Normalized Net Income - Bottom Line',
          'Shareholders'' Equity - Attributable to Parent Shareholders - Total',
          'Common Equity - Total',
          'Revenue from Business Activities - Total',
          'Research & Development Expense',
          'Debt - Total'
      )
    """
    
    sample_result = client.execute(sample_query)
    sample_df = pd.DataFrame(sample_result, columns=['stock_symbol', 'item_name', 'value'])
    
    # 计算各种比率的分布
    sample_pivot = sample_df.pivot_table(
        index='stock_symbol',
        columns='item_name',
        values='value',
        aggfunc='first'
    ).reset_index()
    
    # 计算ROE分布
    roe_values = []
    for _, row in sample_pivot.iterrows():
        net_income = row.get('Normalized Net Income - Bottom Line', 0) or 0
        equity_1 = row.get('Shareholders\' Equity - Attributable to Parent Shareholders - Total', 0) or 0
        equity_2 = row.get('Common Equity - Total', 0) or 0
        
        total_equity = equity_1 or equity_2
        if total_equity > 0:
            roe = (net_income / total_equity) * 100
            if -200 < roe < 200:  # 排除极端异常值
                roe_values.append(roe)
    
    # 计算净利润率分布
    margin_values = []
    for _, row in sample_pivot.iterrows():
        net_income = row.get('Normalized Net Income - Bottom Line', 0) or 0
        revenue = row.get('Revenue from Business Activities - Total', 0) or 0
        
        if revenue > 0:
            margin = (net_income / revenue) * 100
            if -100 < margin < 100:  # 排除极端异常值
                margin_values.append(margin)
    
    # 计算R&D强度分布
    rd_values = []
    for _, row in sample_pivot.iterrows():
        rd_expense = row.get('Research & Development Expense', 0) or 0
        revenue = row.get('Revenue from Business Activities - Total', 0) or 0
        
        if revenue > 0 and rd_expense > 0:
            rd_ratio = (rd_expense / revenue) * 100
            if 0 < rd_ratio < 100:  # 排除极端异常值
                rd_values.append(rd_ratio)
    
    if roe_values:
        roe_95 = np.percentile(roe_values, 95)
        roe_99 = np.percentile(roe_values, 99)
        print(f"📊 ROE分布 ({len(roe_values)}个样本):")
        print(f"   95分位数: {roe_95:.1f}%")
        print(f"   99分位数: {roe_99:.1f}%")
        print(f"   建议上限: {min(100, roe_99):.0f}%")
    
    if margin_values:
        margin_95 = np.percentile(margin_values, 95)
        margin_99 = np.percentile(margin_values, 99)
        print(f"📊 净利润率分布 ({len(margin_values)}个样本):")
        print(f"   95分位数: {margin_95:.1f}%")
        print(f"   99分位数: {margin_99:.1f}%")
        print(f"   建议上限: {min(80, margin_99):.0f}%")
    
    if rd_values:
        rd_95 = np.percentile(rd_values, 95)
        rd_99 = np.percentile(rd_values, 99)
        print(f"📊 R&D强度分布 ({len(rd_values)}个样本):")
        print(f"   95分位数: {rd_95:.1f}%")
        print(f"   99分位数: {rd_99:.1f}%")
        print(f"   建议上限: {min(100, rd_99):.0f}%")
    
    print()
    print("💡 建议调整:")
    print("   • ROE: -80% 到 80%（从 -50% 到 50%）")
    print("   • ROIC: -50% 到 80%（从 -30% 到 50%）")
    print("   • 净利润率: -80% 到 80%（从 -50% 到 50%）")
    print("   • R&D强度: 上限 80%（从 50%）")
    print("   • 保留极值处理，但提高截断阈值以保留更多信息")

if __name__ == "__main__":
    analyze_truncation_limits()

