from clickhouse_driver import Client

# 连接到lseg数据库
client_lseg = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='lseg'
)

# 连接到ap_research数据库
client_ap = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 检查ap_research中有但lseg原始数据中没有的数据 ===")

# 1. 先获取ap_research中的所有唯一的(instrument, financial_period_absolute)组合
print("\n1. 获取ap_research中的唯一数据组合...")
query_ap_combinations = """
SELECT DISTINCT 
    instrument,
    financial_period_absolute,
    COUNT(*) as record_count
FROM priority_quality_fundamental_data_complete_deduped
WHERE period_end_date IS NOT NULL AND period_end_date != ''
GROUP BY instrument, financial_period_absolute
ORDER BY instrument, financial_period_absolute
"""

print("  正在查询ap_research数据...")
result_ap = client_ap.execute(query_ap_combinations)
print(f"  ap_research中共有 {len(result_ap):,} 个唯一的(instrument, period)组合")

# 2. 随机抽样检查一些组合是否在lseg中存在
print("\n2. 抽样检查ap_research中的数据是否在lseg原始表中存在...")

# 取前50个组合进行检查
sample_combinations = result_ap[:50]
print(f"  检查前50个组合...")

discrepancies = []
lseg_tables = ['income_statement', 'balance_sheet_history', 'cash_flow']

for i, (instrument, period, count) in enumerate(sample_combinations):
    if i % 10 == 0:
        print(f"    进度: {i+1}/50")
    
    found_in_lseg = False
    
    # 在lseg的三个表中查找
    for table in lseg_tables:
        query_lseg_check = f"""
        SELECT COUNT(*) as count
        FROM {table}
        WHERE instrument = '{instrument}'
          AND financial_period_absolute = '{period}'
        """
        
        try:
            result_lseg = client_lseg.execute(query_lseg_check)
            lseg_count = result_lseg[0][0]
            
            if lseg_count > 0:
                found_in_lseg = True
                break
        except Exception as e:
            print(f"      查询{table}时出错: {e}")
    
    if not found_in_lseg:
        discrepancies.append((instrument, period, count))

print(f"\n  发现 {len(discrepancies)} 个在ap_research中存在但lseg中不存在的组合:")
if discrepancies:
    print("  Instrument - Period - ap_research记录数")
    print("  " + "-" * 50)
    for instrument, period, count in discrepancies[:20]:  # 只显示前20个
        print(f"  {instrument} - {period} - {count}条记录")
    
    if len(discrepancies) > 20:
        print(f"  ... 还有 {len(discrepancies) - 20} 个")

# 3. 特别检查一些特定的股票
print("\n3. 特别检查一些特定股票的数据一致性...")

# 选择几个有代表性的股票进行详细检查
test_stocks = ['AAPL', 'MSFT', 'GOOGL']

for stock in test_stocks:
    print(f"\n  检查股票: {stock}")
    
    # 获取该股票在ap_research中的instrument
    query_ap_stock = f"""
    SELECT DISTINCT instrument, stock_symbol
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = '{stock}'
    LIMIT 1
    """
    
    result_ap_stock = client_ap.execute(query_ap_stock)
    if result_ap_stock:
        instrument, symbol = result_ap_stock[0]
        print(f"    在ap_research中的instrument: {instrument}")
        
        # 获取该股票在ap_research中的所有period
        query_ap_periods = f"""
        SELECT DISTINCT financial_period_absolute
        FROM priority_quality_fundamental_data_complete_deduped
        WHERE instrument = '{instrument}'
        ORDER BY financial_period_absolute
        """
        
        result_ap_periods = client_ap.execute(query_ap_periods)
        ap_periods = set(period for (period,) in result_ap_periods)
        print(f"    ap_research中的期间数: {len(ap_periods)}")
        
        # 获取该股票在lseg中的所有period（合并三个表）
        lseg_periods = set()
        for table in lseg_tables:
            query_lseg_periods = f"""
            SELECT DISTINCT financial_period_absolute
            FROM {table}
            WHERE instrument = '{instrument}'
            """
            
            try:
                result_lseg_periods = client_lseg.execute(query_lseg_periods)
                table_periods = set(period for (period,) in result_lseg_periods)
                lseg_periods.update(table_periods)
            except Exception as e:
                print(f"      查询{table}时出错: {e}")
        
        print(f"    lseg中的期间数: {len(lseg_periods)}")
        
        # 找出差异
        ap_only = ap_periods - lseg_periods
        lseg_only = lseg_periods - ap_periods
        
        if ap_only:
            print(f"    只在ap_research中存在的期间 ({len(ap_only)}个):")
            for period in sorted(list(ap_only))[:10]:  # 只显示前10个
                print(f"      {period}")
            if len(ap_only) > 10:
                print(f"      ... 还有 {len(ap_only) - 10} 个")
        
        if lseg_only:
            print(f"    只在lseg中存在的期间 ({len(lseg_only)}个):")
            for period in sorted(list(lseg_only))[:10]:  # 只显示前10个
                print(f"      {period}")
            if len(lseg_only) > 10:
                print(f"      ... 还有 {len(lseg_only) - 10} 个")
        
        if not ap_only and not lseg_only:
            print(f"    ✅ 数据完全一致")
    else:
        print(f"    在ap_research中未找到{stock}")

# 4. 检查是否有重复提取的数据
print("\n4. 检查是否有重复提取的数据...")

# 查找ap_research中记录数异常多的组合
query_high_count = """
SELECT 
    instrument,
    financial_period_absolute,
    COUNT(*) as record_count
FROM priority_quality_fundamental_data_complete_deduped
WHERE period_end_date IS NOT NULL AND period_end_date != ''
GROUP BY instrument, financial_period_absolute
HAVING record_count > 1000
ORDER BY record_count DESC
LIMIT 10
"""

result_high_count = client_ap.execute(query_high_count)
if result_high_count:
    print("  记录数异常高的组合（可能表明重复提取）:")
    print("  Instrument - Period - 记录数")
    print("  " + "-" * 40)
    for instrument, period, count in result_high_count:
        print(f"  {instrument} - {period} - {count:,}条记录")
else:
    print("  没有发现记录数异常高的组合")

print("\n=== 检查完成 ===")

