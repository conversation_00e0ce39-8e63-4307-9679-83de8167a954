from clickhouse_driver import Client
from datetime import datetime

# 连接到数据库
client_ap = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 修正季报数据补充逻辑 ===")

def clear_invalid_corrected_real():
    """
    清除无效的 corrected_real 记录
    """
    print("\n1. 清除无效的 corrected_real 记录...")
    
    try:
        # 删除当前所有数据
        client_ap.execute("TRUNCATE TABLE quarterly_data_filled")
        print("  ✅ 清空 quarterly_data_filled 表")
    except Exception as e:
        print(f"  ❌ 清空表时出错: {e}")

def identify_truly_calculable_periods():
    """
    识别真正可以计算的期间（必须有完整的3个其他季度）
    """
    print("\n2. 识别真正可以直接计算的期间...")
    
    # 检查每个股票每年的季度完整性
    stocks_to_check = ['CCEP', 'POST', 'MRP', 'VG', 'SIG', 'AU']
    years_to_check = [2023, 2024]
    
    truly_calculable = []
    needs_estimation = []
    
    for stock in stocks_to_check:
        for year in years_to_check:
            annual_period = f'FY{year}'
            
            # 检查年报是否存在
            annual_query = f"""
            SELECT COUNT(*) as count
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = '{stock}'
              AND financial_period_absolute = '{annual_period}'
            """
            
            try:
                annual_result = client_ap.execute(annual_query)
                has_annual = annual_result[0][0] > 0
                
                if not has_annual:
                    print(f"  ❌ {stock} {year}: 无年报数据")
                    continue
                
                # 检查每个季度的存在情况
                quarters_status = {}
                for q in [1, 2, 3, 4]:
                    quarter_period = f'FY{year}Q{q}'
                    quarter_query = f"""
                    SELECT COUNT(*) as count
                    FROM priority_quality_fundamental_data_complete_deduped
                    WHERE stock_symbol = '{stock}'
                      AND financial_period_absolute = '{quarter_period}'
                    """
                    
                    quarter_result = client_ap.execute(quarter_query)
                    quarters_status[q] = quarter_result[0][0] > 0
                
                # 分析哪些季度可以计算
                missing_quarters = [q for q, exists in quarters_status.items() if not exists]
                existing_quarters = [q for q, exists in quarters_status.items() if exists]
                
                print(f"  📊 {stock} {year}:")
                print(f"     年报: {'✅' if has_annual else '❌'}")
                print(f"     现有季度: Q{existing_quarters}")
                print(f"     缺失季度: Q{missing_quarters}")
                
                # 判断是否可以直接计算
                if len(missing_quarters) == 1 and len(existing_quarters) == 3:
                    missing_q = missing_quarters[0]
                    period = f'FY{year}Q{missing_q}'
                    truly_calculable.append({
                        'stock': stock,
                        'period': period,
                        'annual_period': annual_period,
                        'existing_quarters': [f'FY{year}Q{q}' for q in existing_quarters]
                    })
                    print(f"     ✅ 可直接计算: {period}")
                elif len(missing_quarters) > 1:
                    for missing_q in missing_quarters:
                        period = f'FY{year}Q{missing_q}'
                        needs_estimation.append({
                            'stock': stock,
                            'period': period,
                            'reason': f'缺失{len(missing_quarters)}个季度，无法直接计算'
                        })
                    print(f"     ⚠️ 需要估算: Q{missing_quarters} (缺失{len(missing_quarters)}个季度)")
                else:
                    print(f"     ✅ 数据完整")
                    
            except Exception as e:
                print(f"  ❌ 查询 {stock} {year} 时出错: {e}")
    
    return truly_calculable, needs_estimation

def insert_corrected_real_data(calculable_periods):
    """
    插入真正可以直接计算的数据
    """
    print(f"\n3. 插入真正的 corrected_real 数据...")
    
    total_inserted = 0
    
    for calc_info in calculable_periods:
        stock = calc_info['stock']
        period = calc_info['period']
        annual_period = calc_info['annual_period']
        existing_quarters = calc_info['existing_quarters']
        
        print(f"  📊 计算 {stock} {period}...")
        
        # 构建计算查询
        existing_quarters_str = "', '".join(existing_quarters)
        
        calc_query = f"""
        SELECT 
            a.item_name,
            a.statement_type,
            a.value as annual_value,
            SUM(q.value) as existing_quarters_sum,
            (a.value - SUM(q.value)) as calculated_value
        FROM (
            SELECT item_name, statement_type, value
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = '{stock}'
              AND financial_period_absolute = '{annual_period}'
        ) a
        JOIN (
            SELECT item_name, statement_type, value
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = '{stock}'
              AND financial_period_absolute IN ('{existing_quarters_str}')
        ) q ON a.item_name = q.item_name AND a.statement_type = q.statement_type
        WHERE a.value IS NOT NULL AND q.value IS NOT NULL
        GROUP BY a.item_name, a.statement_type, a.value
        HAVING COUNT(q.value) = {len(existing_quarters)}
        ORDER BY a.statement_type, a.item_name
        """
        
        try:
            results = client_ap.execute(calc_query)
            
            for item_name, statement_type, annual_val, quarters_sum, calc_val in results:
                # 插入记录
                record = {
                    'id': f"{stock}|{period}|{item_name}",
                    'stock_symbol': stock,
                    'financial_period_absolute': period,
                    'statement_type': statement_type,
                    'item_name': item_name,
                    'original_value': None,
                    'data_status': 'corrected_real',
                    'fill_method': 'direct_calculation',
                    'source_periods': f"{annual_period},{','.join(existing_quarters)}",
                    'filled_simple_estimates': None,
                    'corrected_filled_simple_estimates': round(calc_val, 2),
                    'original_effective_date': None,
                    'estimated_effective_date': None,
                    'corrected_effective_date': datetime.now().date(),
                    'created_at': datetime.now(),
                    'updated_at': datetime.now(),
                    'confidence_score': 0.95,
                    'notes': f'Direct calculation: {annual_period} minus {len(existing_quarters)} existing quarters'
                }
                
                insert_sql = """
                INSERT INTO quarterly_data_filled VALUES (
                    %(id)s, %(stock_symbol)s, %(financial_period_absolute)s, %(statement_type)s, %(item_name)s,
                    %(original_value)s, %(data_status)s, %(fill_method)s, %(source_periods)s,
                    %(filled_simple_estimates)s, %(corrected_filled_simple_estimates)s,
                    %(original_effective_date)s, %(estimated_effective_date)s, %(corrected_effective_date)s,
                    %(created_at)s, %(updated_at)s, %(confidence_score)s, %(notes)s
                )
                """
                
                client_ap.execute(insert_sql, record)
                total_inserted += 1
            
            print(f"     插入 {len(results)} 条记录")
            
        except Exception as e:
            print(f"     ❌ 计算 {period} 时出错: {e}")
    
    print(f"  ✅ 总计插入 {total_inserted} 条 corrected_real 记录")
    return total_inserted

def insert_estimation_placeholders(estimation_periods):
    """
    插入需要估算的占位记录
    """
    print(f"\n4. 插入需要估算的占位记录...")
    
    for est_info in estimation_periods:
        stock = est_info['stock']
        period = est_info['period']
        reason = est_info['reason']
        
        placeholder = {
            'id': f"{stock}|{period}|NEEDS_ESTIMATION",
            'stock_symbol': stock,
            'financial_period_absolute': period,
            'statement_type': 'placeholder',
            'item_name': 'NEEDS_ESTIMATION',
            'original_value': None,
            'data_status': 'needs_estimation',
            'fill_method': 'rolling_average',
            'source_periods': 'TBD',
            'filled_simple_estimates': None,
            'corrected_filled_simple_estimates': None,
            'original_effective_date': None,
            'estimated_effective_date': None,
            'corrected_effective_date': None,
            'created_at': datetime.now(),
            'updated_at': datetime.now(),
            'confidence_score': 0.70,
            'notes': reason
        }
        
        insert_sql = """
        INSERT INTO quarterly_data_filled VALUES (
            %(id)s, %(stock_symbol)s, %(financial_period_absolute)s, %(statement_type)s, %(item_name)s,
            %(original_value)s, %(data_status)s, %(fill_method)s, %(source_periods)s,
            %(filled_simple_estimates)s, %(corrected_filled_simple_estimates)s,
            %(original_effective_date)s, %(estimated_effective_date)s, %(corrected_effective_date)s,
            %(created_at)s, %(updated_at)s, %(confidence_score)s, %(notes)s
        )
        """
        
        try:
            client_ap.execute(insert_sql, placeholder)
            print(f"  📝 {stock} {period}: {reason}")
        except Exception as e:
            print(f"  ❌ 插入 {stock} {period} 占位记录时出错: {e}")

def verify_corrected_results():
    """
    验证修正后的结果
    """
    print(f"\n5. 验证修正后的结果...")
    
    try:
        # 统计记录数
        total_query = "SELECT COUNT(*) FROM quarterly_data_filled"
        total_result = client_ap.execute(total_query)
        total_count = total_result[0][0]
        
        # 按状态统计
        status_query = "SELECT data_status, COUNT(*) FROM quarterly_data_filled GROUP BY data_status"
        status_results = client_ap.execute(status_query)
        
        print(f"  总记录数: {total_count}")
        for status, count in status_results:
            print(f"  {status}: {count} 条")
        
        # 显示真正的 corrected_real 记录
        if total_count > 0:
            corrected_query = """
            SELECT stock_symbol, financial_period_absolute, COUNT(*) as item_count
            FROM quarterly_data_filled 
            WHERE data_status = 'corrected_real'
            GROUP BY stock_symbol, financial_period_absolute
            ORDER BY stock_symbol, financial_period_absolute
            """
            
            corrected_results = client_ap.execute(corrected_query)
            
            if corrected_results:
                print(f"\n  真正的 corrected_real 记录:")
                for stock, period, item_count in corrected_results:
                    print(f"    {stock} {period}: {item_count} 个科目")
            else:
                print(f"\n  ⚠️ 没有真正的 corrected_real 记录")
        
    except Exception as e:
        print(f"  ❌ 验证时出错: {e}")

def main():
    """
    主函数
    """
    print("开始修正季报数据补充逻辑...")
    
    try:
        # 1. 清除无效数据
        clear_invalid_corrected_real()
        
        # 2. 重新识别可计算期间
        calculable, estimation = identify_truly_calculable_periods()
        
        print(f"\n=== 分析结果 ===")
        print(f"真正可直接计算: {len(calculable)} 个期间")
        print(f"需要估算: {len(estimation)} 个期间")
        
        # 3. 插入正确的数据
        if calculable:
            corrected_count = insert_corrected_real_data(calculable)
        else:
            corrected_count = 0
            
        if estimation:
            insert_estimation_placeholders(estimation)
        
        # 4. 验证结果
        verify_corrected_results()
        
        print(f"\n=== 修正完成 ===")
        print(f"✅ 严格按照要求重新生成数据")
        print(f"📊 真正的 corrected_real 记录: {corrected_count}")
        print(f"📋 需要估算的期间: {len(estimation)}")
        
    except Exception as e:
        print(f"❌ 修正过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

