from clickhouse_driver import Client

# 快速连接和创建表
client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')

print("创建表1: quarterly_data_filled_enhanced...")
client.execute("""
CREATE TABLE IF NOT EXISTS quarterly_data_filled_enhanced (
    id String, stock_symbol String, financial_period_absolute String, statement_type String, item_name String,
    original_value Nullable(Float64), original_effective_date Nullable(Date),
    filled_simple_estimates Nullable(Float64), estimated_effective_date Nullable(Date),
    corrected_filled_simple_estimates Nullable(Float64), corrected_effective_date Nullable(Date),
    data_status String, fill_method Nullable(String), confidence_score Nullable(Float64),
    item_status String, item_source String, item_mapping Nullable(String), correction_applicable String,
    historical_frequency Nullable(Int32), last_historical_period Nullable(String), first_current_period Nullable(String),
    estimation_notes Nullable(String), correction_notes Nullable(String), item_change_reason Nullable(String),
    source_periods Nullable(String), calculation_formula Nullable(String),
    created_at DateTime, updated_at DateTime, processing_version String, notes Nullable(String)
) ENGINE = MergeTree() ORDER BY (stock_symbol, financial_period_absolute, statement_type, item_name) PARTITION BY stock_symbol;
""")
print("✅ 表1创建成功")

print("创建表2: item_status_mapping...")
client.execute("""
CREATE TABLE IF NOT EXISTS item_status_mapping (
    id String, stock_symbol String, statement_type String, item_name String,
    item_status String, item_source String, item_mapping Nullable(String),
    historical_frequency Nullable(Int32), first_historical_period Nullable(String), last_historical_period Nullable(String),
    first_current_period Nullable(String), current_frequency Nullable(Int32),
    semantic_similarity Nullable(Float64), value_correlation Nullable(Float64), detection_confidence Nullable(Float64),
    item_change_reason Nullable(String), change_detected_date Nullable(Date),
    created_at DateTime, updated_at DateTime, analysis_version String, notes Nullable(String)
) ENGINE = MergeTree() ORDER BY (stock_symbol, statement_type, item_name) PARTITION BY statement_type;
""")
print("✅ 表2创建成功")

print("创建表3: long_time_fundamental_na_list_detailed...")
client.execute("""
CREATE TABLE IF NOT EXISTS long_time_fundamental_na_list_detailed (
    id String, stock_symbol String,
    missing_periods Array(String), available_periods Array(String), total_missing_count Int32, total_available_count Int32,
    earliest_available_period Nullable(String), latest_available_period Nullable(String),
    missing_period_start Nullable(String), missing_period_end Nullable(String),
    missing_reason String, data_gap_type String, investigation_status String,
    recommended_action String, alternative_data_sources Nullable(String),
    created_at DateTime, updated_at DateTime, analysis_date Date, analyst_notes Nullable(String)
) ENGINE = MergeTree() ORDER BY (stock_symbol, missing_period_start) PARTITION BY missing_reason;
""")
print("✅ 表3创建成功")

print("创建表4: estimation_quality_metrics...")
client.execute("""
CREATE TABLE IF NOT EXISTS estimation_quality_metrics (
    id String, metric_type String, fill_method String,
    total_estimations Int32, successful_estimations Int32, successful_corrections Int32, success_rate Float64,
    avg_confidence_score Float64, min_confidence_score Nullable(Float64), max_confidence_score Nullable(Float64), confidence_std_dev Nullable(Float64),
    active_items_count Int32, historical_only_count Int32, current_only_count Int32, renamed_items_count Int32,
    avg_estimation_error Nullable(Float64), correction_improvement_rate Nullable(Float64), data_coverage_rate Float64,
    analysis_period_start Date, analysis_period_end Date,
    created_at DateTime, updated_at DateTime, calculation_version String, detailed_notes Nullable(String)
) ENGINE = MergeTree() ORDER BY (metric_type, fill_method, analysis_period_start) PARTITION BY metric_type;
""")
print("✅ 表4创建成功")

# 验证
tables = client.execute("SHOW TABLES")
print(f"\n📊 数据库现有表数量: {len(tables)}个")
for table in sorted([t[0] for t in tables]):
    print(f"  • {table}")

print("\n🎉 4个核心表创建完成！")

