"""
分析ap_research数据库中所有财报科目
统计所有科目名称、报表类型、出现频次等信息
"""

from clickhouse_driver import Client
import pandas as pd
from collections import defaultdict, Counter

def connect_to_ap_research():
    """连接到ap_research数据库"""
    try:
        client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')
        print("✅ 连接ap_research数据库成功")
        return client
    except Exception as e:
        print(f"❌ 连接数据库失败: {e}")
        return None

def analyze_all_financial_items(client):
    """分析所有财报科目"""
    print("\n=== 分析所有财报科目 ===")
    
    # 查询所有财报数据的科目信息
    query = """
    SELECT 
        statement_type,
        item_name,
        COUNT(*) as frequency,
        COUNT(DISTINCT stock_symbol) as stock_count,
        MIN(substring(financial_period_absolute, 3, 4)) as earliest_year,
        MAX(substring(financial_period_absolute, 3, 4)) as latest_year
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE item_name IS NOT NULL 
      AND item_name != ''
    GROUP BY statement_type, item_name
    ORDER BY statement_type, frequency DESC
    """
    
    print("📊 执行查询...")
    result = client.execute(query)
    
    print(f"✅ 获取到 {len(result)} 个不同的科目记录")
    
    # 转换为DataFrame便于分析
    df = pd.DataFrame(result, columns=[
        'statement_type', 'item_name', 'frequency', 'stock_count', 
        'earliest_year', 'latest_year'
    ])
    
    return df

def generate_comprehensive_report(df):
    """生成综合分析报告"""
    print("\n=== 生成综合分析报告 ===")
    
    # 按报表类型分组统计
    statement_stats = df.groupby('statement_type').agg({
        'item_name': 'count',
        'frequency': 'sum',
        'stock_count': 'max'
    }).round(2)
    
    print("\n📋 报表类型统计:")
    for statement_type, stats in statement_stats.iterrows():
        print(f"   {statement_type}: {stats['item_name']}个科目, 总出现{stats['frequency']}次, 最多{stats['stock_count']}只股票")
    
    # 找出最常见的科目（跨所有报表类型）
    print("\n🔥 最常见的科目 (前30个):")
    top_items = df.nlargest(30, 'frequency')[['item_name', 'statement_type', 'frequency', 'stock_count']]
    for _, row in top_items.iterrows():
        print(f"   {row['item_name']} ({row['statement_type']}): {row['frequency']}次, {row['stock_count']}只股票")
    
    # 按报表类型分别显示最常见科目
    print("\n📊 各报表类型的常见科目:")
    for statement_type in df['statement_type'].unique():
        statement_df = df[df['statement_type'] == statement_type]
        print(f"\n   === {statement_type} ===")
        top_10 = statement_df.nlargest(10, 'frequency')[['item_name', 'frequency', 'stock_count']]
        for _, row in top_10.iterrows():
            print(f"      {row['item_name']}: {row['frequency']}次, {row['stock_count']}只股票")
    
    # 查找与因子相关的关键科目
    print("\n🔍 查找与因子计算相关的关键科目:")
    
    key_terms = {
        '净利润相关': ['net income', 'income', 'profit', 'earnings', '净利润', '利润'],
        '现金流相关': ['cash flow', 'free cash', 'operating cash', 'cash', '现金流', '现金'],
        '股东权益相关': ['equity', 'shareholders', 'stockholders', '权益', '股东'],
        '债务相关': ['debt', 'liabilities', 'borrowing', '债务', '负债'],
        '资产相关': ['assets', 'current assets', 'total assets', '资产'],
        '收入相关': ['revenue', 'sales', 'income', '收入', '营收'],
        '研发相关': ['research', 'development', 'r&d', '研发'],
        '税务相关': ['tax', 'taxes', '税'],
        '每股相关': ['per share', 'eps', '每股'],
        '股本相关': ['shares', 'outstanding', '股本', '股数']
    }
    
    for category, terms in key_terms.items():
        print(f"\n   === {category} ===")
        matching_items = []
        for _, row in df.iterrows():
            item_name_lower = row['item_name'].lower()
            if any(term.lower() in item_name_lower for term in terms):
                matching_items.append(row)
        
        # 按频次排序
        matching_items.sort(key=lambda x: x['frequency'], reverse=True)
        
        if matching_items:
            for item in matching_items[:15]:  # 显示前15个
                print(f"      {item['item_name']} ({item['statement_type']}): {item['frequency']}次, {item['stock_count']}只股票")
        else:
            print("      未找到相关科目")
    
    return df

def save_detailed_report(df):
    """保存详细报告"""
    print("\n💾 保存详细报告...")
    
    # 保存完整科目列表
    output_file = f"colin/financial_statement_items_analysis.csv"
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"   ✅ 完整科目分析已保存到: {output_file}")
    
    # 按报表类型分别保存
    for statement_type in df['statement_type'].unique():
        statement_df = df[df['statement_type'] == statement_type].sort_values('frequency', ascending=False)
        statement_file = f"colin/items_{statement_type.replace(' ', '_').lower()}.csv"
        statement_df.to_csv(statement_file, index=False, encoding='utf-8-sig')
        print(f"   ✅ {statement_type}科目已保存到: {statement_file}")
    
    # 生成因子映射建议
    generate_factor_mapping_suggestions(df)

def generate_factor_mapping_suggestions(df):
    """生成因子字段映射建议"""
    print("\n🎯 生成因子字段映射建议...")
    
    # 定义需要查找的因子字段
    factor_fields = {
        'Net Income': ['net income', 'income after tax', 'net earnings', 'profit after tax'],
        'Total Shareholders\' Equity': ['shareholders equity', 'stockholders equity', 'total equity', 'owners equity'],
        'Free Cash Flow': ['free cash flow', 'fcf', 'cash flow from operations', 'operating cash flow'],
        'Current Assets - Total': ['current assets', 'total current assets'],
        'Current Liabilities - Total': ['current liabilities', 'total current liabilities'],
        'Total Debt': ['total debt', 'long term debt', 'short term debt', 'borrowings'],
        'Earnings Per Share - Diluted': ['earnings per share', 'eps', 'diluted eps'],
        'Shares Outstanding - Diluted Average': ['shares outstanding', 'weighted average shares', 'diluted shares']
    }
    
    suggestions = {}
    
    print("\n   字段映射建议:")
    for target_field, search_terms in factor_fields.items():
        print(f"\n   🔍 查找 '{target_field}' 的可能匹配:")
        
        matches = []
        for _, row in df.iterrows():
            item_name_lower = row['item_name'].lower()
            for term in search_terms:
                if term.lower() in item_name_lower:
                    matches.append({
                        'item_name': row['item_name'],
                        'statement_type': row['statement_type'],
                        'frequency': row['frequency'],
                        'stock_count': row['stock_count'],
                        'match_term': term
                    })
                    break
        
        # 按频次排序
        matches.sort(key=lambda x: x['frequency'], reverse=True)
        
        if matches:
            suggestions[target_field] = matches
            for match in matches[:5]:  # 显示前5个最可能的匹配
                print(f"      ✅ {match['item_name']} ({match['statement_type']})")
                print(f"         频次: {match['frequency']}, 股票数: {match['stock_count']}")
        else:
            print(f"      ❌ 未找到匹配项")
    
    # 保存映射建议
    mapping_file = "colin/factor_field_mapping_suggestions.txt"
    with open(mapping_file, 'w', encoding='utf-8') as f:
        f.write("=== 因子字段映射建议 ===\n\n")
        for target_field, matches in suggestions.items():
            f.write(f"{target_field}:\n")
            for match in matches[:3]:  # 保存前3个最佳匹配
                f.write(f"  - {match['item_name']} ({match['statement_type']}): {match['frequency']}次\n")
            f.write("\n")
    
    print(f"\n   ✅ 字段映射建议已保存到: {mapping_file}")

def main():
    """主函数"""
    print("=== 财报科目综合分析 ===")
    print("分析ap_research数据库中所有财报科目和报表类型")
    
    try:
        # 1. 连接数据库
        client = connect_to_ap_research()
        if not client:
            return
        
        # 2. 分析所有财报科目
        df = analyze_all_financial_items(client)
        
        # 3. 生成综合报告
        generate_comprehensive_report(df)
        
        # 4. 保存详细报告
        save_detailed_report(df)
        
        print(f"\n=== 分析完成 ===")
        print(f"✅ 分析了 {len(df)} 个不同的财报科目")
        print(f"✅ 涵盖 {df['statement_type'].nunique()} 种报表类型")
        print(f"✅ 数据时间跨度: {df['earliest_year'].min()} - {df['latest_year'].max()}")
        print(f"🎯 请查看生成的文件获取详细的字段映射建议！")
        
        client.disconnect()
        
    except Exception as e:
        print(f"❌ 分析过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
