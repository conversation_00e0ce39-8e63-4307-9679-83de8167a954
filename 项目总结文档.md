# 股票财务数据分析项目总结文档

## 📊 项目概述
本项目完成了从ClickHouse数据库中提取股票行情数据和财务数据，进行数据质量分析、去重处理、匹配分析，最终筛选出具有完整三大财务报表的高质量股票。

## 🎯 主要成果
- **筛选出979只高质量股票**（基于2020-2025年交易表现）
- **识别出817只具有完整三大财务报表的股票**
- **生成三大财务报表的匹配数据**（按公告时间匹配行情数据）
- **完成数据去重和质量控制**

---

## 📁 核心文件分类说明

### 🏆 **最终结果文件（重要）**

#### 1. 股票筛选结果
- **`stock_performance_2020_2025_cumulative.csv`** (140KB)
  - **用途**: 基于2020-2025年交易表现筛选出的979只高质量股票
  - **内容**: 包含累计收益率、年化收益率、夏普比率、波动率、最大回撤等指标
  - **使用**: 作为后续财务数据匹配的基础股票池

#### 2. 完整财务报表股票
- **`stocks_with_complete_financial_statements.csv`** (29KB)
  - **用途**: 817只具有完整三大财务报表（利润表、资产负债表、现金流量表）的股票详细分析
  - **内容**: 每只股票的基础代码、原始代码、市场数据情况、三大报表覆盖情况
  - **使用**: 了解哪些股票有完整的财务数据可用于分析

- **`complete_stocks_original_codes.csv`** (5KB)
  - **用途**: 817只完整股票对应的原始RIC代码列表
  - **内容**: 股票代码列，可直接用于数据库查询
  - **使用**: 作为后续分析的标准股票池

#### 3. 缺失股票分析
- **`stocks_without_financial_statements.csv`** (3.5KB)
  - **用途**: 分析162只没有完整财务报表的股票情况
  - **内容**: 缺失原因分类（ETF、无数据等）
  - **使用**: 了解数据缺失情况，优化股票池

- **`financial_statements_coverage_analysis.csv`** (29KB)
  - **用途**: 979只股票的三大财务报表覆盖率详细分析
  - **内容**: 每只股票在三大表中的存在情况
  - **使用**: 全面了解数据覆盖情况

#### 4. 最终匹配结果（核心数据）
- **`filtered_stocks_income_statement_matching.csv`** (33MB)
  - **用途**: 利润表数据与行情数据的匹配结果
  - **内容**: 每只股票每个交易日的利润表财务期间信息
  - **使用**: 进行利润表相关的财务分析

- **`filtered_stocks_balance_sheet_matching.csv`** (12MB)
  - **用途**: 资产负债表数据与行情数据的匹配结果
  - **内容**: 每只股票每个交易日的资产负债表财务期间信息
  - **使用**: 进行资产负债表相关的财务分析

- **`filtered_stocks_cash_flow_matching.csv`** (42MB)
  - **用途**: 现金流量表数据与行情数据的匹配结果
  - **内容**: 每只股票每个交易日的现金流量表财务期间信息
  - **使用**: 进行现金流量表相关的财务分析

### 🔧 **核心脚本文件**

#### 1. 最终匹配脚本
- **`match_filtered_stocks_with_financial_periods_final_correct.py`** (14KB)
  - **功能**: 将979只筛选股票的市场数据与三大财务报表进行匹配
  - **逻辑**: 按公告时间+1天生效规则，匹配每个交易日可用的最新财务期间
  - **输出**: 生成三个分离的CSV文件（利润表、资产负债表、现金流量表）
  - **使用**: 如需重新生成匹配数据，运行此脚本

#### 2. 完整股票保存脚本
- **`save_complete_stocks.py`** (6KB)
  - **功能**: 识别并保存具有完整三大财务报表的股票
  - **逻辑**: 检查每只股票在三大表中的存在情况，找出交集
  - **输出**: 生成完整股票列表和分析报告
  - **使用**: 如需更新完整股票列表，运行此脚本

### 📊 **数据质量分析文件**

#### 1. 重复数据分析
- **`analyze_duplicates.py`** (4KB)
  - **功能**: 分析财务数据中的重复记录
  - **用途**: 数据质量检查，识别重复数据模式

- **`analyze_duplicates_and_missing.py`** (13KB)
  - **功能**: 综合分析重复数据和缺失值
  - **用途**: 全面数据质量评估

#### 2. 交易数据分析
- **`analyze_trading_days.py`** (3KB)
- **`analyze_trading_days_fixed.py`** (3KB)
- **`analyze_turnover_distribution.py`** (6KB)
  - **功能**: 分析交易天数、换手率分布等交易特征
  - **用途**: 了解股票的交易活跃度特征

### 🗂️ **财务数据文件**

#### 1. 财务项目清单
- **`all_financial_items.csv`** (60KB)
  - **内容**: 所有财务项目的汇总清单
  - **用途**: 了解可用的财务指标

- **`income_statement_items.csv`** (25KB)
- **`balance_sheet_items.csv`** (17KB)
- **`cash_flow_items.csv`** (18KB)
  - **内容**: 三大报表各自的财务项目清单
  - **用途**: 了解各报表的具体财务指标

#### 2. 原始财务数据（大型文件）
- **`income_statement_with_announce.csv`** (37GB)
- **`balance_sheet_with_announce.csv`** (18GB)
- **`cash_flow_with_announce.csv`** (16GB)
  - **内容**: 三大报表的原始数据（包含公告日期）
  - **用途**: 原始数据备份，一般不直接使用

- **`income_statement_with_announce_dedup.csv`** (1.7GB)
  - **内容**: 去重后的利润表数据
  - **用途**: 去重后的中间结果

#### 3. 股票代码映射
- **`stock_code_mapping.csv`** (6KB)
  - **内容**: 股票代码在不同格式间的映射关系
  - **用途**: 处理不同数据源间的代码格式差异

### 📈 **股票表现分析文件**

#### 1. 年度表现数据
- **`stock_performance_2020.csv`** (133KB)
- **`stock_performance_2021.csv`** (135KB)
- **`stock_performance_2022.csv`** (137KB)
- **`stock_performance_2023.csv`** (134KB)
- **`stock_performance_2024.csv`** (140KB)
- **`stock_performance_2025.csv`** (140KB)
  - **内容**: 各年度股票表现数据
  - **用途**: 年度表现分析

#### 2. 表现分析脚本
- **`stock_performance_2020_2025.py`** (8KB)
- **`stock_performance_comprehensive.py`** (11KB)
- **`stock_performance_efficient.py`** (8KB)
- **`stock_performance_simple.py`** (8KB)
  - **功能**: 计算股票表现指标（收益率、夏普比率等）
  - **用途**: 生成股票表现分析数据

### 🔍 **数据获取和检查脚本**

#### 1. 数据库连接和查询
- **`clickhouse_query.py`** (6KB)
  - **功能**: ClickHouse数据库连接和基础查询
  - **用途**: 数据库操作的基础工具

#### 2. 数据检查脚本
- **`check_*.py`** 系列文件
  - **功能**: 各种数据质量检查
  - **用途**: 数据验证和问题诊断

#### 3. 数据获取脚本
- **`fetch_*.py`** 系列文件
  - **功能**: 从数据库获取财务数据
  - **用途**: 数据提取和预处理

### 📋 **因子分析文件**

#### 1. McLean因子分析
- **`mclean_factors_implementation.py`** (25KB)
- **`mclean_factors_results.csv`** (197KB)
- **`mclean_factors_summary.py`** (5KB)
  - **功能**: McLean因子模型的实现和结果
  - **用途**: 多因子选股分析

#### 2. 优化因子分析
- **`improved_mclean_factors.py`** (14KB)
- **`optimized_mclean_factors.py`** (14KB)
- **`mega7_optimized_factors.py`** (15KB)
  - **功能**: 改进和优化的因子模型
  - **用途**: 更高级的因子分析

### 📊 **收入增长分析文件**

#### 1. 收入增长分析
- **`revenue_growth_analysis_final.csv`** (134KB)
- **`revenue_growth_corrected_final.py`** (13KB)
  - **功能**: 收入增长趋势分析
  - **用途**: 基本面分析

#### 2. 连续增长筛选
- **`revenue_growth_final_连续*.csv`** 系列文件
- **`revenue_growth_final_连续*.py`** 系列文件
  - **功能**: 筛选连续2-5年收入增长的股票
  - **用途**: 成长股筛选

### 🔧 **工具和辅助文件**

#### 1. 数据合并和映射
- **`merge_industry_info.py`** (5KB)
  - **功能**: 合并行业信息
  - **用途**: 添加行业分类数据

#### 2. 数据格式处理
- **`fix_stock_code_format.py`** (4KB)
- **`create_stock_code_mapping.py`** (7KB)
  - **功能**: 处理股票代码格式
  - **用途**: 数据标准化

---

## 🚀 **使用指南**

### 1. **快速开始**
如果您想使用最终的分析结果：
1. 使用 `complete_stocks_original_codes.csv` 作为标准股票池
2. 使用三个匹配文件进行财务分析：
   - `filtered_stocks_income_statement_matching.csv`
   - `filtered_stocks_balance_sheet_matching.csv`
   - `filtered_stocks_cash_flow_matching.csv`

### 2. **重新生成数据**
如果需要重新生成匹配数据：
1. 运行 `match_filtered_stocks_with_financial_periods_final_correct.py`
2. 运行 `save_complete_stocks.py` 更新完整股票列表

### 3. **数据质量检查**
如果怀疑数据质量：
1. 运行各种 `check_*.py` 脚本
2. 查看 `financial_statements_coverage_analysis.csv` 了解覆盖率

### 4. **扩展分析**
如果需要添加新的分析维度：
1. 基于 `stock_performance_2020_2025_cumulative.csv` 进行新的筛选
2. 使用 `stock_code_mapping.csv` 处理代码格式
3. 参考现有的分析脚本模式

---

## 📝 **注意事项**

1. **文件大小**: 部分原始数据文件很大（GB级别），建议只保留最终结果文件
2. **数据时效性**: 财务数据基于公告时间，使用时需注意时效性
3. **代码格式**: 不同数据源使用不同的股票代码格式，需要统一处理
4. **数据完整性**: 817只股票具有完整数据，其他股票可能存在数据缺失

---

## 🎯 **项目价值**

本项目成功实现了：
- ✅ 从大量股票中筛选出高质量股票
- ✅ 建立了完整的财务数据匹配体系
- ✅ 提供了可复用的数据分析框架
- ✅ 生成了标准化的数据输出格式

这些成果为后续的量化投资研究提供了坚实的数据基础。 