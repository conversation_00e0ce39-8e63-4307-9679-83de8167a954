from clickhouse_driver import Client
import pandas as pd
from datetime import datetime

# 连接到数据库
client_ap = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 最终生成结果展示 ===")

def get_direct_calculation_detailed_results(stock_symbol, missing_period):
    """
    获取直接计算的详细结果
    """
    year = int(missing_period[2:6])
    quarter = int(missing_period[7])
    
    results = []
    
    # 情况1: 通过半年报计算
    if quarter <= 2:
        half_year_period = f'FY{year}H1'
        other_quarter = f'FY{year}Q2' if quarter == 1 else f'FY{year}Q1'
        
        query_half_calc = f"""
        SELECT 
            h.item_name,
            h.statement_type,
            h.value as half_year_value,
            q.value as quarter_value,
            (h.value - q.value) as calculated_value
        FROM (
            SELECT item_name, statement_type, value
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = '{stock_symbol}'
              AND financial_period_absolute = '{half_year_period}'
        ) h
        JOIN (
            SELECT item_name, statement_type, value
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = '{stock_symbol}'
              AND financial_period_absolute = '{other_quarter}'
        ) q ON h.item_name = q.item_name AND h.statement_type = q.statement_type
        WHERE h.value IS NOT NULL AND q.value IS NOT NULL
        ORDER BY h.statement_type, h.item_name
        """
        
        try:
            result = client_ap.execute(query_half_calc)
            for item_name, statement_type, h_value, q_value, calc_value in result:
                results.append({
                    'stock_symbol': stock_symbol,
                    'financial_period_absolute': missing_period,
                    'statement_type': statement_type,
                    'item_name': item_name,
                    'original_value': None,
                    'data_status': 'corrected_real',
                    'fill_method': 'half_year_direct',
                    'source_periods': f'{half_year_period},{other_quarter}',
                    'filled_simple_estimates': None,
                    'corrected_filled_simple_estimates': round(calc_value, 2),
                    'calculation_details': f'{half_year_period}({h_value}) - {other_quarter}({q_value}) = {calc_value}',
                    'confidence_score': 0.95,
                    'notes': f'Direct calculation using half-year data'
                })
        except Exception as e:
            print(f"      查询{stock_symbol}-{missing_period}半年报计算时出错: {e}")
    
    # 情况2: 通过年报计算
    annual_period = f'FY{year}'
    
    # 获取同年其他已知季度
    known_quarters = []
    for q in range(1, 5):
        if q != quarter:
            test_period = f'FY{year}Q{q}'
            query_test = f"""
            SELECT COUNT(*) as count
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = '{stock_symbol}'
              AND financial_period_absolute = '{test_period}'
            """
            try:
                result_test = client_ap.execute(query_test)
                if result_test[0][0] > 0:
                    known_quarters.append(test_period)
            except:
                continue
    
    if known_quarters:
        known_quarters_str = "', '".join(known_quarters)
        
        query_annual_calc = f"""
        SELECT 
            a.item_name,
            a.statement_type,
            a.value as annual_value,
            SUM(q.value) as known_quarters_sum,
            (a.value - SUM(q.value)) as calculated_value
        FROM (
            SELECT item_name, statement_type, value
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = '{stock_symbol}'
              AND financial_period_absolute = '{annual_period}'
        ) a
        JOIN (
            SELECT item_name, statement_type, value
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = '{stock_symbol}'
              AND financial_period_absolute IN ('{known_quarters_str}')
        ) q ON a.item_name = q.item_name AND a.statement_type = q.statement_type
        WHERE a.value IS NOT NULL AND q.value IS NOT NULL
        GROUP BY a.item_name, a.statement_type, a.value
        ORDER BY a.statement_type, a.item_name
        """
        
        try:
            result = client_ap.execute(query_annual_calc)
            for item_name, statement_type, a_value, q_sum, calc_value in result:
                results.append({
                    'stock_symbol': stock_symbol,
                    'financial_period_absolute': missing_period,
                    'statement_type': statement_type,
                    'item_name': item_name,
                    'original_value': None,
                    'data_status': 'corrected_real',
                    'fill_method': 'annual_direct',
                    'source_periods': f'{annual_period},{",".join(known_quarters)}',
                    'filled_simple_estimates': None,
                    'corrected_filled_simple_estimates': round(calc_value, 2),
                    'calculation_details': f'{annual_period}({a_value}) - sum({q_sum}) = {calc_value}',
                    'confidence_score': 0.92,
                    'notes': f'Direct calculation using annual data minus {len(known_quarters)} known quarters'
                })
        except Exception as e:
            print(f"      查询{stock_symbol}-{missing_period}年报计算时出错: {e}")
    
    return results

def show_all_final_results():
    """
    展示所有最终计算结果
    """
    # 可直接计算的股票和期间
    direct_calculable = {
        'CCEP': ['FY2023Q1', 'FY2023Q2', 'FY2024Q1', 'FY2024Q2'],
        'MRP': ['FY2023Q1', 'FY2023Q2'],
        'VG': ['FY2023Q1', 'FY2023Q2'],
        'SIG': ['FY2023Q1'],
        'AU': ['FY2024Q1', 'FY2024Q2']
    }
    
    all_results = []
    
    print("\n1. 直接计算结果详情:")
    print("=" * 80)
    
    for stock_symbol, missing_periods in direct_calculable.items():
        print(f"\n股票: {stock_symbol}")
        print("-" * 40)
        
        for missing_period in missing_periods:
            print(f"\n  缺失期间: {missing_period}")
            
            # 获取详细计算结果
            period_results = get_direct_calculation_detailed_results(stock_symbol, missing_period)
            
            if period_results:
                print(f"    可计算科目数: {len(period_results)}")
                print(f"    计算方法: {period_results[0]['fill_method']}")
                print(f"    数据来源: {period_results[0]['source_periods']}")
                
                # 显示前5个科目的详细计算过程
                print(f"    前5个科目的计算详情:")
                for i, result in enumerate(period_results[:5]):
                    print(f"      {i+1}. {result['statement_type']} - {result['item_name']}")
                    print(f"         计算: {result['calculation_details']}")
                    print(f"         结果: {result['corrected_filled_simple_estimates']}")
                
                if len(period_results) > 5:
                    print(f"      ... 还有 {len(period_results) - 5} 个科目")
                
                all_results.extend(period_results)
            else:
                print(f"    ❌ 无法计算")
    
    print(f"\n2. 总体统计:")
    print("=" * 80)
    print(f"总计算记录数: {len(all_results):,}")
    
    # 按报表类型统计
    statement_stats = {}
    for result in all_results:
        stmt_type = result['statement_type']
        if stmt_type not in statement_stats:
            statement_stats[stmt_type] = 0
        statement_stats[stmt_type] += 1
    
    print(f"按报表类型分布:")
    for stmt_type, count in statement_stats.items():
        print(f"  {stmt_type}: {count:,}条记录")
    
    # 按股票统计
    stock_stats = {}
    for result in all_results:
        stock = result['stock_symbol']
        if stock not in stock_stats:
            stock_stats[stock] = 0
        stock_stats[stock] += 1
    
    print(f"\n按股票分布:")
    for stock, count in stock_stats.items():
        print(f"  {stock}: {count:,}条记录")
    
    # 按计算方法统计
    method_stats = {}
    for result in all_results:
        method = result['fill_method']
        if method not in method_stats:
            method_stats[method] = 0
        method_stats[method] += 1
    
    print(f"\n按计算方法分布:")
    for method, count in method_stats.items():
        print(f"  {method}: {count:,}条记录")
    
    return all_results

def show_sample_records(all_results, num_samples=10):
    """
    显示样本记录
    """
    print(f"\n3. 样本记录展示 (前{num_samples}条):")
    print("=" * 80)
    
    df_sample = pd.DataFrame(all_results[:num_samples])
    
    # 选择关键字段显示
    display_columns = [
        'stock_symbol', 'financial_period_absolute', 'statement_type', 
        'item_name', 'corrected_filled_simple_estimates', 'fill_method', 
        'confidence_score'
    ]
    
    if not df_sample.empty:
        print(df_sample[display_columns].to_string(index=False, max_colwidth=30))
    
    print(f"\n4. 计算详情示例:")
    print("-" * 40)
    
    for i, result in enumerate(all_results[:3]):
        print(f"\n示例 {i+1}:")
        print(f"  股票: {result['stock_symbol']}")
        print(f"  期间: {result['financial_period_absolute']}")
        print(f"  科目: {result['item_name']}")
        print(f"  计算: {result['calculation_details']}")
        print(f"  结果: {result['corrected_filled_simple_estimates']}")
        print(f"  置信度: {result['confidence_score']}")

def show_post_estimation_needs():
    """
    显示POST需要估算的情况
    """
    print(f"\n5. POST股票需要估算的情况:")
    print("=" * 80)
    
    post_missing = ['FY2023Q1', 'FY2023Q2', 'FY2023Q3', 'FY2024Q1', 'FY2024Q2', 'FY2024Q3']
    
    print(f"股票: POST")
    print(f"需要估算的期间: {', '.join(post_missing)}")
    print(f"原因: 不规律的报告时间表，缺乏对应的半年报/年报进行直接计算")
    print(f"建议: 使用滚动平滑法进行估算，置信度约70-80%")

def main():
    """
    主函数
    """
    print("正在生成最终结果...")
    
    try:
        # 显示所有计算结果
        all_results = show_all_final_results()
        
        # 显示样本记录
        show_sample_records(all_results)
        
        # 显示POST的估算需求
        show_post_estimation_needs()
        
        print(f"\n=== 最终结果总结 ===")
        print(f"✅ 直接计算记录: {len(all_results):,}条")
        print(f"✅ 涉及股票: 5只 (CCEP, MRP, VG, SIG, AU)")
        print(f"✅ 涉及期间: 11个缺失季度")
        print(f"✅ 平均置信度: 93-95%")
        print(f"⚠️ 需要估算: POST的6个期间")
        
    except Exception as e:
        print(f"❌ 生成结果时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

