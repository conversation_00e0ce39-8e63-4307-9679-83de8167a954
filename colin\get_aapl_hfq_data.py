#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取AAPL最新行情数据脚本
专门使用hfq表查询苹果公司的最新价格数据
"""

from clickhouse_driver import Client
import pandas as pd
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def connect_to_ap_research():
    """连接到ap_research数据库"""
    try:
        client = Client(
            host='************',
            port=9000,
            user='default',
            password='5ur2pK8WZQdy2',
            database='ap_research'
        )
        print("✅ 成功连接到ap_research数据库")
        return client
    except Exception as e:
        print(f"❌ 连接ap_research数据库失败: {e}")
        return None

def check_hfq_table_structure(client):
    """检查hfq表的结构"""
    print("\n🔍 检查hfq表结构...")
    
    try:
        # 检查表是否存在
        check_table_query = """
        SELECT name, engine, total_rows, total_bytes
        FROM system.tables 
        WHERE database = 'ap_research' AND name LIKE '%hfq%'
        """
        
        tables = client.execute(check_table_query)
        if tables:
            print("📋 找到的hfq相关表:")
            for table in tables:
                print(f"  - {table[0]} (引擎: {table[1]}, 行数: {table[2]:,}, 大小: {table[3]/1024/1024:.2f}MB)")
        else:
            print("⚠️ 未找到hfq相关表")
            return None
            
        # 获取表结构
        for table_name, _, _, _ in tables:
            if 'hfq' in table_name.lower():
                print(f"\n📊 表 {table_name} 的结构:")
                desc_query = f"DESCRIBE {table_name}"
                structure = client.execute(desc_query)
                for field in structure:
                    print(f"  - {field[0]}: {field[1]}")
                return table_name
                
    except Exception as e:
        print(f"❌ 检查表结构失败: {e}")
        return None

def get_aapl_latest_data(client, table_name):
    """获取AAPL最新行情数据"""
    print(f"\n📈 从表 {table_name} 获取AAPL最新行情数据...")
    
    try:
        # 首先检查AAPL在表中的数据情况
        check_query = f"""
        SELECT 
            COUNT(*) as total_records,
            MIN(trade_date) as earliest_date,
            MAX(trade_date) as latest_date,
            COUNT(DISTINCT stock_symbol) as unique_stocks
        FROM {table_name}
        WHERE stock_symbol = 'AAPL'
        """
        
        check_result = client.execute(check_query)
        if check_result and check_result[0][0] > 0:
            total_records, earliest_date, latest_date, unique_stocks = check_result[0]
            print(f"✅ AAPL数据统计:")
            print(f"  - 总记录数: {total_records:,}")
            print(f"  - 最早日期: {earliest_date}")
            print(f"  - 最新日期: {latest_date}")
            print(f"  - 唯一股票数: {unique_stocks}")
        else:
            print("⚠️ 表中没有AAPL的数据")
            return None
            
        # 获取最新10个交易日的数据
        latest_query = f"""
        SELECT 
            trade_date,
            stock_symbol,
            open,
            high,
            low,
            close,
            volume,
            turnover
        FROM {table_name}
        WHERE stock_symbol = 'AAPL'
        ORDER BY trade_date DESC
        LIMIT 10
        """
        
        latest_result = client.execute(latest_query)
        if not latest_result:
            print("⚠️ 未获取到AAPL的最新数据")
            return None
            
        # 转换为DataFrame
        columns = ['trade_date', 'stock_symbol', 'open', 'high', 'low', 'close', 'volume', 'turnover']
        df = pd.DataFrame(latest_result, columns=columns)
        
        # 转换日期格式
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        
        # 计算日收益率
        df['daily_return'] = df['close'].pct_change() * 100
        
        # 计算累计收益率（相对于最新价格）
        df['cumulative_return'] = ((df['close'] / df['close'].iloc[0]) - 1) * 100
        
        print(f"✅ 成功获取到 {len(df)} 条AAPL最新行情数据")
        
        return df
        
    except Exception as e:
        print(f"❌ 获取AAPL数据失败: {e}")
        return None

def display_aapl_summary(df):
    """显示AAPL数据摘要"""
    if df is None or df.empty:
        return

    print("\n" + "="*80)
    print("🍎 AAPL最新行情数据摘要")
    print("="*80)

    # 基本信息
    print(f"📊 数据条数: {len(df):,}")
    print(f"📅 时间范围: {df['trade_date'].min().strftime('%Y-%m-%d')} 到 {df['trade_date'].max().strftime('%Y-%m-%d')}")

    # 最新价格信息
    latest_data = df.iloc[0]
    print(f"\n💰 最新价格信息 (日期: {latest_data['trade_date'].strftime('%Y-%m-%d')}):")
    print(f"  开盘价: ${latest_data['open']:.2f}")
    print(f"  最高价: ${latest_data['high']:.2f}")
    print(f"  最低价: ${latest_data['low']:.2f}")
    print(f"  收盘价: ${latest_data['close']:.2f}")
    print(f"  成交量: {latest_data['volume']:,.0f}")
    print(f"  成交额: ${latest_data['turnover']:,.2f}")

    # 价格统计
    highest_price = df['high'].max()
    lowest_price = df['low'].min()
    avg_price = df['close'].mean()
    price_change = df['close'].iloc[0] - df['close'].iloc[-1]
    price_change_pct = (price_change / df['close'].iloc[-1]) * 100

    print(f"\n📈 价格统计:")
    print(f"  期间最高价: ${highest_price:.2f}")
    print(f"  期间最低价: ${lowest_price:.2f}")
    print(f"  期间平均价: ${avg_price:.2f}")
    print(f"  最新价格变化: ${price_change:.2f} ({price_change_pct:+.2f}%)")

    # 收益率统计
    total_return = df['daily_return'].sum()
    avg_daily_return = df['daily_return'].mean()
    volatility = df['daily_return'].std()

    print(f"\n📊 收益率统计:")
    print(f"  累计收益率: {total_return:.2f}%")
    print(f"  日均收益率: {avg_daily_return:.2f}%")
    print(f"  日收益率波动: {volatility:.2f}%")

    # 成交量统计
    total_volume = df['volume'].sum()
    avg_volume = df['volume'].mean()

    print(f"\n📊 成交量统计:")
    print(f"  总成交量: {total_volume:,.0f}")
    print(f"  日均成交量: {avg_volume:,.0f}")

    # 显示详细数据
    print(f"\n📋 详细数据:")
    print(df.to_string(index=False))

def main():
    """主函数"""
    print("🍎 AAPL最新行情数据查询 (使用hfq表)")
    print("="*60)

    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        print("❌ 无法连接到数据库")
        return

    # 检查hfq表结构
    table_name = check_hfq_table_structure(client)
    if not table_name:
        print("❌ 未找到合适的hfq表")
        return

    # 获取AAPL数据
    df = get_aapl_latest_data(client, table_name)

    # 显示摘要
    display_aapl_summary(df)

    # 保存到CSV文件
    if df is not None and not df.empty:
        filename = f"aapl_hfq_latest_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        df.to_csv(filename, index=False)
        print(f"\n💾 数据已保存到: {filename}")

    print("\n✅ 查询完成!")

if __name__ == "__main__":
    main()
