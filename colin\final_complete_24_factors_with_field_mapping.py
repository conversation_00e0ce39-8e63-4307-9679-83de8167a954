from clickhouse_driver import Client
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>

def connect_to_ap_research():
    """连接到ap_research数据库"""
    client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')
    print("✅ 连接ap_research数据库成功")
    return client

def get_stock_data(client, stock_symbol, calculation_date):
    """获取股票的基本面数据，确保按effective_date排序避免未来数据"""
    query = f"""
    SELECT
        stock_symbol,
        financial_period_absolute,
        period_end_date,
        item_name,
        value,
        statement_type,
        original_announcement_date_time,
        effective_date
    FROM fundamental_data_with_announcement_dates
    WHERE stock_symbol = '{stock_symbol}'
      AND statement_type IN ('income_statement', 'balance_sheet_history', 'cash_flow')
      AND financial_period_absolute REGEXP '^FY[0-9]{{4}}$'
      AND effective_date <= '{calculation_date}'
    ORDER BY effective_date ASC
    """

    result = client.execute(query)
    df = pd.DataFrame(result, columns=[
        'stock_symbol', 'financial_period_absolute', 'period_end_date',
        'item_name', 'value', 'statement_type', 'original_announcement_date_time', 'effective_date'
    ])

    # 如果effective_date为空，则使用announcement_date + 1天
    if df['effective_date'].isna().any():
        df.loc[df['effective_date'].isna(), 'effective_date'] = (
            pd.to_datetime(df.loc[df['effective_date'].isna(), 'original_announcement_date_time']) + 
            pd.Timedelta(days=1)
        )

    # 按effective_date排序，确保时间顺序正确
    df = df.sort_values('effective_date').reset_index(drop=True)

    return df

def get_price_data(client, stock_symbol, calculation_date):
    """获取股票的价格数据，确保使用最新可用数据（避免未来数据）"""
    # 日频数据的effective_date是trade_date + 1，所以这里用calculation_date - 1
    max_trade_date = (pd.to_datetime(calculation_date) - pd.Timedelta(days=1)).strftime('%Y-%m-%d')
    
    query = f"""
    SELECT 
        stock_symbol,
        trade_date,
        close,
        volume
    FROM priority_quality_stock_hfq
    WHERE stock_symbol = '{stock_symbol}'
      AND trade_date <= '{max_trade_date}'
    ORDER BY trade_date DESC
    LIMIT 1
    """
    
    result = client.execute(query)
    if result:
        return {
            'close_price': result[0][2],
            'volume': result[0][3],
            'trade_date': result[0][1]
        }
    return None

def get_historical_price_data(client, stock_symbol, calculation_date, lookback_days=2520):  # 10年历史
    """获取历史价格数据用于滚动计算，避免未来数据"""
    # 日频数据的effective_date是trade_date + 1，所以这里用calculation_date - 1
    max_trade_date = (pd.to_datetime(calculation_date) - pd.Timedelta(days=1)).strftime('%Y-%m-%d')
    start_date = (pd.to_datetime(calculation_date) - pd.Timedelta(days=lookback_days)).strftime('%Y-%m-%d')
    
    query = f"""
    SELECT 
        stock_symbol,
        trade_date,
        close,
        volume
    FROM priority_quality_stock_hfq
    WHERE stock_symbol = '{stock_symbol}'
      AND trade_date BETWEEN '{start_date}' AND '{max_trade_date}'
    ORDER BY trade_date ASC
    """
    
    result = client.execute(query)
    if result:
        df = pd.DataFrame(result, columns=['stock_symbol', 'trade_date', 'close', 'volume'])
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        return df
    return pd.DataFrame()

def find_longest_positive_range(values_list):
    """找到最长的连续正值范围用于CAGR计算"""
    if not values_list:
        return None, None, "no_data"
    
    # 找到所有正值的连续区间
    positive_ranges = []
    current_range = []
    
    for i, item in enumerate(values_list):
        if item['value'] > 0:
            current_range.append(item)
        else:
            if len(current_range) >= 2:  # 至少需要2个点计算CAGR
                positive_ranges.append(current_range)
            current_range = []
    
    # 添加最后一个区间
    if len(current_range) >= 2:
        positive_ranges.append(current_range)
    
    if not positive_ranges:
        # 如果没有连续正值区间，尝试找首尾正值
        positive_values = [item for item in values_list if item['value'] > 0]
        if len(positive_values) >= 2:
            first_positive = positive_values[0]
            last_positive = positive_values[-1]
            return first_positive, last_positive, "turnaround_growth"
        return None, None, "insufficient_positive_data"
    
    # 选择最长的区间
    longest_range = max(positive_ranges, key=len)
    return longest_range[0], longest_range[-1], "continuous_positive"

def get_field_with_priority(data, field_priority_list):
    """按优先级获取字段值，返回值和使用的字段优先级标识"""
    for i, field in enumerate(field_priority_list):
        value = data.get(field, 0) or 0
        if value != 0:
            priority = "PRIMARY" if i == 0 else f"P{i+1}"
            return value, priority
    return 0, "NA"

def get_dynamic_extreme_bounds(historical_values, factor_name, lower_percentile=1, upper_percentile=99):
    """动态计算极端值边界，基于每只股票每个因子的历史百分位数"""
    if not historical_values or len(historical_values) < 3:
        # 如果历史数据不足3个，使用默认边界
        if factor_name == 'cash_flow_coverage_ratio':
            return -100, 500
        elif factor_name == 'tech_premium':
            return 0, 80
        elif factor_name == 'patent_density':
            return 0, 100
        elif factor_name == 'ecosystem_cash_ratio':
            return 0, 100
        elif factor_name == 'fcf_quality':
            return -200, 200
        elif factor_name == 'roce_volatility':
            return 0, 50
        elif factor_name == 'roic':
            return -50, 100
        elif factor_name == 'profit_revenue_ratio':
            return -80, 80
        elif factor_name == 'fcf_per_share':
            return -100, 100
        elif factor_name == 'revenue_yoy':
            return -100, 500
        elif factor_name == 'revenue_cagr':
            return -50, 100
        elif factor_name == 'net_income_yoy':
            return -200, 500
        elif factor_name == 'fcf_cagr':
            return -50, 100
        elif factor_name == 'operating_margin':
            return -100, 100
        elif factor_name == 'operating_margin_std':
            return 0, 50
        elif factor_name == 'roic_cagr':
            return -100, 200
        elif factor_name == 'effective_tax_rate':
            return -50, 100
        elif factor_name == 'effective_tax_rate_improvement':
            return -50, 50
        elif factor_name == 'effective_tax_rate_std':
            return 0, 50
        elif factor_name == 'financial_health':
            return 0, 10
        elif factor_name == 'valuation_bubble_signal':
            return 0, 5
        elif factor_name == 'tech_gap_warning':
            return -100, 200
        elif factor_name == 'dynamic_safety_margin':
            return 0, 200
        elif factor_name == 'revenue_growth_continuity':
            return 0, 100
        else:
            return -100, 100
    
    # 过滤掉无效值
    valid_values = [v for v in historical_values if v is not None and not np.isnan(v)]
    
    if len(valid_values) < 3:
        # 如果有效数据不足，使用默认边界
        return get_dynamic_extreme_bounds([], factor_name, lower_percentile, upper_percentile)
    
    # 计算百分位数边界
    lower_bound = np.percentile(valid_values, lower_percentile)
    upper_bound = np.percentile(valid_values, upper_percentile)
    
    return lower_bound, upper_bound

def get_available_data_at_date(pivot_data, target_date):
    """获取指定日期之前的所有有效数据，用于滚动计算"""
    target_datetime = pd.to_datetime(target_date)
    
    # 过滤出effective_date <= target_date的数据
    available_data = pivot_data[pivot_data['effective_date'] <= target_datetime].copy()
    
    # 按effective_date排序
    available_data = available_data.sort_values('effective_date').reset_index(drop=True)
    
    return available_data

def calculate_rolling_statistics(data_series, window=3):
    """计算滚动统计量，用于稳定性指标"""
    if len(data_series) < window:
        return None
    
    # 计算滚动标准差
    rolling_std = []
    for i in range(window, len(data_series) + 1):
        window_data = data_series[i-window:i]
        if len(window_data) == window:
            rolling_std.append(np.std(window_data))
    
    return rolling_std if rolling_std else None

def calculate_complete_24_factors(client, stock_symbol, calculation_date=None):
    """计算完整字段映射的24个因子，传入计算日期避免未来数据"""
    
    # 如果没有传入计算日期，使用当前日期
    if calculation_date is None:
        calculation_date = datetime.now().strftime('%Y-%m-%d')
    
    print(f"\n🔄 开始计算 {stock_symbol} 在 {calculation_date} 的完整24因子...")
    
    # 获取数据，传入计算日期
    stock_data = get_stock_data(client, stock_symbol, calculation_date)
    price_data = get_price_data(client, stock_symbol, calculation_date)
    historical_price_data = get_historical_price_data(client, stock_symbol, calculation_date)
    
    if stock_data.empty:
        print(f"   ❌ {stock_symbol}: 无基本面数据")
        return None
    
    # 透视数据，保留effective_date信息
    pivot_data = stock_data.pivot_table(
        index=['financial_period_absolute', 'period_end_date', 'effective_date'],
        columns='item_name',
        values='value',
        aggfunc='first'
    ).reset_index()
    
    if pivot_data.empty:
        print(f"   ❌ {stock_symbol}: 数据透视失败")
        return None
    
    # 按effective_date排序，确保时间顺序正确
    pivot_data = pivot_data.sort_values('effective_date').reset_index(drop=True)
    
    # 获取最新数据（基于effective_date）
    latest_data = pivot_data.iloc[-1]
    fiscal_year = int(latest_data['financial_period_absolute'].replace('FY', ''))
    
    factors = {
        'stock_symbol': stock_symbol,
        'calculation_date': calculation_date,
        'fiscal_year': fiscal_year,
        'period_end_date': latest_data['period_end_date'],
        'effective_date': latest_data['effective_date']
    }
    
    # 定义字段映射
    field_mappings = {
        'revenue': [
            'Revenue from Business Activities - Total',
            'Revenue from Goods & Services'
        ],
        'net_income': [
            'Net Income (Loss) - Total',
            'Net Income - Total'
        ],
        'total_assets': [
            'Total Assets',
            'Total Assets - Total'
        ],
        'total_equity': [
            'Shareholders\' Equity - Attributable to Parent Shareholders - Total',
            'Common Equity Attributable to Parent Shareholders',
            'Common Equity - Total'
        ],
        'rd_expense': [
            'Research & Development Expense',
            'Research & Development Expense - Supplemental'
        ],
        'operating_cash_flow': [
            'Net Cash Flow from Operating Activities',
            'Net Cash Flow from Operating Activities - Total',
            'Cash Flow from Operations'
        ],
        'total_liabilities': [
            'Total Liabilities',
            'Total Liabilities & Shareholders Equity'
        ],
        'patents': [
            'Patents - Total',
            'Intellectual Property - Total'
        ],
        'capex': [
            'Capital Expenditure - Total',
            'Capital Expenditure'
        ],
        'depreciation': [
            'Depreciation and Amortization - Total',
            'Depreciation - Total'
        ],
        'interest_expense': [
            'Interest Expense - Total',
            'Interest Expense'
        ],
        'tax_expense': [
            'Income Tax Expense - Total',
            'Income Tax Expense'
        ]
    }
    
    # 1. tech_premium (R&D强度)
    try:
        # 收集历史R&D强度数据用于动态边界计算
        historical_tech_premium = []
        for i in range(min(10, len(pivot_data))):  # 最多取10年历史数据
            data = pivot_data.iloc[-(i+1)]
            rd_expense, _ = get_field_with_priority(data, field_mappings['rd_expense'])
            revenue, _ = get_field_with_priority(data, field_mappings['revenue'])
            
            if revenue > 0:
                tech_premium = (rd_expense / revenue) * 100
                historical_tech_premium.append(tech_premium)
        
        rd_expense, rd_priority = get_field_with_priority(latest_data, field_mappings['rd_expense'])
        revenue, revenue_priority = get_field_with_priority(latest_data, field_mappings['revenue'])
        
        if revenue > 0:
            tech_premium = (rd_expense / revenue) * 100
            
            # 使用动态边界进行极端值处理
            lower_bound, upper_bound = get_dynamic_extreme_bounds(
                historical_tech_premium, 'tech_premium'
            )
            factors['tech_premium'] = np.clip(tech_premium, lower_bound, upper_bound)
            factors['tech_premium_field_flag'] = f"RD:{rd_priority},REV:{revenue_priority},BOUNDS:({lower_bound:.1f},{upper_bound:.1f})"
        else:
            factors['tech_premium'] = None
            factors['tech_premium_field_flag'] = "NA"
    except:
        factors['tech_premium'] = None
        factors['tech_premium_field_flag'] = "NA"
    
    # 2. tech_gap_warning (R&D强度变化率)
    try:
        # 获取计算日期之前的所有有效数据
        available_data = get_available_data_at_date(pivot_data, calculation_date)
        
        if len(available_data) >= 2:
            current_data = available_data.iloc[-1]
            prev_data = available_data.iloc[-2]
            
            current_rd, _ = get_field_with_priority(current_data, field_mappings['rd_expense'])
            current_revenue, _ = get_field_with_priority(current_data, field_mappings['revenue'])
            prev_rd, _ = get_field_with_priority(prev_data, field_mappings['rd_expense'])
            prev_revenue, _ = get_field_with_priority(prev_data, field_mappings['revenue'])
            
            if prev_revenue > 0 and current_revenue > 0:
                prev_tech_premium = (prev_rd / prev_revenue) * 100
                current_tech_premium = (current_rd / current_revenue) * 100
                
                if prev_tech_premium > 0:
                    change_rate = (current_tech_premium - prev_tech_premium) / prev_tech_premium * 100
                    
                    # 收集历史变化率数据用于动态边界计算
                    historical_change_rates = []
                    for i in range(2, min(10, len(available_data))):
                        if i < len(available_data):
                            curr_data = available_data.iloc[-(i)]
                            prev_data = available_data.iloc[-(i+1)]
                            
                            curr_rd, _ = get_field_with_priority(curr_data, field_mappings['rd_expense'])
                            curr_rev, _ = get_field_with_priority(curr_data, field_mappings['revenue'])
                            prev_rd, _ = get_field_with_priority(prev_data, field_mappings['rd_expense'])
                            prev_rev, _ = get_field_with_priority(prev_data, field_mappings['revenue'])
                            
                            if prev_rev > 0 and curr_rev > 0:
                                prev_tp = (prev_rd / prev_rev) * 100
                                curr_tp = (curr_rd / curr_rev) * 100
                                if prev_tp > 0:
                                    rate = (curr_tp - prev_tp) / prev_tp * 100
                                    historical_change_rates.append(rate)
                    
                    # 使用动态边界进行极端值处理
                    lower_bound, upper_bound = get_dynamic_extreme_bounds(
                        historical_change_rates, 'tech_gap_warning'
                    )
                    factors['tech_gap_warning'] = np.clip(change_rate, lower_bound, upper_bound)
                else:
                    factors['tech_gap_warning'] = None
            else:
                factors['tech_gap_warning'] = None
        else:
            factors['tech_gap_warning'] = None
    except:
        factors['tech_gap_warning'] = None
    
    # 3. patent_density (专利密度)
    try:
        # 收集历史专利密度数据用于动态边界计算
        historical_patent_density = []
        for i in range(min(10, len(pivot_data))):  # 最多取10年历史数据
            data = pivot_data.iloc[-(i+1)]
            intangible_assets, _ = get_field_with_priority(data, field_mappings['intangible_assets'])
            revenue, _ = get_field_with_priority(data, field_mappings['revenue'])
            
            if revenue > 0:
                patent_density = (intangible_assets / revenue) * 100
                historical_patent_density.append(patent_density)
        
        intangible_assets, intangible_priority = get_field_with_priority(latest_data, field_mappings['intangible_assets'])
        revenue, revenue_priority = get_field_with_priority(latest_data, field_mappings['revenue'])
        
        if revenue > 0:
            patent_density = (intangible_assets / revenue) * 100
            
            # 使用动态边界进行极端值处理
            lower_bound, upper_bound = get_dynamic_extreme_bounds(
                historical_patent_density, 'patent_density'
            )
            factors['patent_density'] = np.clip(patent_density, lower_bound, upper_bound)
            factors['patent_density_field_flag'] = f"INT:{intangible_priority},REV:{revenue_priority},BOUNDS:({lower_bound:.1f},{upper_bound:.1f})"
        else:
            factors['patent_density'] = None
            factors['patent_density_field_flag'] = "NA"
    except:
        factors['patent_density'] = None
        factors['patent_density_field_flag'] = "NA"
    
    # 4. ecosystem_cash_ratio (现金生态比率)
    try:
        # 收集历史现金生态比率数据用于动态边界计算
        historical_cash_ratio = []
        for i in range(min(10, len(pivot_data))):  # 最多取10年历史数据
            data = pivot_data.iloc[-(i+1)]
            cash, _ = get_field_with_priority(data, field_mappings['cash'])
            total_assets, _ = get_field_with_priority(data, field_mappings['total_assets'])
            
            if total_assets > 0:
                cash_ratio = (cash / total_assets) * 100
                historical_cash_ratio.append(cash_ratio)
        
        cash, cash_priority = get_field_with_priority(latest_data, field_mappings['cash'])
        total_assets, asset_priority = get_field_with_priority(latest_data, field_mappings['total_assets'])
        
        if total_assets > 0:
            cash_ratio = (cash / total_assets) * 100
            
            # 使用动态边界进行极端值处理
            lower_bound, upper_bound = get_dynamic_extreme_bounds(
                historical_cash_ratio, 'ecosystem_cash_ratio'
            )
            factors['ecosystem_cash_ratio'] = np.clip(cash_ratio, lower_bound, upper_bound)
            factors['ecosystem_cash_ratio_field_flag'] = f"CASH:{cash_priority},ASSET:{asset_priority},BOUNDS:({lower_bound:.1f},{upper_bound:.1f})"
        else:
            factors['ecosystem_cash_ratio'] = None
            factors['ecosystem_cash_ratio_field_flag'] = "NA"
    except:
        factors['ecosystem_cash_ratio'] = None
        factors['ecosystem_cash_ratio_field_flag'] = "NA"
    
    # 5. cash_flow_coverage_ratio (现金流覆盖率) - 新增因子，使用动态极端值处理
    try:
        # 收集历史现金流覆盖率数据用于动态边界计算
        historical_cash_flow_coverage = []
        for i in range(min(10, len(pivot_data))):  # 最多取10年历史数据
            data = pivot_data.iloc[-(i+1)]
            ocf, _ = get_field_with_priority(data, field_mappings['operating_cash_flow'])
            liab, _ = get_field_with_priority(data, field_mappings['total_liabilities'])
            
            if liab > 0:
                coverage = (ocf / liab) * 100
                historical_cash_flow_coverage.append(coverage)
        
        # 计算当前现金流覆盖率
        operating_cash_flow, ocf_priority = get_field_with_priority(latest_data, field_mappings['operating_cash_flow'])
        total_liabilities, liab_priority = get_field_with_priority(latest_data, field_mappings['total_liabilities'])
        
        if total_liabilities > 0:
            cash_flow_coverage = (operating_cash_flow / total_liabilities) * 100
            
            # 使用动态边界进行极端值处理
            lower_bound, upper_bound = get_dynamic_extreme_bounds(
                historical_cash_flow_coverage, 'cash_flow_coverage_ratio'
            )
            factors['cash_flow_coverage_ratio'] = np.clip(cash_flow_coverage, lower_bound, upper_bound)
            factors['cash_flow_coverage_ratio_field_flag'] = f"OCF:{ocf_priority},LIAB:{liab_priority},BOUNDS:({lower_bound:.1f},{upper_bound:.1f})"
        else:
            factors['cash_flow_coverage_ratio'] = None
            factors['cash_flow_coverage_ratio_field_flag'] = "NA"
    except:
        factors['cash_flow_coverage_ratio'] = None
        factors['cash_flow_coverage_ratio_field_flag'] = "NA"
    
    # 6. fcf_quality (FCF质量)
    try:
        # 收集历史FCF质量数据用于动态边界计算
        historical_fcf_quality = []
        for i in range(min(10, len(pivot_data))):  # 最多取10年历史数据
            data = pivot_data.iloc[-(i+1)]
            fcf, _ = get_field_with_priority(data, field_mappings['free_cash_flow'])
            net_income, _ = get_field_with_priority(data, field_mappings['net_income'])
            
            if net_income != 0:
                fcf_quality = fcf / net_income
                historical_fcf_quality.append(fcf_quality)
        
        fcf, fcf_priority = get_field_with_priority(latest_data, field_mappings['free_cash_flow'])
        net_income, ni_priority = get_field_with_priority(latest_data, field_mappings['net_income'])
        
        if net_income != 0:
            fcf_quality = fcf / net_income
            
            # 使用动态边界进行极端值处理
            lower_bound, upper_bound = get_dynamic_extreme_bounds(
                historical_fcf_quality, 'fcf_quality'
            )
            factors['fcf_quality'] = np.clip(fcf_quality, lower_bound, upper_bound)
            factors['fcf_quality_field_flag'] = f"FCF:{fcf_priority},NI:{ni_priority},BOUNDS:({lower_bound:.1f},{upper_bound:.1f})"
        else:
            factors['fcf_quality'] = None
            factors['fcf_quality_field_flag'] = "NA"
    except:
        factors['fcf_quality'] = None
        factors['fcf_quality_field_flag'] = "NA"
    
    # 7. dynamic_safety_margin (动态安全边际)
    try:
        if factors.get('tech_premium') is not None and factors.get('ecosystem_cash_ratio') is not None:
            safety_margin = factors['tech_premium'] + factors['ecosystem_cash_ratio']
            
            # 收集历史安全边际数据用于动态边界计算
            historical_safety_margin = []
            for i in range(min(10, len(pivot_data))):  # 最多取10年历史数据
                data = pivot_data.iloc[-(i+1)]
                rd_expense, _ = get_field_with_priority(data, field_mappings['rd_expense'])
                revenue, _ = get_field_with_priority(data, field_mappings['revenue'])
                cash, _ = get_field_with_priority(data, field_mappings['cash'])
                total_assets, _ = get_field_with_priority(data, field_mappings['total_assets'])
                
                if revenue > 0 and total_assets > 0:
                    tech_premium_hist = (rd_expense / revenue) * 100
                    cash_ratio_hist = (cash / total_assets) * 100
                    margin = tech_premium_hist + cash_ratio_hist
                    historical_safety_margin.append(margin)
            
            # 使用动态边界进行极端值处理
            lower_bound, upper_bound = get_dynamic_extreme_bounds(
                historical_safety_margin, 'dynamic_safety_margin'
            )
            factors['dynamic_safety_margin'] = np.clip(safety_margin, lower_bound, upper_bound)
        else:
            factors['dynamic_safety_margin'] = None
    except:
        factors['dynamic_safety_margin'] = None
    
    # 8. revenue_growth_continuity (收入增长连续性)
    try:
        if len(pivot_data) >= 3:
            recent_years = min(3, len(pivot_data))
            revenues = []
            for i in range(recent_years):
                data = pivot_data.iloc[-(i+1)]
                revenue, _ = get_field_with_priority(data, field_mappings['revenue'])
                revenues.append(revenue)
            
            revenues.reverse()
            
            if all(r > 0 for r in revenues):
                growth_rates = []
                for i in range(1, len(revenues)):
                    growth_rate = (revenues[i] - revenues[i-1]) / revenues[i-1] * 100
                    growth_rates.append(growth_rate)
                
                if growth_rates:
                    growth_continuity = np.std(growth_rates)
                    
                    # 收集历史收入增长连续性数据用于动态边界计算
                    historical_growth_continuity = []
                    for i in range(3, min(8, len(pivot_data))):
                        if i < len(pivot_data):
                            window_revenues = []
                            for j in range(i-2, i+1):
                                if j < len(pivot_data):
                                    data = pivot_data.iloc[-(j+1)]
                                    revenue, _ = get_field_with_priority(data, field_mappings['revenue'])
                                    window_revenues.append(revenue)
                            
                            if len(window_revenues) >= 3 and all(r > 0 for r in window_revenues):
                                window_growth_rates = []
                                for k in range(1, len(window_revenues)):
                                    growth_rate = (window_revenues[k] - window_revenues[k-1]) / window_revenues[k-1] * 100
                                    window_growth_rates.append(growth_rate)
                                
                                if window_growth_rates:
                                    continuity = np.std(window_growth_rates)
                                    historical_growth_continuity.append(continuity)
                    
                    # 使用动态边界进行极端值处理
                    lower_bound, upper_bound = get_dynamic_extreme_bounds(
                        historical_growth_continuity, 'revenue_growth_continuity'
                    )
                    factors['revenue_growth_continuity'] = np.clip(growth_continuity, lower_bound, upper_bound)
                else:
                    factors['revenue_growth_continuity'] = None
            else:
                factors['revenue_growth_continuity'] = None
        else:
            factors['revenue_growth_continuity'] = None
    except:
        factors['revenue_growth_continuity'] = None
    
    # 9. effective_tax_rate_improvement (有效税率改善)
    try:
        # 获取计算日期之前的所有有效数据
        available_data = get_available_data_at_date(pivot_data, calculation_date)
        
        if len(available_data) >= 2:
            current_data = available_data.iloc[-1]
            prev_data = available_data.iloc[-2]
            
            current_ebit, ebit_priority = get_field_with_priority(current_data, field_mappings['ebit'])
            current_tax, tax_priority = get_field_with_priority(current_data, field_mappings['income_taxes'])
            
            prev_ebit, _ = get_field_with_priority(prev_data, field_mappings['ebit'])
            prev_tax, _ = get_field_with_priority(prev_data, field_mappings['income_taxes'])
            
            if current_ebit > 0 and prev_ebit > 0:
                current_tax_rate = (current_tax / current_ebit) * 100
                prev_tax_rate = (prev_tax / prev_ebit) * 100
                
                if -50 <= current_tax_rate <= 100 and -50 <= prev_tax_rate <= 100:
                    improvement = current_tax_rate - prev_tax_rate
                    
                    # 收集历史税率改善数据用于动态边界计算
                    historical_tax_improvement = []
                    for i in range(2, min(10, len(available_data))):
                        if i < len(available_data):
                            curr_data = available_data.iloc[-(i)]
                            prev_data = available_data.iloc[-(i+1)]
                            curr_ebit, _ = get_field_with_priority(curr_data, field_mappings['ebit'])
                            curr_tax, _ = get_field_with_priority(curr_data, field_mappings['income_taxes'])
                            prev_ebit, _ = get_field_with_priority(prev_data, field_mappings['ebit'])
                            prev_tax, _ = get_field_with_priority(prev_data, field_mappings['income_taxes'])
                            
                            if curr_ebit > 0 and prev_ebit > 0:
                                curr_rate = (curr_tax / curr_ebit) * 100
                                prev_rate = (prev_tax / prev_ebit) * 100
                                if -50 <= curr_rate <= 100 and -50 <= prev_rate <= 100:
                                    imp = curr_rate - prev_rate
                                    historical_tax_improvement.append(imp)
                    
                    # 使用动态边界进行极端值处理
                    lower_bound, upper_bound = get_dynamic_extreme_bounds(
                        historical_tax_improvement, 'effective_tax_rate_improvement'
                    )
                    factors['effective_tax_rate_improvement'] = np.clip(improvement, lower_bound, upper_bound)
                    factors['effective_tax_rate_improvement_field_flag'] = f"EBIT:{ebit_priority},TAX:{tax_priority},BOUNDS:({lower_bound:.1f},{upper_bound:.1f})"
                else:
                    factors['effective_tax_rate_improvement'] = None
                    factors['effective_tax_rate_improvement_field_flag'] = "NA"
            else:
                factors['effective_tax_rate_improvement'] = None
                factors['effective_tax_rate_improvement_field_flag'] = "NA"
        else:
            factors['effective_tax_rate_improvement'] = None
            factors['effective_tax_rate_improvement_field_flag'] = "NA"
    except:
        factors['effective_tax_rate_improvement'] = None
        factors['effective_tax_rate_improvement_field_flag'] = "NA"
    
    # 10. financial_health (财务健康度)
    try:
        # 收集历史财务健康度数据用于动态边界计算
        historical_financial_health = []
        for i in range(min(10, len(pivot_data))):  # 最多取10年历史数据
            data = pivot_data.iloc[-(i+1)]
            current_assets, _ = get_field_with_priority(data, field_mappings['current_assets'])
            current_liabilities, _ = get_field_with_priority(data, field_mappings['current_liabilities'])
            
            if current_liabilities > 0:
                current_ratio = current_assets / current_liabilities
                historical_financial_health.append(current_ratio)
        
        current_assets, ca_priority = get_field_with_priority(latest_data, field_mappings['current_assets'])
        current_liabilities, cl_priority = get_field_with_priority(latest_data, field_mappings['current_liabilities'])
        
        if current_liabilities > 0:
            current_ratio = current_assets / current_liabilities
            
            # 使用动态边界进行极端值处理
            lower_bound, upper_bound = get_dynamic_extreme_bounds(
                historical_financial_health, 'financial_health'
            )
            factors['financial_health'] = np.clip(current_ratio, lower_bound, upper_bound)
            factors['financial_health_field_flag'] = f"CA:{ca_priority},CL:{cl_priority},BOUNDS:({lower_bound:.1f},{upper_bound:.1f})"
        else:
            factors['financial_health'] = None
            factors['financial_health_field_flag'] = "NA"
    except:
        factors['financial_health'] = None
        factors['financial_health_field_flag'] = "NA"
    
    # 11. valuation_bubble_signal (估值泡沫信号)
    try:
        net_income, ni_priority = get_field_with_priority(latest_data, field_mappings['net_income'])
        shares, share_priority = get_field_with_priority(latest_data, field_mappings['shares'])
        
        if net_income > 0 and shares > 0 and price_data:
            eps = net_income / shares
            pe_ratio = price_data['close_price'] / eps
            
            if len(pivot_data) >= 3:
                revenues = []
                for i in range(min(5, len(pivot_data))):
                    data = pivot_data.iloc[-(i+1)]
                    revenue, _ = get_field_with_priority(data, field_mappings['revenue'])
                    if revenue > 0:
                        revenues.append({'year': len(pivot_data) - i, 'value': revenue})
                
                revenues.reverse()
                
                if len(revenues) >= 2:
                    start_revenue = revenues[0]['value']
                    end_revenue = revenues[-1]['value']
                    years = len(revenues) - 1
                    
                    if start_revenue > 0:
                        revenue_cagr = (pow(end_revenue / start_revenue, 1 / years) - 1) * 100
                        
                        if revenue_cagr > 0:
                            peg_ratio = pe_ratio / revenue_cagr
                            
                            # 收集历史PEG比率数据用于动态边界计算
                            historical_peg_ratios = []
                            for i in range(min(5, len(pivot_data))):
                                if i < len(pivot_data):
                                    data = pivot_data.iloc[-(i+1)]
                                    ni, _ = get_field_with_priority(data, field_mappings['net_income'])
                                    rev, _ = get_field_with_priority(data, field_mappings['revenue'])
                                    if ni > 0 and rev > 0:
                                        eps = ni / shares
                                        if eps > 0:
                                            pe = price_data['close_price'] / eps
                                            # 简化计算，使用收入增长率作为代理
                                            if i > 0:
                                                prev_rev = pivot_data.iloc[-(i+2)]['revenue'] if i+2 < len(pivot_data) else rev
                                                if prev_rev > 0:
                                                    growth = (rev - prev_rev) / prev_rev * 100
                                                    if growth > 0:
                                                        peg = pe / growth
                                                        historical_peg_ratios.append(peg)
                            
                            # 使用动态边界进行极端值处理
                            lower_bound, upper_bound = get_dynamic_extreme_bounds(
                                historical_peg_ratios, 'valuation_bubble_signal'
                            )
                            factors['valuation_bubble_signal'] = np.clip(peg_ratio, lower_bound, upper_bound)
                            factors['valuation_bubble_signal_field_flag'] = f"NI:{ni_priority},SHARE:{share_priority},BOUNDS:({lower_bound:.1f},{upper_bound:.1f})"
                        else:
                            factors['valuation_bubble_signal'] = None
                            factors['valuation_bubble_signal_field_flag'] = "NA"
                    else:
                        factors['valuation_bubble_signal'] = None
                        factors['valuation_bubble_signal_field_flag'] = "NA"
                else:
                    factors['valuation_bubble_signal'] = None
                    factors['valuation_bubble_signal_field_flag'] = "NA"
            else:
                factors['valuation_bubble_signal'] = None
                factors['valuation_bubble_signal_field_flag'] = "NA"
        else:
            factors['valuation_bubble_signal'] = None
            factors['valuation_bubble_signal_field_flag'] = "NA"
    except:
        factors['valuation_bubble_signal'] = None
        factors['valuation_bubble_signal_field_flag'] = "NA"
    
    # 12-24. 其他因子（继续使用相同的字段映射模式）
    
    # 12. roce_volatility (ROCE波动性) - 重命名，明确负向性
    try:
        if len(pivot_data) >= 3:
            roce_values = []
            for i in range(min(5, len(pivot_data))):
                data = pivot_data.iloc[-(i+1)]
                net_income, _ = get_field_with_priority(data, field_mappings['net_income'])
                debt, _ = get_field_with_priority(data, field_mappings['debt'])
                total_equity, _ = get_field_with_priority(data, field_mappings['equity'])
                capital_employed = debt + total_equity
                
                if capital_employed > 0:
                    roce = (net_income / capital_employed) * 100
                    roce_values.append(roce)
            
            if len(roce_values) >= 3:
                roce_volatility = np.std(roce_values)
                
                # 收集历史ROCE波动性数据用于动态边界计算
                historical_roce_volatility = []
                for i in range(3, min(8, len(pivot_data))):
                    if i < len(pivot_data):
                        window_roce = []
                        for j in range(i-2, i+1):
                            if j < len(pivot_data):
                                data = pivot_data.iloc[-(j+1)]
                                ni, _ = get_field_with_priority(data, field_mappings['net_income'])
                                debt, _ = get_field_with_priority(data, field_mappings['debt'])
                                equity, _ = get_field_with_priority(data, field_mappings['equity'])
                                capital = debt + equity
                                if capital > 0:
                                    roce = (ni / capital) * 100
                                    window_roce.append(roce)
                        if len(window_roce) >= 3:
                            vol = np.std(window_roce)
                            historical_roce_volatility.append(vol)
                
                # 使用动态边界进行极端值处理
                lower_bound, upper_bound = get_dynamic_extreme_bounds(
                    historical_roce_volatility, 'roce_volatility'
                )
                factors['roce_volatility'] = np.clip(roce_volatility, lower_bound, upper_bound)
            else:
                factors['roce_volatility'] = None
        else:
            factors['roce_volatility'] = None
    except:
        factors['roce_volatility'] = None
    
    # 13. revenue_yoy (收入同比增长)
    try:
        # 获取计算日期之前的所有有效数据
        available_data = get_available_data_at_date(pivot_data, calculation_date)
        
        if len(available_data) >= 2:
            current_data = available_data.iloc[-1]
            prev_data = available_data.iloc[-2]
            
            current_revenue, _ = get_field_with_priority(current_data, field_mappings['revenue'])
            prev_revenue, _ = get_field_with_priority(prev_data, field_mappings['revenue'])
            
            if prev_revenue > 0:
                revenue_yoy = (current_revenue - prev_revenue) / prev_revenue * 100
                
                # 收集历史收入同比增长数据用于动态边界计算
                historical_revenue_yoy = []
                for i in range(2, min(10, len(available_data))):
                    if i < len(available_data):
                        curr_data = available_data.iloc[-(i)]
                        prev_data = available_data.iloc[-(i+1)]
                        curr_rev, _ = get_field_with_priority(curr_data, field_mappings['revenue'])
                        prev_rev, _ = get_field_with_priority(prev_data, field_mappings['revenue'])
                        if prev_rev > 0:
                            yoy = (curr_rev - prev_rev) / prev_rev * 100
                            historical_revenue_yoy.append(yoy)
                
                # 使用动态边界进行极端值处理
                lower_bound, upper_bound = get_dynamic_extreme_bounds(
                    historical_revenue_yoy, 'revenue_yoy'
                )
                factors['revenue_yoy'] = np.clip(revenue_yoy, lower_bound, upper_bound)
            else:
                factors['revenue_yoy'] = None
        else:
            factors['revenue_yoy'] = None
    except:
        factors['revenue_yoy'] = None
    
    # 14. revenue_cagr (收入CAGR)
    try:
        revenues = []
        for i, row in pivot_data.iterrows():
            revenue, _ = get_field_with_priority(row, field_mappings['revenue'])
            fiscal_year = int(row['financial_period_absolute'].replace('FY', ''))
            if revenue > 0:
                revenues.append({'year': fiscal_year, 'value': revenue})
        
        if len(revenues) >= 2:
            start_revenue, end_revenue, calculation_type = find_longest_positive_range(revenues)
            
            if start_revenue and end_revenue:
                years = end_revenue['year'] - start_revenue['year']
                if years > 0:
                    if calculation_type == "turnaround_growth":
                        revenue_cagr = (end_revenue['value'] - start_revenue['value']) / start_revenue['value'] / years * 100
                    else:
                        revenue_cagr = (pow(end_revenue['value'] / start_revenue['value'], 1 / years) - 1) * 100
                    
                    # 收集历史收入CAGR数据用于动态边界计算
                    historical_revenue_cagr = []
                    for start_idx in range(len(revenues)-1):
                        for end_idx in range(start_idx+1, len(revenues)):
                            start_r = revenues[start_idx]
                            end_r = revenues[end_idx]
                            years_diff = end_r['year'] - start_r['year']
                            if years_diff > 0 and start_r['value'] > 0:
                                cagr = (pow(end_r['value'] / start_r['value'], 1 / years_diff) - 1) * 100
                                historical_revenue_cagr.append(cagr)
                    
                    # 使用动态边界进行极端值处理
                    lower_bound, upper_bound = get_dynamic_extreme_bounds(
                        historical_revenue_cagr, 'revenue_cagr'
                    )
                    factors['revenue_cagr'] = np.clip(revenue_cagr, lower_bound, upper_bound)
                else:
                    factors['revenue_cagr'] = None
            else:
                factors['revenue_cagr'] = None
        else:
            factors['revenue_cagr'] = None
    except:
        factors['revenue_cagr'] = None
    
    # 15. net_income_yoy (净利润同比增长)
    try:
        # 获取计算日期之前的所有有效数据
        available_data = get_available_data_at_date(pivot_data, calculation_date)
        
        if len(available_data) >= 2:
            current_data = available_data.iloc[-1]
            prev_data = available_data.iloc[-2]
            
            current_ni, _ = get_field_with_priority(current_data, field_mappings['net_income'])
            prev_ni, _ = get_field_with_priority(prev_data, field_mappings['net_income'])
            
            if prev_ni != 0:
                ni_yoy = (current_ni - prev_ni) / abs(prev_ni) * 100
                
                # 收集历史净利润同比增长数据用于动态边界计算
                historical_ni_yoy = []
                for i in range(2, min(10, len(available_data))):
                    if i < len(available_data):
                        curr_data = available_data.iloc[-(i)]
                        prev_data = available_data.iloc[-(i+1)]
                        curr_ni, _ = get_field_with_priority(curr_data, field_mappings['net_income'])
                        prev_ni, _ = get_field_with_priority(prev_data, field_mappings['net_income'])
                        if prev_ni != 0:
                            yoy = (curr_ni - prev_ni) / abs(prev_ni) * 100
                            historical_ni_yoy.append(yoy)
                
                # 使用动态边界进行极端值处理
                lower_bound, upper_bound = get_dynamic_extreme_bounds(
                    historical_ni_yoy, 'net_income_yoy'
                )
                factors['net_income_yoy'] = np.clip(ni_yoy, lower_bound, upper_bound)
            else:
                factors['net_income_yoy'] = None
        else:
            factors['net_income_yoy'] = None
    except:
        factors['net_income_yoy'] = None
    
    # 16. profit_revenue_ratio (利润收入比)
    try:
        net_income, ni_priority = get_field_with_priority(latest_data, field_mappings['net_income'])
        revenue, revenue_priority = get_field_with_priority(latest_data, field_mappings['revenue'])
        
        if revenue > 0:
            profit_margin = (net_income / revenue) * 100
            
            # 收集历史利润收入比数据用于动态边界计算
            historical_profit_margin = []
            for i in range(min(10, len(pivot_data))):  # 最多取10年历史数据
                data = pivot_data.iloc[-(i+1)]
                ni, _ = get_field_with_priority(data, field_mappings['net_income'])
                rev, _ = get_field_with_priority(data, field_mappings['revenue'])
                
                if rev > 0:
                    margin = (ni / rev) * 100
                    historical_profit_margin.append(margin)
            
            # 使用动态边界进行极端值处理
            lower_bound, upper_bound = get_dynamic_extreme_bounds(
                historical_profit_margin, 'profit_revenue_ratio'
            )
            factors['profit_revenue_ratio'] = np.clip(profit_margin, lower_bound, upper_bound)
            factors['profit_revenue_ratio_field_flag'] = f"NI:{ni_priority},REV:{revenue_priority},BOUNDS:({lower_bound:.1f},{upper_bound:.1f})"
        else:
            factors['profit_revenue_ratio'] = None
            factors['profit_revenue_ratio_field_flag'] = "NA"
    except:
        factors['profit_revenue_ratio'] = None
        factors['profit_revenue_ratio_field_flag'] = "NA"
    
    # 17. fcf_per_share (每股自由现金流)
    try:
        fcf, fcf_priority = get_field_with_priority(latest_data, field_mappings['free_cash_flow'])
        shares, share_priority = get_field_with_priority(latest_data, field_mappings['shares'])
        
        if shares > 0:
            fcf_per_share = fcf / shares
            
            # 收集历史每股自由现金流数据用于动态边界计算
            historical_fcf_per_share = []
            for i in range(min(10, len(pivot_data))):  # 最多取10年历史数据
                data = pivot_data.iloc[-(i+1)]
                fcf_hist, _ = get_field_with_priority(data, field_mappings['free_cash_flow'])
                shares_hist, _ = get_field_with_priority(data, field_mappings['shares'])
                
                if shares_hist > 0:
                    fcf_ps = fcf_hist / shares_hist
                    historical_fcf_per_share.append(fcf_ps)
            
            # 使用动态边界进行极端值处理
            lower_bound, upper_bound = get_dynamic_extreme_bounds(
                historical_fcf_per_share, 'fcf_per_share'
            )
            factors['fcf_per_share'] = np.clip(fcf_per_share, lower_bound, upper_bound)
            factors['fcf_per_share_field_flag'] = f"FCF:{fcf_priority},SHARE:{share_priority},BOUNDS:({lower_bound:.1f},{upper_bound:.1f})"
        else:
            factors['fcf_per_share'] = None
            factors['fcf_per_share_field_flag'] = "NA"
    except:
        factors['fcf_per_share'] = None
        factors['fcf_per_share_field_flag'] = "NA"
    
    # 18. fcf_cagr (自由现金流CAGR)
    try:
        fcf_values = []
        for i, row in pivot_data.iterrows():
            fcf, _ = get_field_with_priority(row, field_mappings['free_cash_flow'])
            fiscal_year = int(row['financial_period_absolute'].replace('FY', ''))
            fcf_values.append({'year': fiscal_year, 'value': fcf})
        
        if len(fcf_values) >= 2:
            start_fcf, end_fcf, calculation_type = find_longest_positive_range(fcf_values)
            
            if start_fcf and end_fcf:
                years = end_fcf['year'] - start_fcf['year']
                if years > 0:
                    if calculation_type == "turnaround_growth":
                        fcf_cagr = (end_fcf['value'] - start_fcf['value']) / abs(start_fcf['value']) / years * 100
                    else:
                        fcf_cagr = (pow(end_fcf['value'] / start_fcf['value'], 1 / years) - 1) * 100
                    
                    # 收集历史FCF CAGR数据用于动态边界计算
                    historical_fcf_cagr = []
                    for start_idx in range(len(fcf_values)-1):
                        for end_idx in range(start_idx+1, len(fcf_values)):
                            start_f = fcf_values[start_idx]
                            end_f = fcf_values[end_idx]
                            years_diff = end_f['year'] - start_f['year']
                            if years_diff > 0 and start_f['value'] != 0:
                                if start_f['value'] > 0:
                                    cagr = (pow(end_f['value'] / start_f['value'], 1 / years_diff) - 1) * 100
                                else:
                                    cagr = (end_f['value'] - start_f['value']) / abs(start_f['value']) / years_diff * 100
                                historical_fcf_cagr.append(cagr)
                    
                    # 使用动态边界进行极端值处理
                    lower_bound, upper_bound = get_dynamic_extreme_bounds(
                        historical_fcf_cagr, 'fcf_cagr'
                    )
                    factors['fcf_cagr'] = np.clip(fcf_cagr, lower_bound, upper_bound)
                else:
                    factors['fcf_cagr'] = None
            else:
                factors['fcf_cagr'] = None
        else:
            factors['fcf_cagr'] = None
    except:
        factors['fcf_cagr'] = None
    
    # 19. operating_margin (营业利润率)
    try:
        operating_income, oi_priority = get_field_with_priority(latest_data, field_mappings['operating_income'])
        revenue, revenue_priority = get_field_with_priority(latest_data, field_mappings['revenue'])
        
        if revenue > 0:
            operating_margin = (operating_income / revenue) * 100
            
            # 收集历史营业利润率数据用于动态边界计算
            historical_operating_margin = []
            for i in range(min(10, len(pivot_data))):  # 最多取10年历史数据
                data = pivot_data.iloc[-(i+1)]
                oi, _ = get_field_with_priority(data, field_mappings['operating_income'])
                rev, _ = get_field_with_priority(data, field_mappings['revenue'])
                
                if rev > 0:
                    margin = (oi / rev) * 100
                    historical_operating_margin.append(margin)
            
            # 使用动态边界进行极端值处理
            lower_bound, upper_bound = get_dynamic_extreme_bounds(
                historical_operating_margin, 'operating_margin'
            )
            factors['operating_margin'] = np.clip(operating_margin, lower_bound, upper_bound)
            factors['operating_margin_field_flag'] = f"OI:{oi_priority},REV:{revenue_priority},BOUNDS:({lower_bound:.1f},{upper_bound:.1f})"
        else:
            factors['operating_margin'] = None
            factors['operating_margin_field_flag'] = "NA"
    except:
        factors['operating_margin'] = None
        factors['operating_margin_field_flag'] = "NA"
    
    # 20. operating_margin_std (营业利润率稳定性)
    try:
        # 获取计算日期之前的所有有效数据
        available_data = get_available_data_at_date(pivot_data, calculation_date)
        
        if len(available_data) >= 3:
            margins = []
            for i in range(min(4, len(available_data))):
                data = available_data.iloc[-(i+1)]
                operating_income, _ = get_field_with_priority(data, field_mappings['operating_income'])
                revenue, _ = get_field_with_priority(data, field_mappings['revenue'])
                
                if revenue > 0:
                    margin = (operating_income / revenue) * 100
                    margins.append(margin)
            
            if len(margins) >= 3:
                margin_std = np.std(margins)
                
                # 收集历史营业利润率标准差数据用于动态边界计算
                historical_margin_std = []
                for i in range(3, min(8, len(pivot_data))):
                    if i < len(pivot_data):
                        window_margins = []
                        for j in range(i-2, i+1):
                            if j < len(pivot_data):
                                data = pivot_data.iloc[-(j+1)]
                                oi, _ = get_field_with_priority(data, field_mappings['operating_income'])
                                rev, _ = get_field_with_priority(data, field_mappings['revenue'])
                                
                                if rev > 0:
                                    margin = (oi / rev) * 100
                                    window_margins.append(margin)
                        
                        if len(window_margins) >= 3:
                            std = np.std(window_margins)
                            historical_margin_std.append(std)
                
                # 使用动态边界进行极端值处理
                lower_bound, upper_bound = get_dynamic_extreme_bounds(
                    historical_margin_std, 'operating_margin_std'
                )
                factors['operating_margin_std'] = np.clip(margin_std, lower_bound, upper_bound)
            else:
                factors['operating_margin_std'] = None
        else:
            factors['operating_margin_std'] = None
    except:
        factors['operating_margin_std'] = None
    
    # 21. roic (投资回报率)
    try:
        net_income, ni_priority = get_field_with_priority(latest_data, field_mappings['net_income'])
        debt, debt_priority = get_field_with_priority(latest_data, field_mappings['debt'])
        total_equity, equity_priority = get_field_with_priority(latest_data, field_mappings['equity'])
        invested_capital = debt + total_equity
        
        if invested_capital > 0 and net_income > 0:
            roic = (net_income / invested_capital) * 100
            
            # 收集历史ROIC数据用于动态边界计算
            historical_roic = []
            for i in range(min(10, len(pivot_data))):  # 最多取10年历史数据
                data = pivot_data.iloc[-(i+1)]
                ni, _ = get_field_with_priority(data, field_mappings['net_income'])
                debt, _ = get_field_with_priority(data, field_mappings['debt'])
                equity, _ = get_field_with_priority(data, field_mappings['equity'])
                capital = debt + equity
                
                if capital > 0 and ni > 0:
                    roic_hist = (ni / capital) * 100
                    historical_roic.append(roic_hist)
            
            # 使用动态边界进行极端值处理
            lower_bound, upper_bound = get_dynamic_extreme_bounds(
                historical_roic, 'roic'
            )
            factors['roic'] = np.clip(roic, lower_bound, upper_bound)
            factors['roic_field_flag'] = f"NI:{ni_priority},DEBT:{debt_priority},EQ:{equity_priority},BOUNDS:({lower_bound:.1f},{upper_bound:.1f})"
        else:
            factors['roic'] = None
            factors['roic_field_flag'] = "NA"
    except:
        factors['roic'] = None
        factors['roic_field_flag'] = "NA"
    
    # 22. roic_cagr (ROIC CAGR)
    try:
        roic_values = []
        for i, row in pivot_data.iterrows():
            net_income, _ = get_field_with_priority(row, field_mappings['net_income'])
            debt, _ = get_field_with_priority(row, field_mappings['debt'])
            total_equity, _ = get_field_with_priority(row, field_mappings['equity'])
            invested_capital = debt + total_equity
            fiscal_year = int(row['financial_period_absolute'].replace('FY', ''))
            
            if invested_capital > 0 and net_income > 0:
                roic = (net_income / invested_capital) * 100
                roic_values.append({'year': fiscal_year, 'value': roic})
            else:
                roic_values.append({'year': fiscal_year, 'value': np.nan})
        
        valid_roic = [item for item in roic_values if not np.isnan(item['value']) and item['value'] > 0]
        
        if len(valid_roic) >= 2:
            start_roic, end_roic, calculation_type = find_longest_positive_range(valid_roic)
            
            if start_roic and end_roic:
                years = end_roic['year'] - start_roic['year']
                if years > 0:
                    if calculation_type == "turnaround_growth":
                        roic_cagr = (end_roic['value'] - start_roic['value']) / start_roic['value'] / years * 100
                    else:
                        roic_cagr = (pow(end_roic['value'] / start_roic['value'], 1 / years) - 1) * 100
                    
                    # 收集历史ROIC CAGR数据用于动态边界计算
                    historical_roic_cagr = []
                    for start_idx in range(len(valid_roic)-1):
                        for end_idx in range(start_idx+1, len(valid_roic)):
                            start_r = valid_roic[start_idx]
                            end_r = valid_roic[end_idx]
                            years_diff = end_r['year'] - start_r['year']
                            if years_diff > 0 and start_r['value'] > 0:
                                cagr = (pow(end_r['value'] / start_r['value'], 1 / years_diff) - 1) * 100
                                historical_roic_cagr.append(cagr)
                    
                    # 使用动态边界进行极端值处理
                    lower_bound, upper_bound = get_dynamic_extreme_bounds(
                        historical_roic_cagr, 'roic_cagr'
                    )
                    factors['roic_cagr'] = np.clip(roic_cagr, lower_bound, upper_bound)
                else:
                    factors['roic_cagr'] = None
            else:
                factors['roic_cagr'] = None
        else:
            factors['roic_cagr'] = None
    except:
        factors['roic_cagr'] = None
    
    # 23. effective_tax_rate (有效税率)
    try:
        ebit, ebit_priority = get_field_with_priority(latest_data, field_mappings['ebit'])
        tax_expense, tax_priority = get_field_with_priority(latest_data, field_mappings['income_taxes'])
        
        if ebit > 0:
            tax_rate = (tax_expense / ebit) * 100
            
            # 收集历史有效税率数据用于动态边界计算
            historical_tax_rate = []
            for i in range(min(10, len(pivot_data))):  # 最多取10年历史数据
                data = pivot_data.iloc[-(i+1)]
                ebit_hist, _ = get_field_with_priority(data, field_mappings['ebit'])
                tax_hist, _ = get_field_with_priority(data, field_mappings['income_taxes'])
                
                if ebit_hist > 0:
                    tax_rate_hist = (tax_hist / ebit_hist) * 100
                    historical_tax_rate.append(tax_rate_hist)
            
            # 使用动态边界进行极端值处理
            lower_bound, upper_bound = get_dynamic_extreme_bounds(
                historical_tax_rate, 'effective_tax_rate'
            )
            factors['effective_tax_rate'] = np.clip(tax_rate, lower_bound, upper_bound)
            factors['effective_tax_rate_field_flag'] = f"EBIT:{ebit_priority},TAX:{tax_priority},BOUNDS:({lower_bound:.1f},{upper_bound:.1f})"
        else:
            factors['effective_tax_rate'] = None
            factors['effective_tax_rate_field_flag'] = "NA"
    except:
        factors['effective_tax_rate'] = None
        factors['effective_tax_rate_field_flag'] = "NA"
    
    # 24. effective_tax_rate_std (有效税率稳定性)
    try:
        # 获取计算日期之前的所有有效数据
        available_data = get_available_data_at_date(pivot_data, calculation_date)
        
        if len(available_data) >= 3:
            tax_rates = []
            for i in range(min(4, len(available_data))):
                data = available_data.iloc[-(i+1)]
                ebit, _ = get_field_with_priority(data, field_mappings['ebit'])
                tax_expense, _ = get_field_with_priority(data, field_mappings['income_taxes'])
                
                if ebit > 0:
                    tax_rate = (tax_expense / ebit) * 100
                    if -50 <= tax_rate <= 100:
                        tax_rates.append(tax_rate)
            
            if len(tax_rates) >= 3:
                tax_rate_std = np.std(tax_rates)
                
                # 收集历史税率标准差数据用于动态边界计算
                historical_tax_std = []
                for i in range(3, min(8, len(pivot_data))):
                    if i < len(pivot_data):
                        window_tax_rates = []
                        for j in range(i-2, i+1):
                            if j < len(pivot_data):
                                data = pivot_data.iloc[-(j+1)]
                                ebit, _ = get_field_with_priority(data, field_mappings['ebit'])
                                tax, _ = get_field_with_priority(data, field_mappings['income_taxes'])
                                if ebit > 0:
                                    rate = (tax / ebit) * 100
                                    if -50 <= rate <= 100:
                                        window_tax_rates.append(rate)
                        if len(window_tax_rates) >= 3:
                            std = np.std(window_tax_rates)
                            historical_tax_std.append(std)
                
                # 使用动态边界进行极端值处理
                lower_bound, upper_bound = get_dynamic_extreme_bounds(
                    historical_tax_std, 'effective_tax_rate_std'
                )
                factors['effective_tax_rate_std'] = np.clip(tax_rate_std, lower_bound, upper_bound)
            else:
                factors['effective_tax_rate_std'] = None
        else:
            factors['effective_tax_rate_std'] = None
    except:
        factors['effective_tax_rate_std'] = None
    
    print(f"   ✅ {stock_symbol}: 完整24因子计算完成")
    return factors

def calculate_rolling_statistics_for_standardization(client, stock_symbol, calculation_date, lookback_days=1250):
    """计算滚动过去5年统计量用于因子标准化，避免未来函数"""
    # 获取历史基本面数据（过去5年约1250个交易日，确保有足够的数据点计算统计量）
    start_date = (pd.to_datetime(calculation_date) - pd.Timedelta(days=lookback_days)).strftime('%Y-%m-%d')
    
    query = f"""
    SELECT
        stock_symbol,
        financial_period_absolute,
        period_end_date,
        item_name,
        value,
        effective_date
    FROM fundamental_data_with_announcement_dates
    WHERE stock_symbol = '{stock_symbol}'
      AND statement_type IN ('income_statement', 'balance_sheet_history', 'cash_flow')
      AND financial_period_absolute REGEXP '^FY[0-9]{{4}}$'
      AND effective_date BETWEEN '{start_date}' AND '{calculation_date}'
    ORDER BY effective_date ASC
    """
    
    result = client.execute(query)
    if not result:
        return None
    
    df = pd.DataFrame(result, columns=[
        'stock_symbol', 'financial_period_absolute', 'period_end_date',
        'item_name', 'value', 'effective_date'
    ])
    
    # 透视数据
    pivot_data = df.pivot_table(
        index=['financial_period_absolute', 'period_end_date', 'effective_date'],
        columns='item_name',
        values='value',
        aggfunc='first'
    ).reset_index()
    
    if pivot_data.empty:
        return None
    
    # 按effective_date排序
    pivot_data = pivot_data.sort_values('effective_date').reset_index(drop=True)
    
    return pivot_data

def standardize_factors(factors_dict, client, stock_symbol, calculation_date):
    """对24个因子进行标准化处理"""
    
    print(f"🔄 开始标准化 {stock_symbol} 的24个因子...")
    
    # 获取滚动过去5年统计量
    rolling_data = calculate_rolling_statistics_for_standardization(client, stock_symbol, calculation_date)
    if rolling_data is None:
        print(f"   ❌ {stock_symbol}: 无法获取滚动统计量数据")
        return factors_dict
    
    # 定义字段映射（与计算函数保持一致）
    field_mappings = {
        'revenue': [
            'Revenue from Business Activities - Total',
            'Revenue from Goods & Services'
        ],
        'net_income': [
            'Net Income (Loss) - Total',
            'Net Income - Total'
        ],
        'total_assets': [
            'Total Assets',
            'Total Assets - Total'
        ],
        'total_equity': [
            'Shareholders\' Equity - Attributable to Parent Shareholders - Total',
            'Common Equity Attributable to Parent Shareholders',
            'Common Equity - Total'
        ],
        'rd_expense': [
            'Research & Development Expense',
            'Research & Development Expense - Supplemental'
        ],
        'operating_cash_flow': [
            'Net Cash Flow from Operating Activities',
            'Net Cash Flow from Operating Activities - Total',
            'Cash Flow from Operations'
        ],
        'total_liabilities': [
            'Total Liabilities',
            'Total Liabilities & Shareholders Equity'
        ],
        'patents': [
            'Patents - Total',
            'Intellectual Property - Total'
        ],
        'capex': [
            'Capital Expenditure - Total',
            'Capital Expenditure'
        ],
        'depreciation': [
            'Depreciation and Amortization - Total',
            'Depreciation - Total'
        ],
        'interest_expense': [
            'Interest Expense - Total',
            'Interest Expense'
        ],
        'tax_expense': [
            'Income Tax Expense - Total',
            'Income Tax Expense'
        ],
        'ebit': [
            'Operating Income (Loss)',
            'Earnings Before Interest and Taxes'
        ],
        'cash': [
            'Cash and Cash Equivalents',
            'Cash and Cash Equivalents - Total'
        ],
        'intangible_assets': [
            'Intangible Assets - Total',
            'Intangible Assets'
        ],
        'debt': [
            'Total Debt',
            'Long Term Debt'
        ],
        'equity': [
            'Shareholders\' Equity - Attributable to Parent Shareholders - Total',
            'Common Equity Attributable to Parent Shareholders'
        ],
        'shares': [
            'Common Stock Shares Outstanding',
            'Shares Outstanding'
        ],
        'income_taxes': [
            'Income Tax Expense - Total',
            'Income Tax Expense'
        ]
    }
    
    def get_field_with_priority_simple(data, field_priority_list):
        """简化版字段获取函数"""
        for field in field_priority_list:
            if field in data and pd.notna(data[field]) and data[field] != 0:
                return data[field], 'P1'
        return None, 'NA'
    
    # 1. 估值与财务比率因子 → Min-Max标准化 [0,1]
    min_max_factors = [
        'valuation_bubble_signal', 'profit_revenue_ratio', 
        'operating_margin', 'roic'
    ]
    
    for factor in min_max_factors:
        if factor in factors_dict and factors_dict[factor] is not None:
            # 收集滚动数据
            rolling_values = []
            for i in range(min(10, len(rolling_data))):
                data = rolling_data.iloc[-(i+1)]
                
                if factor == 'valuation_bubble_signal':
                    # 计算PEG比率
                    ni, _ = get_field_with_priority_simple(data, field_mappings['net_income'])
                    rev, _ = get_field_with_priority_simple(data, field_mappings['revenue'])
                    if ni and rev and ni > 0 and rev > 0:
                        # 简化计算，这里需要价格数据
                        rolling_values.append(ni/rev)  # 简化代理
                
                elif factor == 'profit_revenue_ratio':
                    ni, _ = get_field_with_priority_simple(data, field_mappings['net_income'])
                    rev, _ = get_field_with_priority_simple(data, field_mappings['revenue'])
                    if ni and rev and rev > 0:
                        rolling_values.append((ni/rev) * 100)
                
                elif factor == 'operating_margin':
                    ebit, _ = get_field_with_priority_simple(data, field_mappings['ebit'])
                    rev, _ = get_field_with_priority_simple(data, field_mappings['revenue'])
                    if ebit and rev and rev > 0:
                        rolling_values.append((ebit/rev) * 100)
                
                elif factor == 'roic':
                    ni, _ = get_field_with_priority_simple(data, field_mappings['net_income'])
                    debt, _ = get_field_with_priority_simple(data, field_mappings['debt'])
                    equity, _ = get_field_with_priority_simple(data, field_mappings['equity'])
                    if ni and debt is not None and equity:
                        capital_employed = debt + equity
                        if capital_employed > 0:
                            rolling_values.append((ni/capital_employed) * 100)
            
            if len(rolling_values) >= 3:
                rolling_min = min(rolling_values)
                rolling_max = max(rolling_values)
                
                if rolling_max > rolling_min:
                    # 负向因子先翻转
                    if factor == 'valuation_bubble_signal':
                        raw_value = -factors_dict[factor]
                    else:
                        raw_value = factors_dict[factor]
                    
                    # Min-Max标准化
                    normalized_value = (raw_value - rolling_min) / (rolling_max - rolling_min)
                    factors_dict[f'{factor}_normalized'] = normalized_value
                    factors_dict[f'{factor}_standardization_method'] = 'Min-Max'
                    factors_dict[f'{factor}_rolling_bounds'] = f"({rolling_min:.2f}, {rolling_max:.2f})"
                else:
                    factors_dict[f'{factor}_normalized'] = 0.5  # 默认中值
                    factors_dict[f'{factor}_standardization_method'] = 'Min-Max-Fallback'
            else:
                factors_dict[f'{factor}_normalized'] = 0.5
                factors_dict[f'{factor}_standardization_method'] = 'Min-Max-NoData'
    
    # 2. 波动率与变化率因子 → Z-Score标准化
    zscore_volatility_factors = [
        'tech_gap_warning', 'roce_volatility', 'effective_tax_rate_std'
    ]
    
    for factor in zscore_volatility_factors:
        if factor in factors_dict and factors_dict[factor] is not None:
            # 收集滚动数据
            rolling_values = []
            for i in range(min(10, len(rolling_data))):
                data = rolling_data.iloc[-(i+1)]
                
                if factor == 'tech_gap_warning':
                    # 计算R&D强度变化率
                    if i < len(rolling_data) - 1:
                        curr_data = rolling_data.iloc[-(i+1)]
                        prev_data = rolling_data.iloc[-(i+2)]
                        curr_rd, _ = get_field_with_priority_simple(curr_data, field_mappings['rd_expense'])
                        curr_rev, _ = get_field_with_priority_simple(curr_data, field_mappings['revenue'])
                        prev_rd, _ = get_field_with_priority_simple(prev_data, field_mappings['rd_expense'])
                        prev_rev, _ = get_field_with_priority_simple(prev_data, field_mappings['revenue'])
                        
                        if curr_rd and curr_rev and prev_rd and prev_rev and prev_rev > 0:
                            prev_tp = (prev_rd / prev_rev) * 100
                            curr_tp = (curr_rd / curr_rev) * 100
                            if prev_tp > 0:
                                rate = (curr_tp - prev_tp) / prev_tp * 100
                                rolling_values.append(rate)
                
                elif factor == 'roce_volatility':
                    # 计算ROCE
                    ni, _ = get_field_with_priority_simple(data, field_mappings['net_income'])
                    debt, _ = get_field_with_priority_simple(data, field_mappings['debt'])
                    equity, _ = get_field_with_priority_simple(data, field_mappings['equity'])
                    if ni and debt is not None and equity:
                        capital_employed = debt + equity
                        if capital_employed > 0:
                            roce = (ni/capital_employed) * 100
                            rolling_values.append(roce)
                
                elif factor == 'effective_tax_rate_std':
                    # 计算有效税率
                    ebit, _ = get_field_with_priority_simple(data, field_mappings['ebit'])
                    tax, _ = get_field_with_priority_simple(data, field_mappings['tax_expense'])
                    if ebit and tax and ebit > 0:
                        tax_rate = (tax/ebit) * 100
                        if -50 <= tax_rate <= 100:
                            rolling_values.append(tax_rate)
            
            if len(rolling_values) >= 3:
                rolling_mean = np.mean(rolling_values)
                rolling_std = np.std(rolling_values)
                
                if rolling_std > 0:
                    # Z-Score标准化
                    normalized_value = (factors_dict[factor] - rolling_mean) / rolling_std
                    # 负向因子翻转
                    normalized_value = -normalized_value
                    
                    factors_dict[f'{factor}_normalized'] = normalized_value
                    factors_dict[f'{factor}_standardization_method'] = 'Z-Score-Negative'
                    factors_dict[f'{factor}_rolling_stats'] = f"μ={rolling_mean:.2f}, σ={rolling_std:.2f}"
                else:
                    factors_dict[f'{factor}_normalized'] = 0
                    factors_dict[f'{factor}_standardization_method'] = 'Z-Score-ZeroStd'
            else:
                factors_dict[f'{factor}_normalized'] = 0
                factors_dict[f'{factor}_standardization_method'] = 'Z-Score-NoData'
    
    # 3. 技术护城河因子 → Min-Max标准化 [0,1]
    min_max_tech_factors = [
        'tech_premium', 'patent_density', 'ecosystem_cash_ratio'
    ]
    
    for factor in min_max_tech_factors:
        if factor in factors_dict and factors_dict[factor] is not None:
            rolling_values = []
            for i in range(min(10, len(rolling_data))):
                data = rolling_data.iloc[-(i+1)]
                
                if factor == 'tech_premium':
                    rd, _ = get_field_with_priority_simple(data, field_mappings['rd_expense'])
                    rev, _ = get_field_with_priority_simple(data, field_mappings['revenue'])
                    if rd and rev and rev > 0:
                        rolling_values.append((rd/rev) * 100)
                
                elif factor == 'patent_density':
                    intang, _ = get_field_with_priority_simple(data, field_mappings['intangible_assets'])
                    rev, _ = get_field_with_priority_simple(data, field_mappings['revenue'])
                    if intang and rev and rev > 0:
                        rolling_values.append((intang/rev) * 100)
                
                elif factor == 'ecosystem_cash_ratio':
                    cash, _ = get_field_with_priority_simple(data, field_mappings['cash'])
                    assets, _ = get_field_with_priority_simple(data, field_mappings['total_assets'])
                    if cash and assets and assets > 0:
                        rolling_values.append((cash/assets) * 100)
            
            if len(rolling_values) >= 3:
                rolling_min = min(rolling_values)
                rolling_max = max(rolling_values)
                
                if rolling_max > rolling_min:
                    normalized_value = (factors_dict[factor] - rolling_min) / (rolling_max - rolling_min)
                    factors_dict[f'{factor}_normalized'] = normalized_value
                    factors_dict[f'{factor}_standardization_method'] = 'Min-Max'
                    factors_dict[f'{factor}_rolling_bounds'] = f"({rolling_min:.2f}, {rolling_max:.2f})"
                else:
                    factors_dict[f'{factor}_normalized'] = 0.5
                    factors_dict[f'{factor}_standardization_method'] = 'Min-Max-Fallback'
            else:
                factors_dict[f'{factor}_normalized'] = 0.5
                factors_dict[f'{factor}_standardization_method'] = 'Min-Max-NoData'
    
    # 4. 现金流质量因子 → Robust Scaling
    robust_scaling_factors = [
        'fcf_quality', 'cash_flow_coverage_ratio'
    ]
    
    for factor in robust_scaling_factors:
        if factor in factors_dict and factors_dict[factor] is not None:
            rolling_values = []
            for i in range(min(10, len(rolling_data))):
                data = rolling_data.iloc[-(i+1)]
                
                if factor == 'fcf_quality':
                    fcf, _ = get_field_with_priority_simple(data, field_mappings.get('fcf', ['Free Cash Flow']))
                    ni, _ = get_field_with_priority_simple(data, field_mappings['net_income'])
                    if fcf and ni and ni != 0:
                        rolling_values.append((fcf/ni) * 100)
                
                elif factor == 'cash_flow_coverage_ratio':
                    ocf, _ = get_field_with_priority_simple(data, field_mappings['operating_cash_flow'])
                    liab, _ = get_field_with_priority_simple(data, field_mappings['total_liabilities'])
                    if ocf and liab and liab > 0:
                        rolling_values.append((ocf/liab) * 100)
            
            if len(rolling_values) >= 3:
                rolling_median = np.median(rolling_values)
                rolling_q75 = np.percentile(rolling_values, 75)
                rolling_q25 = np.percentile(rolling_values, 25)
                rolling_iqr = rolling_q75 - rolling_q25
                
                if rolling_iqr > 0:
                    normalized_value = (factors_dict[factor] - rolling_median) / rolling_iqr
                    factors_dict[f'{factor}_normalized'] = normalized_value
                    factors_dict[f'{factor}_standardization_method'] = 'Robust-Scaling'
                    factors_dict[f'{factor}_rolling_stats'] = f"M={rolling_median:.2f}, IQR={rolling_iqr:.2f}"
                else:
                    factors_dict[f'{factor}_normalized'] = 0
                    factors_dict[f'{factor}_standardization_method'] = 'Robust-Scaling-ZeroIQR'
            else:
                factors_dict[f'{factor}_normalized'] = 0
                factors_dict[f'{factor}_standardization_method'] = 'Robust-Scaling-NoData'
    
    # 5. 增长与变化率因子 → Z-Score标准化
    zscore_growth_factors = [
        'revenue_growth_continuity', 'revenue_yoy', 'revenue_cagr',
        'net_income_yoy', 'fcf_cagr', 'roic_cagr'
    ]
    
    for factor in zscore_growth_factors:
        if factor in factors_dict and factors_dict[factor] is not None:
            rolling_values = []
            for i in range(min(10, len(rolling_data))):
                data = rolling_data.iloc[-(i+1)]
                
                # 这些因子在原始数据中已经计算好了，直接使用
                if factor in data:
                    rolling_values.append(data[factor])
            
            if len(rolling_values) >= 3:
                rolling_mean = np.mean(rolling_values)
                rolling_std = np.std(rolling_values)
                
                if rolling_std > 0:
                    normalized_value = (factors_dict[factor] - rolling_mean) / rolling_std
                    factors_dict[f'{factor}_normalized'] = normalized_value
                    factors_dict[f'{factor}_standardization_method'] = 'Z-Score'
                    factors_dict[f'{factor}_rolling_stats'] = f"μ={rolling_mean:.2f}, σ={rolling_std:.2f}"
                else:
                    factors_dict[f'{factor}_normalized'] = 0
                    factors_dict[f'{factor}_standardization_method'] = 'Z-Score-ZeroStd'
            else:
                factors_dict[f'{factor}_normalized'] = 0
                factors_dict[f'{factor}_standardization_method'] = 'Z-Score-NoData'
    
    # 6. 稳定性与税率因子 → Z-Score标准化
    zscore_stability_factors = [
        'operating_margin_std', 'effective_tax_rate'
    ]
    
    for factor in zscore_stability_factors:
        if factor in factors_dict and factors_dict[factor] is not None:
            rolling_values = []
            for i in range(min(10, len(rolling_data))):
                data = rolling_data.iloc[-(i+1)]
                
                if factor == 'operating_margin_std':
                    # 计算营业利润率
                    ebit, _ = get_field_with_priority_simple(data, field_mappings['ebit'])
                    rev, _ = get_field_with_priority_simple(data, field_mappings['revenue'])
                    if ebit and rev and rev > 0:
                        rolling_values.append((ebit/rev) * 100)
                
                elif factor == 'effective_tax_rate':
                    # 计算有效税率
                    ebit, _ = get_field_with_priority_simple(data, field_mappings['ebit'])
                    tax, _ = get_field_with_priority_simple(data, field_mappings['tax_expense'])
                    if ebit and tax and ebit > 0:
                        tax_rate = (tax/ebit) * 100
                        if -50 <= tax_rate <= 100:
                            rolling_values.append(tax_rate)
            
            if len(rolling_values) >= 3:
                rolling_mean = np.mean(rolling_values)
                rolling_std = np.std(rolling_values)
                
                if rolling_std > 0:
                    # effective_tax_rate 是负向因子，先翻转
                    if factor == 'effective_tax_rate':
                        raw_value = -factors_dict[factor]
                    else:
                        raw_value = factors_dict[factor]
                    
                    normalized_value = (raw_value - rolling_mean) / rolling_std
                    factors_dict[f'{factor}_normalized'] = normalized_value
                    factors_dict[f'{factor}_standardization_method'] = 'Z-Score-Negative' if factor == 'effective_tax_rate' else 'Z-Score'
                    factors_dict[f'{factor}_rolling_stats'] = f"μ={rolling_mean:.2f}, σ={rolling_std:.2f}"
                else:
                    factors_dict[f'{factor}_normalized'] = 0
                    factors_dict[f'{factor}_standardization_method'] = 'Z-Score-ZeroStd'
            else:
                factors_dict[f'{factor}_normalized'] = 0
                factors_dict[f'{factor}_standardization_method'] = 'Z-Score-NoData'
    
    # 7. 其他因子 → Min-Max标准化 [0,1]
    min_max_other_factors = [
        'financial_health', 'fcf_per_share', 'effective_tax_rate_improvement', 'dynamic_safety_margin'
    ]
    
    for factor in min_max_other_factors:
        if factor in factors_dict and factors_dict[factor] is not None:
            rolling_values = []
            for i in range(min(10, len(rolling_data))):
                data = rolling_data.iloc[-(i+1)]
                
                if factor == 'financial_health':
                    # 计算流动比率
                    ca, _ = get_field_with_priority_simple(data, field_mappings.get('current_assets', ['Current Assets']))
                    cl, _ = get_field_with_priority_simple(data, field_mappings.get('current_liabilities', ['Current Liabilities']))
                    if ca and cl and cl > 0:
                        rolling_values.append(ca/cl)
                
                elif factor == 'fcf_per_share':
                    fcf, _ = get_field_with_priority_simple(data, field_mappings.get('fcf', ['Free Cash Flow']))
                    shares, _ = get_field_with_priority_simple(data, field_mappings['shares'])
                    if fcf and shares and shares > 0:
                        rolling_values.append(fcf/shares)
                
                elif factor == 'effective_tax_rate_improvement':
                    # 计算税率改善
                    ebit, _ = get_field_with_priority_simple(data, field_mappings['ebit'])
                    tax, _ = get_field_with_priority_simple(data, field_mappings['tax_expense'])
                    if ebit and tax and ebit > 0:
                        tax_rate = (tax/ebit) * 100
                        if -50 <= tax_rate <= 100:
                            rolling_values.append(tax_rate)
                
                elif factor == 'dynamic_safety_margin':
                    # 这是复合因子，需要单独处理
                    if 'tech_premium' in factors_dict and 'ecosystem_cash_ratio' in factors_dict:
                        if factors_dict['tech_premium'] is not None and factors_dict['ecosystem_cash_ratio'] is not None:
                            safety_margin = factors_dict['tech_premium'] + factors_dict['ecosystem_cash_ratio']
                            rolling_values.append(safety_margin)
            
            if len(rolling_values) >= 3:
                rolling_min = min(rolling_values)
                rolling_max = max(rolling_values)
                
                if rolling_max > rolling_min:
                    normalized_value = (factors_dict[factor] - rolling_min) / (rolling_max - rolling_min)
                    factors_dict[f'{factor}_normalized'] = normalized_value
                    factors_dict[f'{factor}_standardization_method'] = 'Min-Max'
                    factors_dict[f'{factor}_rolling_bounds'] = f"({rolling_min:.2f}, {rolling_max:.2f})"
                else:
                    factors_dict[f'{factor}_normalized'] = 0.5
                    factors_dict[f'{factor}_standardization_method'] = 'Min-Max-Fallback'
            else:
                factors_dict[f'{factor}_normalized'] = 0.5
                factors_dict[f'{factor}_standardization_method'] = 'Min-Max-NoData'
    
    print(f"   ✅ {stock_symbol}: 24因子标准化完成")
    return factors_dict

def main():
    """主函数：计算所有股票的24因子并进行标准化"""
    print("🚀 开始计算24因子系统并进行标准化...")
    
    # 连接ClickHouse
    client = connect_to_ap_research()
    if not client:
        print("❌ 无法连接到ClickHouse数据库")
        return
    
    try:
        # 获取所有股票列表
        stocks = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META'] # Assuming these are the stocks to process
        if not stocks:
            print("❌ 无法获取股票列表")
            return
        
        print(f"📊 找到 {len(stocks)} 只股票")
        
        # 设置计算日期（可以设置为历史日期进行回测）
        calculation_date = datetime.now().strftime('%Y-%m-%d')  # 可以修改为历史日期
        print(f"📅 计算日期: {calculation_date}")
        
        # 计算所有股票的因子并进行标准化
        all_factors = {}
        for i, stock in enumerate(stocks, 1):
            print(f"\n📈 处理进度: {i}/{len(stocks)} - {stock}")
            try:
                # 1. 计算原始因子
                factors = calculate_complete_24_factors(client, stock, calculation_date)
                if factors:
                    # 2. 进行标准化
                    factors = standardize_factors(factors, client, stock, calculation_date)
                    all_factors[stock] = factors
            except Exception as e:
                print(f"   ❌ {stock}: 计算失败 - {e}")
                continue
        
        # 保存结果
        if all_factors:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"mega7_complete_24_factors_standardized_{timestamp}.csv"
            
            # 转换为DataFrame
            df = pd.DataFrame.from_dict(all_factors, orient='index')
            
            # 保存到CSV
            df.to_csv(filename, encoding='utf-8-sig')
            print(f"\n✅ 因子计算和标准化完成！共处理 {len(all_factors)} 只股票")
            print(f"📁 结果保存至: {filename}")
        else:
            print("❌ 没有成功计算任何股票的因子")
            
    except Exception as e:
        print(f"❌ 主程序执行失败: {e}")
    finally:
        if client:
            client.disconnect()
            print("🔌 数据库连接已关闭")

if __name__ == "__main__":
    main()

