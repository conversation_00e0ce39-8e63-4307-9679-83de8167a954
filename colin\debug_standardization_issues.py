#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断因子标准化问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from final_complete_24_factors_with_field_mapping import (
    connect_to_ap_research, 
    calculate_complete_24_factors,
    calculate_rolling_statistics_for_standardization
)
import pandas as pd
import numpy as np

def debug_rolling_data_issue():
    """调试滚动数据获取问题"""
    print("🔍 开始调试标准化中的滚动数据问题...")
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        print("❌ 无法连接到数据库")
        return
    
    try:
        test_stock = 'AAPL'
        calculation_date = '2024-01-15'
        
        print(f"📊 测试股票: {test_stock}")
        print(f"📅 计算日期: {calculation_date}")
        
        # 1. 检查原始因子计算
        print("\n🔄 步骤1: 检查原始因子计算...")
        factors = calculate_complete_24_factors(client, test_stock, calculation_date)
        
        if factors:
            print("✅ 原始因子计算成功")
            # 显示几个关键因子的原始值
            key_factors = ['revenue_yoy', 'revenue_cagr', 'profit_revenue_ratio', 'tech_premium']
            for factor in key_factors:
                if factor in factors:
                    print(f"   {factor}: {factors[factor]}")
        else:
            print("❌ 原始因子计算失败")
            return
        
        # 2. 检查滚动统计数据获取
        print("\n🔄 步骤2: 检查滚动统计数据获取...")
        rolling_data = calculate_rolling_statistics_for_standardization(client, test_stock, calculation_date)
        
        if rolling_data is not None:
            print(f"✅ 滚动数据获取成功，共 {len(rolling_data)} 条记录")
            print(f"   数据列数: {len(rolling_data.columns)}")
            print(f"   数据时间范围: {rolling_data['effective_date'].min()} 到 {rolling_data['effective_date'].max()}")
            
            # 检查关键字段是否存在
            print("\n📋 检查关键因子字段是否存在于滚动数据中:")
            for factor in key_factors:
                if factor in rolling_data.columns:
                    non_null_count = rolling_data[factor].notna().sum()
                    print(f"   ✅ {factor}: 存在，非空值 {non_null_count}/{len(rolling_data)}")
                else:
                    print(f"   ❌ {factor}: 不存在于滚动数据中")
            
            # 显示滚动数据的前几行
            print(f"\n📊 滚动数据前3行:")
            print(rolling_data.head(3))
            
        else:
            print("❌ 滚动数据获取失败")
            return
        
        # 3. 手动测试标准化逻辑
        print("\n🔄 步骤3: 手动测试标准化逻辑...")
        
        # 测试revenue_yoy的标准化
        if 'revenue_yoy' in factors and factors['revenue_yoy'] is not None:
            print(f"\n📈 测试 revenue_yoy 标准化:")
            print(f"   当前值: {factors['revenue_yoy']}")
            
            # 检查历史数据
            rolling_values = []
            for i in range(min(10, len(rolling_data))):
                data = rolling_data.iloc[-(i+1)]
                if 'revenue_yoy' in data and pd.notna(data['revenue_yoy']):
                    rolling_values.append(data['revenue_yoy'])
                    print(f"   历史值 {i+1}: {data['revenue_yoy']}")
            
            print(f"   收集到的历史值数量: {len(rolling_values)}")
            
            if len(rolling_values) >= 3:
                rolling_mean = np.mean(rolling_values)
                rolling_std = np.std(rolling_values)
                print(f"   历史均值: {rolling_mean:.4f}")
                print(f"   历史标准差: {rolling_std:.4f}")
                
                if rolling_std > 0:
                    normalized_value = (factors['revenue_yoy'] - rolling_mean) / rolling_std
                    print(f"   标准化值: {normalized_value:.4f}")
                else:
                    print("   ⚠️ 标准差为0，无法标准化")
            else:
                print("   ⚠️ 历史数据不足，无法标准化")
        
        # 4. 检查数据库表结构
        print("\n🔄 步骤4: 检查数据库表结构...")
        
        # 检查fundamental_data_with_announcement_dates表是否存在
        table_check_query = """
        SELECT COUNT(*) as count
        FROM fundamental_data_with_announcement_dates
        WHERE stock_symbol = 'AAPL'
        LIMIT 1
        """
        
        try:
            result = client.execute(table_check_query)
            if result and result[0][0] > 0:
                print("✅ fundamental_data_with_announcement_dates 表存在且有数据")
            else:
                print("❌ fundamental_data_with_announcement_dates 表不存在或无数据")
                
                # 检查替代表
                alt_query = """
                SELECT COUNT(*) as count
                FROM priority_quality_fundamental_data_complete_deduped
                WHERE stock_symbol = 'AAPL'
                LIMIT 1
                """
                alt_result = client.execute(alt_query)
                if alt_result and alt_result[0][0] > 0:
                    print("✅ 找到替代表: priority_quality_fundamental_data_complete_deduped")
                else:
                    print("❌ 也没有找到替代表")
                    
        except Exception as e:
            print(f"❌ 表检查出错: {e}")
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            client.disconnect()
            print("🔌 数据库连接已关闭")

def debug_factor_calculation_in_rolling_data():
    """调试滚动数据中因子计算问题"""
    print("\n🔍 调试滚动数据中的因子计算问题...")
    
    # 问题分析：
    # 1. 滚动数据只包含原始财务数据，不包含计算后的因子
    # 2. 标准化函数试图从滚动数据中直接获取因子值，但这些因子需要计算
    # 3. 需要对每个历史期间重新计算因子，而不是直接查找
    
    print("📋 问题分析:")
    print("   1. 滚动数据只包含原始财务科目，不包含计算后的因子")
    print("   2. revenue_yoy, revenue_cagr等需要基于历史数据计算")
    print("   3. 当前逻辑试图直接从滚动数据中获取因子值，导致失败")
    print("   4. 需要重新设计标准化逻辑")

if __name__ == "__main__":
    print("🚀 因子标准化问题诊断")
    print("=" * 60)
    
    debug_rolling_data_issue()
    debug_factor_calculation_in_rolling_data()
