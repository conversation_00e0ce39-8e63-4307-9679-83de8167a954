#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Mega7历史10年所有因子值，检查NA、0、None问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from final_complete_24_factors_with_field_mapping import (
    connect_to_ap_research, 
    calculate_complete_24_factors
)
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def validate_mega7_all_factors():
    """验证Mega7历史10年所有因子值"""
    print("🔍 验证Mega7历史10年所有因子值")
    print("=" * 80)
    
    # Mega7股票
    mega7_stocks = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META']
    
    # 生成最近10年的日期
    end_date = datetime(2024, 1, 15)
    dates = []
    for i in range(10):
        date = end_date - timedelta(days=365 * i)
        dates.append(date.strftime('%Y-%m-%d'))
    
    print(f"📅 验证日期: {dates}")
    print(f"📊 验证股票: {mega7_stocks}")
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        print("❌ 无法连接到数据库")
        return
    
    try:
        # 存储所有结果
        all_results = []
        issue_summary = {
            'none_values': [],
            'zero_values': [],
            'nan_values': [],
            'total_calculations': 0,
            'successful_calculations': 0
        }
        
        for stock in mega7_stocks:
            print(f"\n📊 验证股票: {stock}")
            print("-" * 60)
            
            stock_issues = {
                'none_count': 0,
                'zero_count': 0,
                'nan_count': 0,
                'valid_count': 0,
                'total_factors': 0
            }
            
            for date_idx, date in enumerate(dates):
                print(f"   日期 {date_idx+1}/10: {date}", end=" ")
                
                try:
                    factors = calculate_complete_24_factors(client, stock, date)
                    issue_summary['total_calculations'] += 1
                    
                    if factors:
                        issue_summary['successful_calculations'] += 1
                        
                        # 提取所有因子值（排除元数据）
                        factor_values = {}
                        for key, value in factors.items():
                            if not key.endswith('_field_flag') and not key.endswith('_calculation_details'):
                                if key not in ['stock_symbol', 'calculation_date', 'fiscal_year', 'period_end_date', 'effective_date']:
                                    factor_values[key] = value
                        
                        # 分析每个因子的状态
                        none_count = 0
                        zero_count = 0
                        nan_count = 0
                        valid_count = 0
                        
                        for factor_name, factor_value in factor_values.items():
                            stock_issues['total_factors'] += 1
                            
                            if factor_value is None:
                                none_count += 1
                                stock_issues['none_count'] += 1
                                issue_summary['none_values'].append({
                                    'stock': stock,
                                    'date': date,
                                    'factor': factor_name,
                                    'value': factor_value,
                                    'flag': factors.get(f"{factor_name}_field_flag", "无标志")
                                })
                            elif pd.isna(factor_value):
                                nan_count += 1
                                stock_issues['nan_count'] += 1
                                issue_summary['nan_values'].append({
                                    'stock': stock,
                                    'date': date,
                                    'factor': factor_name,
                                    'value': factor_value
                                })
                            elif factor_value == 0 or factor_value == 0.0:
                                zero_count += 1
                                stock_issues['zero_count'] += 1
                                issue_summary['zero_values'].append({
                                    'stock': stock,
                                    'date': date,
                                    'factor': factor_name,
                                    'value': factor_value,
                                    'flag': factors.get(f"{factor_name}_field_flag", "无标志")
                                })
                            else:
                                valid_count += 1
                                stock_issues['valid_count'] += 1
                        
                        print(f"✅ 有效:{valid_count}, None:{none_count}, 零值:{zero_count}, NaN:{nan_count}")
                        
                        # 存储结果
                        all_results.append({
                            'stock': stock,
                            'date': date,
                            'valid_count': valid_count,
                            'none_count': none_count,
                            'zero_count': zero_count,
                            'nan_count': nan_count,
                            'total_factors': len(factor_values)
                        })
                        
                    else:
                        print("❌ 计算失败")
                        
                except Exception as e:
                    print(f"❌ 错误: {e}")
                    continue
            
            # 股票级别统计
            if stock_issues['total_factors'] > 0:
                print(f"\n   📊 {stock} 总体统计:")
                print(f"     总因子计算次数: {stock_issues['total_factors']}")
                print(f"     有效值: {stock_issues['valid_count']} ({stock_issues['valid_count']/stock_issues['total_factors']*100:.1f}%)")
                print(f"     None值: {stock_issues['none_count']} ({stock_issues['none_count']/stock_issues['total_factors']*100:.1f}%)")
                print(f"     零值: {stock_issues['zero_count']} ({stock_issues['zero_count']/stock_issues['total_factors']*100:.1f}%)")
                print(f"     NaN值: {stock_issues['nan_count']} ({stock_issues['nan_count']/stock_issues['total_factors']*100:.1f}%)")
        
        # 全面分析结果
        print(f"\n📊 全面验证结果")
        print("=" * 80)
        
        print(f"总计算次数: {issue_summary['total_calculations']}")
        print(f"成功计算次数: {issue_summary['successful_calculations']}")
        print(f"成功率: {issue_summary['successful_calculations']/issue_summary['total_calculations']*100:.1f}%")
        
        # None值问题分析
        if issue_summary['none_values']:
            print(f"\n🚨 None值问题分析 ({len(issue_summary['none_values'])} 个):")
            print("-" * 60)
            
            # 按因子分组
            none_by_factor = {}
            for issue in issue_summary['none_values']:
                factor = issue['factor']
                if factor not in none_by_factor:
                    none_by_factor[factor] = []
                none_by_factor[factor].append(issue)
            
            for factor, issues in sorted(none_by_factor.items(), key=lambda x: len(x[1]), reverse=True):
                print(f"   ❌ {factor}: {len(issues)} 次None")
                # 显示标志信息
                flags = set([issue['flag'] for issue in issues])
                for flag in flags:
                    count = sum(1 for issue in issues if issue['flag'] == flag)
                    print(f"      {flag}: {count} 次")
        
        # 零值问题分析
        if issue_summary['zero_values']:
            print(f"\n⚠️ 零值问题分析 ({len(issue_summary['zero_values'])} 个):")
            print("-" * 60)
            
            # 按因子分组
            zero_by_factor = {}
            for issue in issue_summary['zero_values']:
                factor = issue['factor']
                if factor not in zero_by_factor:
                    zero_by_factor[factor] = []
                zero_by_factor[factor].append(issue)
            
            for factor, issues in sorted(zero_by_factor.items(), key=lambda x: len(x[1]), reverse=True):
                print(f"   ⚠️ {factor}: {len(issues)} 次零值")
        
        # NaN值问题分析
        if issue_summary['nan_values']:
            print(f"\n📊 NaN值问题分析 ({len(issue_summary['nan_values'])} 个):")
            print("-" * 60)
            
            # 按因子分组
            nan_by_factor = {}
            for issue in issue_summary['nan_values']:
                factor = issue['factor']
                if factor not in nan_by_factor:
                    nan_by_factor[factor] = []
                nan_by_factor[factor].append(issue)
            
            for factor, issues in sorted(nan_by_factor.items(), key=lambda x: len(x[1]), reverse=True):
                print(f"   📊 {factor}: {len(issues)} 次NaN")
        
        # 保存详细结果
        if all_results:
            df_results = pd.DataFrame(all_results)
            output_file = f"mega7_factor_validation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            df_results.to_csv(output_file, index=False)
            print(f"\n💾 详细结果已保存到: {output_file}")
        
        # 总结
        total_issues = len(issue_summary['none_values']) + len(issue_summary['zero_values']) + len(issue_summary['nan_values'])
        if total_issues == 0:
            print(f"\n🎉 验证完成：没有发现任何None、零值或NaN问题！")
        else:
            print(f"\n⚠️ 验证完成：发现 {total_issues} 个问题需要进一步修复")
        
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            client.disconnect()
            print("🔌 数据库连接已关闭")

if __name__ == "__main__":
    validate_mega7_all_factors()
