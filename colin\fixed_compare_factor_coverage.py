import pandas as pd
import numpy as np
from datetime import datetime

def compare_factor_coverage():
    """对比改进前后的因子覆盖率"""
    
    print("=== 对比改进前后的因子覆盖率 ===")
    print()
    
    # 1. 读取改进前的结果（最新的覆盖率文件）
    try:
        old_coverage_file = "colin/factor_coverage_20250810_004921.csv"
        old_coverage = pd.read_csv(old_coverage_file)
        print(f"✅ 读取改进前覆盖率数据: {old_coverage_file}")
        print(f"   列名: {list(old_coverage.columns)}")
    except Exception as e:
        print(f"❌ 无法读取改进前的覆盖率文件: {e}")
        return
    
    # 2. 读取改进后的结果
    try:
        new_factors_file = "colin/complete_improved_24_factors_20250810_022631.csv"
        new_factors = pd.read_csv(new_factors_file)
        print(f"✅ 读取改进后因子数据: {new_factors_file}")
        print(f"   因子数量: {len(new_factors.columns)-1}")
    except Exception as e:
        print(f"❌ 无法读取改进后的因子文件: {e}")
        return
    
    # 3. 数据规模对比
    old_total_stocks = old_coverage['total_count'].iloc[0] if len(old_coverage) > 0 else 0
    new_total_stocks = len(new_factors)
    
    print(f"\n📊 数据规模对比:")
    print(f"   改进前: {len(old_coverage)} 个因子，{old_total_stocks} 只股票")
    print(f"   改进后: {len(new_factors.columns)-1} 个因子，{new_total_stocks} 只股票")
    print()
    
    # 4. 计算改进后的覆盖率
    new_coverage_data = []
    for col in new_factors.columns:
        if col != 'stock_symbol':
            valid_count = new_factors[col].notna().sum()
            total_count = len(new_factors)
            coverage_rate = (valid_count / total_count) * 100
            
            new_coverage_data.append({
                'factor': col,
                'coverage_rate': coverage_rate,
                'valid_count': valid_count,
                'total_count': total_count
            })
    
    new_coverage_df = pd.DataFrame(new_coverage_data)
    
    # 5. 合并对比数据
    print(f"📈 详细覆盖率对比:")
    print("-" * 95)
    print(f"{'因子名称':<30} {'改进前覆盖率':<15} {'改进后覆盖率':<15} {'变化':<15} {'状态'}")
    print("-" * 95)
    
    # 因子名称映射（处理可能的命名差异）
    factor_mapping = {
        'tech_premium': 'tech_premium',
        'tech_gap_warning': 'tech_gap_warning', 
        'patent_density': 'patent_density',
        'ecosystem_cash_ratio': 'ecosystem_cash_ratio',
        'adjusted_roce': 'adjusted_roe',  # 改名了
        'fcf_quality': 'fcf_quality',
        'dynamic_safety_margin': 'dynamic_safety_margin',
        'revenue_growth_continuity': 'revenue_growth_continuity',
        'effective_tax_rate_improvement': 'effective_tax_rate_improvement',
        'financial_health': 'financial_health',
        'valuation_bubble_signal': 'valuation_bubble_signal',
        'roce_stability': 'roe_stability',  # 改名了
        'revenue_yoy': 'revenue_yoy',
        'revenue_cagr': 'revenue_cagr',
        'net_income_yoy': 'net_income_yoy',
        'profit_revenue_ratio': 'profit_revenue_ratio',
        'fcf_per_share': 'fcf_per_share',
        'fcf_cagr': 'fcf_cagr',
        'operating_margin': 'operating_margin',
        'operating_margin_std': 'operating_margin_std',
        'roic': 'roic',
        'roic_cagr': 'roic_cagr',
        'effective_tax_rate': 'effective_tax_rate',
        'effective_tax_rate_std': 'effective_tax_rate_std'
    }
    
    improvements = []
    
    for _, old_row in old_coverage.iterrows():
        old_factor = old_row['factor']
        old_rate = old_row['coverage_rate']
        
        new_factor = factor_mapping.get(old_factor, old_factor)
        
        # 获取改进后覆盖率
        new_row = new_coverage_df[new_coverage_df['factor'] == new_factor]
        if len(new_row) > 0:
            new_rate = new_row['coverage_rate'].iloc[0]
            
            # 注意：由于样本数量不同，需要标准化比较
            # 改进前是821只股票，改进后是20只股票（测试样本）
            change = new_rate - old_rate
            
            if change > 10:
                status = "🚀 显著提升"
            elif change > 0:
                status = "✅ 小幅提升"
            elif change > -10:
                status = "➖ 基本持平"
            else:
                status = "⚠️  有所下降"
            
            print(f"{old_factor:<30} {old_rate:>13.1f}% {new_rate:>13.1f}% {change:>+13.1f}% {status}")
            
            improvements.append({
                'factor': old_factor,
                'new_factor': new_factor,
                'old_rate': old_rate,
                'new_rate': new_rate,
                'change': change
            })
        else:
            print(f"{old_factor:<30} {old_rate:>13.1f}% {'未找到':>13} {'N/A':>13} ❌ 未找到")
    
    print("-" * 95)
    
    # 6. 统计总结
    if improvements:
        avg_old_rate = np.mean([x['old_rate'] for x in improvements])
        avg_new_rate = np.mean([x['new_rate'] for x in improvements])
        avg_change = avg_new_rate - avg_old_rate
        
        improved_count = len([x for x in improvements if x['change'] > 5])
        declined_count = len([x for x in improvements if x['change'] < -5])
        stable_count = len(improvements) - improved_count - declined_count
        
        print(f"\n📊 总体改进效果 (注意：样本规模不同):")
        print(f"   改进前平均覆盖率: {avg_old_rate:.1f}% (821只股票)")
        print(f"   改进后平均覆盖率: {avg_new_rate:.1f}% (20只股票)")
        print(f"   平均变化: {avg_change:+.1f}%")
        print(f"   因子表现: {improved_count}个提升，{stable_count}个稳定，{declined_count}个下降")
        print()
        
        # 按覆盖率分类
        print("📋 按改进后覆盖率分类:")
        excellent = [x for x in improvements if x['new_rate'] >= 95]
        good = [x for x in improvements if 80 <= x['new_rate'] < 95]
        medium = [x for x in improvements if 50 <= x['new_rate'] < 80]
        poor = [x for x in improvements if x['new_rate'] < 50]
        
        print(f"   🌟 优秀 (≥95%): {len(excellent)} 个")
        for imp in excellent[:5]:  # 显示前5个
            print(f"      • {imp['new_factor']}: {imp['new_rate']:.1f}%")
        if len(excellent) > 5:
            print(f"      • ... 还有{len(excellent)-5}个")
        
        print(f"   ✅ 良好 (80-94%): {len(good)} 个")
        for imp in good[:3]:
            print(f"      • {imp['new_factor']}: {imp['new_rate']:.1f}%")
        
        print(f"   ⚠️  中等 (50-79%): {len(medium)} 个")
        for imp in medium:
            print(f"      • {imp['new_factor']}: {imp['new_rate']:.1f}%")
        
        print(f"   ❌ 较低 (<50%): {len(poor)} 个")
        for imp in poor:
            print(f"      • {imp['new_factor']}: {imp['new_rate']:.1f}%")
        
        print()
        
        # 关键改进
        print("🎯 关键发现:")
        
        # 找出改进最大的因子
        best_improvements = sorted([x for x in improvements if x['change'] > 0], 
                                 key=lambda x: x['change'], reverse=True)[:5]
        if best_improvements:
            print("   🚀 改进最明显的因子:")
            for imp in best_improvements:
                print(f"      • {imp['new_factor']}: {imp['old_rate']:.1f}% → {imp['new_rate']:.1f}% (+{imp['change']:.1f}%)")
        
        # 找出需要关注的因子
        concerning = [x for x in improvements if x['new_rate'] < 60]
        if concerning:
            print("   ⚠️  需要关注的因子 (覆盖率<60%):")
            for imp in concerning:
                reason = ""
                if 'tech' in imp['new_factor'] or 'patent' in imp['new_factor']:
                    reason = " (需要R&D数据)"
                elif 'valuation' in imp['new_factor']:
                    reason = " (需要价格和EPS数据)"
                print(f"      • {imp['new_factor']}: {imp['new_rate']:.1f}%{reason}")
    
    # 7. 保存对比结果
    if improvements:
        comparison_data = []
        for imp in improvements:
            comparison_data.append({
                'Old_Factor': imp['factor'],
                'New_Factor': imp['new_factor'],
                'Old_Coverage_821_stocks': imp['old_rate'],
                'New_Coverage_20_stocks': imp['new_rate'],
                'Change': imp['change'],
                'Category': ('优秀' if imp['new_rate'] >= 95 else 
                           '良好' if imp['new_rate'] >= 80 else
                           '中等' if imp['new_rate'] >= 50 else '较低')
            })
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        comparison_df = pd.DataFrame(comparison_data)
        filename = f"colin/coverage_comparison_{timestamp}.csv"
        comparison_df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"\n✅ 详细对比结果已保存到: {filename}")

if __name__ == "__main__":
    compare_factor_coverage()

