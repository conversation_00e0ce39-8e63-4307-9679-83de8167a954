#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mega7股票24因子计算脚本
计算苹果、微软、谷歌、亚马逊、英伟达、<PERSON><PERSON>、特斯拉的完整因子情况
"""

from final_complete_24_factors_with_field_mapping import (
    connect_to_ap_research,
    calculate_complete_24_factors,
    standardize_factors
)
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def get_mega7_stocks():
    """获取Mega7股票列表"""
    mega7_stocks = [
        'AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'NVDA', 'META', 'TSLA'
    ]
    print(f"🎯 Mega7股票列表: {', '.join(mega7_stocks)}")
    return mega7_stocks

def calculate_mega7_factors():
    """计算Mega7股票的24个因子"""
    print("🚀 开始计算Mega7股票的24个因子...")
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        print("❌ 数据库连接失败")
        return None
    
    # 获取Mega7股票列表
    mega7_stocks = get_mega7_stocks()
    
    # 设置计算日期（使用最新可用日期）
    calculation_date = datetime.now().strftime('%Y-%m-%d')
    print(f"📅 计算日期: {calculation_date}")
    
    # 存储所有股票的因子结果
    all_factors = []
    
    for i, stock in enumerate(mega7_stocks, 1):
        print(f"\n{'='*60}")
        print(f"📊 计算第 {i}/{len(mega7_stocks)} 只股票: {stock}")
        print(f"{'='*60}")
        
        try:
            # 计算24个因子
            factors = calculate_complete_24_factors(client, stock, calculation_date)
            
            if factors:
                print(f"✅ {stock} 因子计算成功")
                
                # 标准化因子
                print(f"🔄 开始标准化 {stock} 的因子...")
                standardized_factors = standardize_factors(factors, client, stock, calculation_date)
                
                if standardized_factors:
                    print(f"✅ {stock} 因子标准化成功")
                    
                    # 添加股票标识
                    standardized_factors['stock_symbol'] = stock
                    standardized_factors['calculation_date'] = calculation_date
                    
                    all_factors.append(standardized_factors)
                    
                    # 显示因子概览
                    print(f"\n📈 {stock} 因子概览:")
                    for factor_name, value in standardized_factors.items():
                        if factor_name not in ['stock_symbol', 'calculation_date']:
                            print(f"  {factor_name}: {value:.4f}")
                else:
                    print(f"❌ {stock} 因子标准化失败")
            else:
                print(f"❌ {stock} 因子计算失败")
                
        except Exception as e:
            print(f"❌ 计算 {stock} 时出错: {e}")
            continue
    
    # 汇总结果
    if all_factors:
        print(f"\n{'='*80}")
        print(f"🎉 Mega7因子计算完成！")
        print(f"{'='*80}")
        
        # 创建汇总DataFrame
        df_summary = pd.DataFrame(all_factors)
        
        # 重新排列列顺序
        cols = ['stock_symbol', 'calculation_date'] + [col for col in df_summary.columns if col not in ['stock_symbol', 'calculation_date']]
        df_summary = df_summary[cols]
        
        # 保存结果
        output_file = f'mega7_factors_{calculation_date}.csv'
        df_summary.to_csv(output_file, index=False)
        print(f"💾 结果已保存到: {output_file}")
        
        # 显示汇总统计
        print(f"\n📊 因子覆盖率统计:")
        factor_cols = [col for col in df_summary.columns if col not in ['stock_symbol', 'calculation_date']]
        
        for factor in factor_cols:
            coverage = df_summary[factor].notna().sum()
            coverage_pct = (coverage / len(mega7_stocks)) * 100
            print(f"  {factor}: {coverage}/{len(mega7_stocks)} ({coverage_pct:.1f}%)")
        
        # 显示每只股票的因子数量
        print(f"\n📈 各股票因子数量:")
        for _, row in df_summary.iterrows():
            factor_count = row[factor_cols].notna().sum()
            print(f"  {row['stock_symbol']}: {factor_count}/24 个因子")
        
        return df_summary
    else:
        print("❌ 没有成功计算任何股票的因子")
        return None

def main():
    """主函数"""
    print("🌟 Mega7股票24因子计算系统")
    print("=" * 50)
    
    # 计算因子
    result = calculate_mega7_factors()
    
    if result is not None:
        print(f"\n✅ 计算完成！共处理 {len(result)} 只Mega7股票")
        print(f"📊 结果包含 {len(result.columns)-2} 个因子")
    else:
        print("\n❌ 计算失败")

if __name__ == "__main__":
    main()
