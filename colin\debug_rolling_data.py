#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试滚动数据获取问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from final_complete_24_factors_with_field_mapping import connect_to_ap_research
import pandas as pd

def debug_rolling_data():
    """调试滚动数据获取"""
    print("🔍 开始调试滚动数据获取问题...")
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        print("❌ 无法连接到数据库")
        return
    
    try:
        # 测试股票
        test_stock = 'AAPL'
        calculation_date = '2024-01-15'
        
        print(f"📊 测试股票: {test_stock}")
        print(f"📅 计算日期: {calculation_date}")
        
        # 1. 检查基本面数据表结构
        print("\n🔍 步骤1: 检查基本面数据表结构...")
        
        # 检查表是否存在
        tables_query = "SHOW TABLES FROM ap_research"
        tables = client.execute(tables_query)
        print(f"   数据库中的表: {[t[0] for t in tables]}")
        
        # 检查fundamental_data_with_announcement_dates表结构
        if any('fundamental_data_with_announcement_dates' in t[0] for t in tables):
            print("   ✅ 找到fundamental_data_with_announcement_dates表")
            
            # 检查表结构
            schema_query = "DESCRIBE fundamental_data_with_announcement_dates"
            schema = client.execute(schema_query)
            print(f"   表结构: {[col[0] for col in schema]}")
            
            # 检查数据量
            count_query = "SELECT COUNT(*) FROM fundamental_data_with_announcement_dates"
            total_count = client.execute(count_query)[0][0]
            print(f"   总记录数: {total_count}")
            
            # 检查AAPL的数据
            aapl_count_query = f"SELECT COUNT(*) FROM fundamental_data_with_announcement_dates WHERE stock_symbol = '{test_stock}'"
            aapl_count = client.execute(aapl_count_query)[0][0]
            print(f"   {test_stock}记录数: {aapl_count}")
            
            # 检查AAPL的财务期间
            periods_query = f"""
            SELECT DISTINCT financial_period_absolute, period_end_date, effective_date
            FROM fundamental_data_with_announcement_dates 
            WHERE stock_symbol = '{test_stock}'
            ORDER BY effective_date DESC
            LIMIT 10
            """
            periods = client.execute(periods_query)
            print(f"   {test_stock}最近10个财务期间:")
            for period in periods:
                print(f"     {period[0]} | {period[1]} | {period[2]}")
            
            # 检查字段值
            fields_query = f"""
            SELECT DISTINCT item_name
            FROM fundamental_data_with_announcement_dates 
            WHERE stock_symbol = '{test_stock}'
            ORDER BY item_name
            LIMIT 20
            """
            fields = client.execute(fields_query)
            print(f"   {test_stock}前20个字段名:")
            for field in fields:
                print(f"     {field[0]}")
                
        else:
            print("   ❌ 未找到fundamental_data_with_announcement_dates表")
            
            # 检查其他可能的表名
            for table in tables:
                if 'fundamental' in table[0].lower():
                    print(f"   发现相关表: {table[0]}")
        
        # 2. 测试滚动数据查询
        print("\n🔍 步骤2: 测试滚动数据查询...")
        
        # 模拟滚动统计量函数的查询
        start_date = (pd.to_datetime(calculation_date) - pd.Timedelta(days=3650)).strftime('%Y-%m-%d')
        
        test_query = f"""
        SELECT
            stock_symbol,
            financial_period_absolute,
            period_end_date,
            item_name,
            value,
            effective_date
        FROM fundamental_data_with_announcement_dates
        WHERE stock_symbol = '{test_stock}'
          AND statement_type IN ('income_statement', 'balance_sheet_history', 'cash_flow')
          AND financial_period_absolute REGEXP '^FY[0-9]{{4}}$'
          AND effective_date BETWEEN '{start_date}' AND '{calculation_date}'
        ORDER BY effective_date ASC
        """
        
        print(f"   测试查询: {test_query}")
        
        try:
            result = client.execute(test_query)
            if result:
                print(f"   ✅ 查询成功，返回 {len(result)} 条记录")
                
                # 显示前几条记录
                print("   前5条记录:")
                for i, row in enumerate(result[:5]):
                    print(f"     {i+1}: {row}")
                
                # 检查数据分布
                df = pd.DataFrame(result, columns=[
                    'stock_symbol', 'financial_period_absolute', 'period_end_date',
                    'item_name', 'value', 'effective_date'
                ])
                
                print(f"\n   数据统计:")
                print(f"     财务期间数: {df['financial_period_absolute'].nunique()}")
                print(f"     字段数: {df['item_name'].nunique()}")
                print(f"     日期范围: {df['effective_date'].min()} 到 {df['effective_date'].max()}")
                
                # 透视数据测试
                pivot_data = df.pivot_table(
                    index=['financial_period_absolute', 'period_end_date', 'effective_date'],
                    columns='item_name',
                    values='value',
                    aggfunc='first'
                ).reset_index()
                
                print(f"     透视后数据形状: {pivot_data.shape}")
                print(f"     透视后列数: {len(pivot_data.columns)}")
                
            else:
                print("   ❌ 查询返回空结果")
                
        except Exception as e:
            print(f"   ❌ 查询失败: {e}")
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            client.disconnect()
            print("🔌 数据库连接已关闭")

if __name__ == "__main__":
    debug_rolling_data()
