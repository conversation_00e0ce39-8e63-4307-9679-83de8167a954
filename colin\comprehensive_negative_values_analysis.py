from clickhouse_driver import Client
import pandas as pd
import numpy as np
from datetime import datetime

def connect_to_ap_research():
    """连接到ap_research数据库"""
    client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')
    print("✅ 连接ap_research数据库成功")
    return client

def analyze_negative_values_comprehensive():
    """全面分析所有股票的负税率和负净利润情况"""
    
    print("=== 全面分析负税率和负净利润情况 ===")
    print()
    
    client = connect_to_ap_research()
    
    # 获取所有股票的年报数据
    print("📊 获取所有821只股票的年报数据...")
    query = """
    SELECT 
        stock_symbol,
        financial_period_absolute,
        statement_type,
        item_name,
        value,
        period_end_date
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE financial_period_absolute REGEXP '^FY[0-9]{4}$'
      AND item_name IN (
          'Normalized Net Income - Bottom Line',
          'Income Taxes', 
          'Earnings before Interest & Taxes (EBIT)',
          'Free Cash Flow',
          'Revenue from Business Activities - Total'
      )
    ORDER BY stock_symbol, financial_period_absolute, item_name
    """
    
    result = client.execute(query)
    print(f"   📊 获取到 {len(result)} 条记录")
    
    # 转换为DataFrame
    df = pd.DataFrame(result, columns=[
        'stock_symbol', 'financial_period_absolute', 'statement_type', 'item_name', 'value', 'period_end_date'
    ])
    
    # 透视数据
    pivot_df = df.pivot_table(
        index=['stock_symbol', 'financial_period_absolute', 'period_end_date'],
        columns='item_name',
        values='value',
        aggfunc='first'
    ).reset_index()
    
    pivot_df['fiscal_year'] = pivot_df['financial_period_absolute'].str.extract(r'FY(\d{4})').astype(int)
    pivot_df = pivot_df.sort_values(['stock_symbol', 'fiscal_year'])
    
    print(f"   📊 处理后数据: {len(pivot_df)} 行，{len(pivot_df['stock_symbol'].unique())} 只股票")
    print()
    
    # 分析结果存储
    negative_net_income_cases = []
    negative_tax_rate_cases = []
    negative_fcf_cases = []
    negative_revenue_cases = []
    
    # 按股票分组分析
    for stock_symbol, stock_data in pivot_df.groupby('stock_symbol'):
        stock_data = stock_data.sort_values('fiscal_year')
        
        for idx, row in stock_data.iterrows():
            year = row['fiscal_year']
            period = row['financial_period_absolute']
            
            # 1. 检查负净利润
            net_income = row.get('Normalized Net Income - Bottom Line', 0) or 0
            if net_income < 0:
                negative_net_income_cases.append({
                    'stock_symbol': stock_symbol,
                    'fiscal_year': year,
                    'period': period,
                    'net_income': net_income,
                    'period_end_date': row['period_end_date']
                })
            
            # 2. 检查负税率
            tax_expense = row.get('Income Taxes', 0) or 0
            ebit = row.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
            
            if ebit > 0:  # 只有正EBIT才计算税率
                tax_rate = (tax_expense / ebit) * 100
                if tax_rate < 0:
                    negative_tax_rate_cases.append({
                        'stock_symbol': stock_symbol,
                        'fiscal_year': year,
                        'period': period,
                        'tax_expense': tax_expense,
                        'ebit': ebit,
                        'tax_rate': tax_rate,
                        'period_end_date': row['period_end_date']
                    })
            
            # 3. 检查负FCF
            fcf = row.get('Free Cash Flow', 0) or 0
            if fcf < 0:
                negative_fcf_cases.append({
                    'stock_symbol': stock_symbol,
                    'fiscal_year': year,
                    'period': period,
                    'fcf': fcf,
                    'period_end_date': row['period_end_date']
                })
            
            # 4. 检查负营收
            revenue = row.get('Revenue from Business Activities - Total', 0) or 0
            if revenue < 0:
                negative_revenue_cases.append({
                    'stock_symbol': stock_symbol,
                    'fiscal_year': year,
                    'period': period,
                    'revenue': revenue,
                    'period_end_date': row['period_end_date']
                })
    
    # 输出详细结果
    print("=" * 80)
    print("📋 1. 负净利润情况详细列表")
    print("=" * 80)
    print(f"总计发现 {len(negative_net_income_cases)} 个负净利润案例")
    print()
    
    if negative_net_income_cases:
        # 按股票分组统计
        ni_by_stock = {}
        for case in negative_net_income_cases:
            stock = case['stock_symbol']
            if stock not in ni_by_stock:
                ni_by_stock[stock] = []
            ni_by_stock[stock].append(case)
        
        print(f"涉及股票数量: {len(ni_by_stock)} 只")
        print("-" * 80)
        print(f"{'股票':<8} {'年份':<8} {'净利润(百万)':<15} {'财报期间':<12} {'期间结束日期'}")
        print("-" * 80)
        
        for stock in sorted(ni_by_stock.keys()):
            cases = sorted(ni_by_stock[stock], key=lambda x: x['fiscal_year'])
            for i, case in enumerate(cases):
                stock_display = stock if i == 0 else ""
                print(f"{stock_display:<8} {case['fiscal_year']:<8} {case['net_income']:>12,.0f} {case['period']:<12} {case['period_end_date']}")
            print()
    
    print("=" * 80)
    print("📋 2. 负税率情况详细列表")
    print("=" * 80)
    print(f"总计发现 {len(negative_tax_rate_cases)} 个负税率案例")
    print()
    
    if negative_tax_rate_cases:
        # 按股票分组统计
        tax_by_stock = {}
        for case in negative_tax_rate_cases:
            stock = case['stock_symbol']
            if stock not in tax_by_stock:
                tax_by_stock[stock] = []
            tax_by_stock[stock].append(case)
        
        print(f"涉及股票数量: {len(tax_by_stock)} 只")
        print("-" * 100)
        print(f"{'股票':<8} {'年份':<8} {'税费(百万)':<12} {'EBIT(百万)':<12} {'税率%':<10} {'财报期间':<12} {'期间结束日期'}")
        print("-" * 100)
        
        for stock in sorted(tax_by_stock.keys()):
            cases = sorted(tax_by_stock[stock], key=lambda x: x['fiscal_year'])
            for i, case in enumerate(cases):
                stock_display = stock if i == 0 else ""
                print(f"{stock_display:<8} {case['fiscal_year']:<8} {case['tax_expense']:>10,.0f} {case['ebit']:>10,.0f} "
                      f"{case['tax_rate']:>8.1f} {case['period']:<12} {case['period_end_date']}")
            print()
    
    print("=" * 80)
    print("📋 3. 负自由现金流情况详细列表")
    print("=" * 80)
    print(f"总计发现 {len(negative_fcf_cases)} 个负FCF案例")
    print()
    
    if negative_fcf_cases:
        # 按股票分组统计
        fcf_by_stock = {}
        for case in negative_fcf_cases:
            stock = case['stock_symbol']
            if stock not in fcf_by_stock:
                fcf_by_stock[stock] = []
            fcf_by_stock[stock].append(case)
        
        print(f"涉及股票数量: {len(fcf_by_stock)} 只")
        print("-" * 80)
        print(f"{'股票':<8} {'年份':<8} {'FCF(百万)':<15} {'财报期间':<12} {'期间结束日期'}")
        print("-" * 80)
        
        # 只显示前20个股票，避免输出过长
        displayed_stocks = 0
        for stock in sorted(fcf_by_stock.keys()):
            if displayed_stocks >= 20:
                remaining = len(fcf_by_stock) - displayed_stocks
                print(f"... 还有 {remaining} 只股票有负FCF情况")
                break
                
            cases = sorted(fcf_by_stock[stock], key=lambda x: x['fiscal_year'])
            for i, case in enumerate(cases):
                stock_display = stock if i == 0 else ""
                print(f"{stock_display:<8} {case['fiscal_year']:<8} {case['fcf']:>12,.0f} {case['period']:<12} {case['period_end_date']}")
            print()
            displayed_stocks += 1
    
    print("=" * 80)
    print("📋 4. 负营收情况详细列表")
    print("=" * 80)
    print(f"总计发现 {len(negative_revenue_cases)} 个负营收案例")
    print()
    
    if negative_revenue_cases:
        print("-" * 80)
        print(f"{'股票':<8} {'年份':<8} {'营收(百万)':<15} {'财报期间':<12} {'期间结束日期'}")
        print("-" * 80)
        
        for case in negative_revenue_cases:
            print(f"{case['stock_symbol']:<8} {case['fiscal_year']:<8} {case['revenue']:>12,.0f} "
                  f"{case['period']:<12} {case['period_end_date']}")
    
    print("=" * 80)
    print("📊 统计总结")
    print("=" * 80)
    print(f"1. 负净利润: {len(negative_net_income_cases)} 个案例，涉及 {len(set(c['stock_symbol'] for c in negative_net_income_cases))} 只股票")
    print(f"2. 负税率:   {len(negative_tax_rate_cases)} 个案例，涉及 {len(set(c['stock_symbol'] for c in negative_tax_rate_cases))} 只股票")
    print(f"3. 负FCF:    {len(negative_fcf_cases)} 个案例，涉及 {len(set(c['stock_symbol'] for c in negative_fcf_cases))} 只股票")
    print(f"4. 负营收:   {len(negative_revenue_cases)} 个案例，涉及 {len(set(c['stock_symbol'] for c in negative_revenue_cases))} 只股票")
    print()
    
    # 保存详细结果到CSV
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    if negative_net_income_cases:
        ni_df = pd.DataFrame(negative_net_income_cases)
        ni_df.to_csv(f'colin/negative_net_income_cases_{timestamp}.csv', index=False, encoding='utf-8-sig')
        print(f"✅ 负净利润详细数据已保存到: colin/negative_net_income_cases_{timestamp}.csv")
    
    if negative_tax_rate_cases:
        tax_df = pd.DataFrame(negative_tax_rate_cases)
        tax_df.to_csv(f'colin/negative_tax_rate_cases_{timestamp}.csv', index=False, encoding='utf-8-sig')
        print(f"✅ 负税率详细数据已保存到: colin/negative_tax_rate_cases_{timestamp}.csv")
    
    if negative_fcf_cases:
        fcf_df = pd.DataFrame(negative_fcf_cases)
        fcf_df.to_csv(f'colin/negative_fcf_cases_{timestamp}.csv', index=False, encoding='utf-8-sig')
        print(f"✅ 负FCF详细数据已保存到: colin/negative_fcf_cases_{timestamp}.csv")
    
    if negative_revenue_cases:
        rev_df = pd.DataFrame(negative_revenue_cases)
        rev_df.to_csv(f'colin/negative_revenue_cases_{timestamp}.csv', index=False, encoding='utf-8-sig')
        print(f"✅ 负营收详细数据已保存到: colin/negative_revenue_cases_{timestamp}.csv")

if __name__ == "__main__":
    analyze_negative_values_comprehensive()

