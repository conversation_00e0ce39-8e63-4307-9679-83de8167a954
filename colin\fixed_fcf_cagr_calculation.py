import pandas as pd
import numpy as np
from datetime import datetime
from clickhouse_driver import Client

def connect_to_ap_research():
    """连接到ap_research数据库"""
    try:
        client = Client(
            host='************',
            port=9000,
            user='default',
            password='5ur2pK8WZQdy2',
            database='ap_research'
        )
        print("✅ 连接ap_research数据库成功")
        return client
    except Exception as e:
        print(f"❌ 连接数据库失败: {e}")
        return None

def get_annual_data_only(client, stock_symbol):
    """
    获取严格的年报数据（排除季报）
    """
    query = f"""
    SELECT 
        financial_period_absolute,
        item_name,
        value
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = '{stock_symbol}'
      AND financial_period_absolute REGEXP '^FY[0-9]{{4}}$'  -- 严格匹配FY+4位年份
      AND value IS NOT NULL
    ORDER BY financial_period_absolute
    """
    
    try:
        result = client.execute(query)
        df = pd.DataFrame(result, columns=['period', 'item_name', 'value'])
        
        # 转换为透视表
        pivot_df = df.pivot_table(
            index='period', 
            columns='item_name', 
            values='value', 
            aggfunc='first'  # 如果有重复，取第一个
        ).reset_index()
        
        return pivot_df
    except Exception as e:
        print(f"❌ 获取{stock_symbol}年报数据失败: {e}")
        return pd.DataFrame()

def calculate_fcf_cagr_fixed(stock_data, stock_symbol):
    """
    修复后的FCF CAGR计算
    """
    if len(stock_data) < 3:
        return None, "数据不足(需要≥3年)"
    
    # 提取FCF数据
    fcf_column = None
    for col in ['Free Cash Flow', 'Operating Cash Flow', 'Cash Flow from Operations']:
        if col in stock_data.columns:
            fcf_column = col
            break
    
    if fcf_column is None:
        return None, "未找到FCF字段"
    
    # 过滤有效FCF数据
    valid_data = stock_data[stock_data[fcf_column].notna() & (stock_data[fcf_column] != 0)].copy()
    valid_data = valid_data.sort_values('period')
    
    if len(valid_data) < 3:
        return None, f"有效FCF数据不足({len(valid_data)}年)"
    
    # 找到连续的正值FCF区间进行CAGR计算
    fcf_values = valid_data[fcf_column].values
    periods = valid_data['period'].values
    
    # 方法1：尝试使用最新的连续正值区间
    positive_indices = []
    for i, fcf in enumerate(fcf_values):
        if fcf > 0:
            positive_indices.append(i)
    
    if len(positive_indices) >= 3:
        # 找最长的连续正值区间
        best_start = best_end = 0
        best_length = 0
        
        current_start = positive_indices[0]
        current_end = positive_indices[0]
        
        for i in range(1, len(positive_indices)):
            if positive_indices[i] == positive_indices[i-1] + 1:
                # 连续
                current_end = positive_indices[i]
            else:
                # 不连续，检查当前区间
                current_length = current_end - current_start + 1
                if current_length > best_length:
                    best_start = current_start
                    best_end = current_end
                    best_length = current_length
                
                current_start = current_end = positive_indices[i]
        
        # 检查最后一个区间
        current_length = current_end - current_start + 1
        if current_length > best_length:
            best_start = current_start
            best_end = current_end
            best_length = current_length
        
        if best_length >= 3:
            # 使用最佳连续区间计算CAGR
            start_fcf = fcf_values[best_start]
            end_fcf = fcf_values[best_end]
            years = best_end - best_start
            
            if start_fcf > 0 and end_fcf > 0 and years > 0:
                cagr = ((end_fcf / start_fcf) ** (1/years) - 1) * 100
                period_info = f"{periods[best_start]}-{periods[best_end]}({years+1}年)"
                return cagr, f"连续正值区间{period_info}"
    
    # 方法2：如果没有足够长的连续正值区间，尝试首尾正值
    if fcf_values[0] > 0 and fcf_values[-1] > 0:
        years = len(fcf_values) - 1
        cagr = ((fcf_values[-1] / fcf_values[0]) ** (1/years) - 1) * 100
        period_info = f"{periods[0]}-{periods[-1]}({len(fcf_values)}年)"
        return cagr, f"首尾正值{period_info}"
    
    # 方法3：找到最近的两个正值进行计算
    recent_positive = []
    for i in range(len(fcf_values)-1, -1, -1):
        if fcf_values[i] > 0:
            recent_positive.append((i, fcf_values[i], periods[i]))
            if len(recent_positive) >= 2:
                break
    
    if len(recent_positive) >= 2:
        end_idx, end_fcf, end_period = recent_positive[0]
        start_idx, start_fcf, start_period = recent_positive[1]
        years = end_idx - start_idx
        
        if years > 0:
            cagr = ((end_fcf / start_fcf) ** (1/years) - 1) * 100
            return cagr, f"最近正值{start_period}-{end_period}({years+1}年)"
    
    return None, "无法找到合适的正值区间进行CAGR计算"

def main():
    """主函数"""
    print("=== 修复FCF CAGR计算逻辑 ===")
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        return
    
    # 测试问题股票
    test_stocks = ['AMZN', 'TSLA', 'META', 'AAPL']
    
    results = []
    
    for stock in test_stocks:
        print(f"\n🔍 测试 {stock}:")
        
        # 获取严格的年报数据
        stock_data = get_annual_data_only(client, stock)
        
        if len(stock_data) == 0:
            print(f"   ❌ 未获取到数据")
            continue
        
        print(f"   📊 获取到 {len(stock_data)} 年的年报数据")
        
        # 显示FCF数据
        fcf_col = None
        for col in ['Free Cash Flow', 'Operating Cash Flow']:
            if col in stock_data.columns:
                fcf_col = col
                break
        
        if fcf_col:
            print(f"   💰 FCF数据 ({fcf_col}):")
            for _, row in stock_data.tail(8).iterrows():  # 显示最近8年
                period = row['period']
                fcf = row[fcf_col]
                if pd.notna(fcf):
                    status = "✅" if fcf > 0 else "❌"
                    print(f"     {period}: {fcf:,.0f} {status}")
        
        # 计算FCF CAGR
        cagr, note = calculate_fcf_cagr_fixed(stock_data, stock)
        
        if cagr is not None:
            print(f"   🎯 FCF CAGR: {cagr:.4f}% ({note})")
            results.append({
                'stock_symbol': stock,
                'fcf_cagr': cagr,
                'calculation_note': note,
                'status': '成功'
            })
        else:
            print(f"   ❌ FCF CAGR: 无法计算 ({note})")
            results.append({
                'stock_symbol': stock,
                'fcf_cagr': None,
                'calculation_note': note,
                'status': '失败'
            })
    
    # 保存结果
    results_df = pd.DataFrame(results)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f"colin/fixed_fcf_cagr_results_{timestamp}.csv"
    results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"\n✅ 修复结果已保存到: {output_file}")
    
    # 显示对比
    print(f"\n📊 修复前后对比:")
    print(f"   AMZN: 修复前=NaN, 修复后={results_df[results_df['stock_symbol']=='AMZN']['fcf_cagr'].iloc[0]:.4f}%")
    
    client.disconnect()
    
    print(f"\n🎯 修复要点:")
    print(f"1. ✅ 严格过滤年报数据（排除季报）")
    print(f"2. ✅ 智能处理负值FCF（寻找正值区间）")
    print(f"3. ✅ 多种CAGR计算策略（连续区间、首尾正值、最近正值）")
    print(f"4. ✅ 详细的计算说明和透明度")

if __name__ == "__main__":
    main()

