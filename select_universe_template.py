# %% [markdown]
# ![QuantConnect Logo](https://cdn.quantconnect.com/web/i/icon.png)
# <hr>

# %%
# QuantBook Analysis Tool
# For more information see [https://www.quantconnect.com/docs/v2/our-platform/research/getting-started]

# %%
from AlgorithmImports import *
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import time

qb = QuantBook()
end = datetime.now()
start = end - timedelta(days=3*365)

def filter_function(fundamentals):
    sorted_by_pe_ratio = sorted(
        [f for f in fundamentals if (f.HasFundamentalData)], 
        key=lambda fundamental: fundamental.valuation_ratios.pe_ratio
    )
    return [fundamental.symbol for fundamental in sorted_by_pe_ratio]

universe = qb.add_universe(filter_function)

# %%
universe_history = qb.universe_history(universe, start, end)

# %%
universe_history = universe_history.droplevel('symbol', axis=0)
for date, fundamentals in universe_history.items():
    for fundamental in fundamentals:
        symbol = fundamental.symbol
        price = fundamental.price
        if fundamental.has_fundamental_data:
            pe_ratio = fundamental.valuation_ratios.pe_ratio

# %%
first_record = universe_history.iloc[0]  # 或 universe_history[0]
len(first_record)

# %%

data = [{
    'symbol': f.Symbol.Value,
    'price': f.Price,
    'pe': f.ValuationRatios.PERatio if f.HasFundamentalData else None
} for f in first_record]
df = pd.DataFrame(data)
df

# %%
import gc

# 初始化存储容器
structured_data = []
unique_symbols = set()

# 分块处理参数
chunk_size = 30  # 每次处理30天数据（根据内存调整）
total_days = len(universe_history)

for chunk_start in range(0, total_days, chunk_size):
    chunk_end = min(chunk_start + chunk_size, total_days)
    print(f"处理日期段: {chunk_start+1}-{chunk_end}/{total_days}")
    
    try:
        # 处理当前分块
        for idx in range(chunk_start, chunk_end):
            daily_fundamentals = universe_history.iloc[idx]
            date = universe_history.index[idx]
            
            for fundamental in daily_fundamentals:
                # 基础字段提取（带错误处理）
                try:
                    symbol = fundamental.Symbol.Value
                    price = fundamental.Price
                    
                    # 估值指标
                    valuation = getattr(fundamental, 'ValuationRatios', None)
                    pe = getattr(valuation, 'PERatio', None) if valuation else None
                    pb = getattr(valuation, 'PBRatio', None) if valuation else None
                    
                    # 记录数据
                    structured_data.append({
                        'date': date,
                        'symbol': symbol,
                        'price': price,
                        'pe_ratio': pe,
                        'pb_ratio': pb
                    })
                    
                    # 收集唯一代码
                    unique_symbols.add(symbol)
                
                except Exception as e:
                    print(f"解析错误 @ {date}: {str(e)}")
                    continue
        
        # 强制释放内存
        del daily_fundamentals
        gc.collect()
        
    except MemoryError:
        print(f"内存不足，已处理到第{chunk_start}天")
        break

# 转换为结构化DataFrame
df_structured = pd.DataFrame(structured_data)
df_structured['date'] = pd.to_datetime(df_structured['date'])  # 确保日期格式

# 设置多级索引（date + symbol）
df_structured.set_index(['date', 'symbol'], inplace=True)

# 输出结果验证
print("\n处理完成！")
print(f"总记录数: {len(df_structured)}")
print(f"唯一股票数: {len(unique_symbols)}")
print("\n前5条记录示例:")
print(df_structured.head())

# 可选：保存结果
df_structured.to_csv('structured_fundamentals.csv')
with open('unique_symbols.txt', 'w') as f:
    f.write('\n'.join(sorted(unique_symbols)))

# %%
# 转换为DataFrame
df_structured

# %%
unique_symbol_list = sorted(list(unique_symbols))
unique_symbol_list
len(unique_symbol_list)

# %%



