from clickhouse_driver import Client
import pandas as pd

# 连接ClickHouse数据库
client = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 年报数据缺失情况检查（高效版本）===")

# 1. 先统计总体情况
print("\n1. 统计总体情况...")
query_summary = """
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT stock_symbol) as total_stocks
FROM priority_quality_fundamental_data_complete_deduped
WHERE period_end_date IS NOT NULL AND period_end_date != ''
"""

result_summary = client.execute(query_summary)
total_records, total_stocks = result_summary[0]
print(f"  总财务数据记录数: {total_records:,}")
print(f"  唯一股票数: {total_stocks:,}")

# 2. 统计年报数据情况
print("\n2. 统计年报数据情况...")
query_annual = """
SELECT 
    COUNT(*) as annual_records,
    COUNT(DISTINCT stock_symbol) as annual_stocks
FROM priority_quality_fundamental_data_complete_deduped
WHERE period_end_date IS NOT NULL AND period_end_date != ''
  AND (financial_period_absolute LIKE '%Q4%' OR financial_period_absolute REGEXP 'FY[0-9]{4}$')
"""

result_annual = client.execute(query_annual)
annual_records, annual_stocks = result_annual[0]
print(f"  年报数据记录数: {annual_records:,}")
print(f"  有年报数据的股票数: {annual_stocks:,}")

# 3. 计算缺失情况
missing_stocks = total_stocks - annual_stocks
coverage_rate = annual_stocks / total_stocks * 100

print(f"\n3. 年报数据覆盖情况:")
print(f"  总股票数: {total_stocks:,}")
print(f"  有年报数据的股票数: {annual_stocks:,}")
print(f"  缺失年报数据的股票数: {missing_stocks:,}")
print(f"  年报数据覆盖率: {coverage_rate:.1f}%")

# 4. 查找缺失年报数据的股票
print("\n4. 查找缺失年报数据的股票...")
query_missing = """
SELECT stock_symbol
FROM (
    SELECT DISTINCT stock_symbol
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE period_end_date IS NOT NULL AND period_end_date != ''
) all_stocks
WHERE stock_symbol NOT IN (
    SELECT DISTINCT stock_symbol
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE period_end_date IS NOT NULL AND period_end_date != ''
      AND (financial_period_absolute LIKE '%Q4%' OR financial_period_absolute REGEXP 'FY[0-9]{4}$')
)
ORDER BY stock_symbol
LIMIT 20
"""

result_missing = client.execute(query_missing)
if result_missing:
    print(f"  缺失年报数据的股票列表（前20只）:")
    for i, (stock,) in enumerate(result_missing):
        print(f"    {i+1:2d}. {stock}")
    if missing_stocks > 20:
        print(f"    ... 还有 {missing_stocks - 20} 只股票")
else:
    print("  所有股票都有年报数据！")

# 5. 分析年报数据的年份分布
print("\n5. 年报数据年份分布...")
query_year_dist = """
SELECT 
    year_count,
    COUNT(*) as stock_count
FROM (
    SELECT 
        stock_symbol,
        COUNT(DISTINCT 
            CASE 
                WHEN financial_period_absolute REGEXP 'FY([0-9]{4})$'
                THEN extractAll(financial_period_absolute, 'FY([0-9]{4})$')[1]
                ELSE NULL
            END
        ) as year_count
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE period_end_date IS NOT NULL AND period_end_date != ''
      AND (financial_period_absolute LIKE '%Q4%' OR financial_period_absolute REGEXP 'FY[0-9]{4}$')
    GROUP BY stock_symbol
) year_stats
GROUP BY year_count
ORDER BY year_count
"""

result_year_dist = client.execute(query_year_dist)
if result_year_dist:
    print("  年份数分布:")
    total_annual_stocks = sum(stock_count for _, stock_count in result_year_dist)
    avg_years = sum(year_count * stock_count for year_count, stock_count in result_year_dist) / total_annual_stocks
    
    for year_count, stock_count in result_year_dist:
        print(f"    {year_count}年: {stock_count}只股票")
    
    print(f"  平均年份数: {avg_years:.1f}")

# 6. 分析period_end_date的范围
print("\n6. Period End Date范围...")
query_date_range = """
SELECT 
    MIN(period_end_date) as min_date,
    MAX(period_end_date) as max_date,
    COUNT(DISTINCT period_end_date) as unique_dates
FROM priority_quality_fundamental_data_complete_deduped
WHERE period_end_date IS NOT NULL AND period_end_date != ''
  AND (financial_period_absolute LIKE '%Q4%' OR financial_period_absolute REGEXP 'FY[0-9]{4}$')
"""

result_date_range = client.execute(query_date_range)
if result_date_range:
    min_date, max_date, unique_dates = result_date_range[0]
    print(f"  年报Period End Date范围: {min_date} 到 {max_date}")
    print(f"  唯一年报Period End Date数量: {unique_dates:,}")

print("\n=== 检查完成 ===")
