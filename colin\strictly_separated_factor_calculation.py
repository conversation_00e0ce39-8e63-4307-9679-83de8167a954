import pandas as pd
import numpy as np
from datetime import datetime
from clickhouse_driver import Client

def connect_to_ap_research():
    """连接到ap_research数据库"""
    try:
        client = Client(
            host='************',
            port=9000,
            user='default',
            password='5ur2pK8WZQdy2',
            database='ap_research'
        )
        print("✅ 连接ap_research数据库成功")
        return client
    except Exception as e:
        print(f"❌ 连接数据库失败: {e}")
        return None

def get_strictly_annual_data(client, stock_symbol):
    """
    严格获取年报数据，完全排除季报
    """
    query = f"""
    SELECT 
        financial_period_absolute,
        item_name,
        value
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = '{stock_symbol}'
      AND financial_period_absolute REGEXP '^FY[0-9]{{4}}$'  -- 严格匹配：只要FY+4位数字
      AND value IS NOT NULL
    ORDER BY financial_period_absolute
    """
    
    try:
        result = client.execute(query)
        df = pd.DataFrame(result, columns=['period', 'item_name', 'value'])
        
        if len(df) == 0:
            return pd.DataFrame()
        
        # 转换为透视表
        pivot_df = df.pivot_table(
            index='period', 
            columns='item_name', 
            values='value', 
            aggfunc='first'
        ).reset_index()
        
        return pivot_df
    except Exception as e:
        print(f"❌ 获取{stock_symbol}年报数据失败: {e}")
        return pd.DataFrame()

def get_strictly_quarterly_data(client, stock_symbol):
    """
    严格获取季报数据，完全排除年报
    """
    query = f"""
    SELECT 
        financial_period_absolute,
        item_name,
        value
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = '{stock_symbol}'
      AND financial_period_absolute REGEXP '^FY[0-9]{{4}}Q[1-4]$'  -- 严格匹配：FY+4位数字+Q+季度
      AND value IS NOT NULL
    ORDER BY financial_period_absolute
    """
    
    try:
        result = client.execute(query)
        df = pd.DataFrame(result, columns=['period', 'item_name', 'value'])
        
        if len(df) == 0:
            return pd.DataFrame()
        
        # 转换为透视表
        pivot_df = df.pivot_table(
            index='period', 
            columns='item_name', 
            values='value', 
            aggfunc='first'
        ).reset_index()
        
        return pivot_df
    except Exception as e:
        print(f"❌ 获取{stock_symbol}季报数据失败: {e}")
        return pd.DataFrame()

def calculate_annual_factors(annual_data, stock_symbol):
    """
    基于年报数据计算因子
    """
    factors = {'stock_symbol': stock_symbol, 'data_type': 'ANNUAL'}
    
    if len(annual_data) == 0:
        return factors
    
    latest_data = annual_data.iloc[-1]
    
    # FCF CAGR (年报版本)
    try:
        if len(annual_data) >= 3:
            fcf_column = None
            for col in ['Free Cash Flow', 'Operating Cash Flow']:
                if col in annual_data.columns:
                    fcf_column = col
                    break
            
            if fcf_column:
                # 只使用年报FCF数据
                valid_data = annual_data[annual_data[fcf_column].notna()].copy()
                
                if len(valid_data) >= 3:
                    # 寻找连续正值区间
                    fcf_values = valid_data[fcf_column].values
                    periods = valid_data['period'].values
                    
                    # 找最长连续正值区间
                    best_start = best_end = -1
                    best_length = 0
                    
                    current_start = -1
                    for i, fcf in enumerate(fcf_values):
                        if fcf > 0:
                            if current_start == -1:
                                current_start = i
                            current_end = i
                            current_length = current_end - current_start + 1
                            if current_length > best_length:
                                best_start = current_start
                                best_end = current_end
                                best_length = current_length
                        else:
                            current_start = -1
                    
                    if best_length >= 3:
                        start_fcf = fcf_values[best_start]
                        end_fcf = fcf_values[best_end]
                        years = best_end - best_start
                        
                        cagr = ((end_fcf / start_fcf) ** (1/years) - 1) * 100
                        factors['fcf_cagr_annual'] = cagr
                        factors['fcf_cagr_period'] = f"{periods[best_start]}-{periods[best_end]}"
                    else:
                        factors['fcf_cagr_annual'] = None
                        factors['fcf_cagr_period'] = "无足够连续正值"
                else:
                    factors['fcf_cagr_annual'] = None
                    factors['fcf_cagr_period'] = "年报FCF数据不足"
            else:
                factors['fcf_cagr_annual'] = None
                factors['fcf_cagr_period'] = "未找到FCF字段"
        else:
            factors['fcf_cagr_annual'] = None
            factors['fcf_cagr_period'] = "年报数据不足3年"
    except Exception as e:
        factors['fcf_cagr_annual'] = None
        factors['fcf_cagr_period'] = f"计算错误: {e}"
    
    # ROIC (年报版本)
    try:
        net_income = latest_data.get('Normalized Net Income - Bottom Line', 0) or 0
        debt = latest_data.get('Debt - Total', 0) or 0
        
        # 尝试多个股东权益字段
        equity = 0
        for eq_field in [
            "Shareholders' Equity - Attributable to Parent Shareholders - Total",
            "Common Equity - Total", 
            "Common Equity Attributable to Parent Shareholders"
        ]:
            if eq_field in latest_data and pd.notna(latest_data[eq_field]):
                equity = latest_data[eq_field]
                break
        
        invested_capital = debt + equity
        if invested_capital > 0:
            factors['roic_annual'] = (net_income / invested_capital) * 100
        else:
            factors['roic_annual'] = None
    except:
        factors['roic_annual'] = None
    
    return factors

def calculate_quarterly_factors(quarterly_data, stock_symbol):
    """
    基于季报数据计算因子
    """
    factors = {'stock_symbol': stock_symbol, 'data_type': 'QUARTERLY'}
    
    if len(quarterly_data) == 0:
        return factors
    
    # 获取最新季报
    latest_quarterly = quarterly_data.iloc[-1]
    
    # 季报FCF CAGR (同季度比较)
    try:
        if len(quarterly_data) >= 12:  # 至少3年的季报数据
            fcf_column = None
            for col in ['Free Cash Flow', 'Operating Cash Flow']:
                if col in quarterly_data.columns:
                    fcf_column = col
                    break
            
            if fcf_column:
                # 按季度分组，只比较相同季度
                quarterly_data['year'] = quarterly_data['period'].str[2:6]
                quarterly_data['quarter'] = quarterly_data['period'].str[6:8]
                
                latest_quarter = quarterly_data.iloc[-1]['quarter']
                same_quarter_data = quarterly_data[quarterly_data['quarter'] == latest_quarter].copy()
                
                if len(same_quarter_data) >= 3:
                    valid_data = same_quarter_data[same_quarter_data[fcf_column].notna()].copy()
                    valid_data = valid_data.sort_values('period')
                    
                    if len(valid_data) >= 3:
                        fcf_values = valid_data[fcf_column].values
                        if fcf_values[0] > 0 and fcf_values[-1] > 0:
                            years = len(fcf_values) - 1
                            cagr = ((fcf_values[-1] / fcf_values[0]) ** (1/years) - 1) * 100
                            factors['fcf_cagr_quarterly'] = cagr
                            factors['fcf_cagr_quarter_type'] = f"{latest_quarter}季度同比"
                        else:
                            factors['fcf_cagr_quarterly'] = None
                            factors['fcf_cagr_quarter_type'] = "首尾FCF非正值"
                    else:
                        factors['fcf_cagr_quarterly'] = None
                        factors['fcf_cagr_quarter_type'] = "同季度有效数据不足"
                else:
                    factors['fcf_cagr_quarterly'] = None
                    factors['fcf_cagr_quarter_type'] = "同季度数据不足3年"
            else:
                factors['fcf_cagr_quarterly'] = None
                factors['fcf_cagr_quarter_type'] = "未找到FCF字段"
        else:
            factors['fcf_cagr_quarterly'] = None
            factors['fcf_cagr_quarter_type'] = "季报数据不足3年"
    except Exception as e:
        factors['fcf_cagr_quarterly'] = None
        factors['fcf_cagr_quarter_type'] = f"计算错误: {e}"
    
    return factors

def main():
    """主函数"""
    print("=== 严格分离的年报/季报因子计算 ===")
    print()
    print("📋 计算原则:")
    print("   ✅ 年报数据只能跟年报数据比")
    print("   ✅ 季报数据只能跟季报数据比")
    print("   ✅ 完全隔离，绝不混合")
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        return
    
    # 测试Mega7股票
    test_stocks = ['AMZN', 'TSLA', 'META', 'AAPL']
    
    all_results = []
    
    for stock in test_stocks:
        print(f"\n🔍 分析 {stock}:")
        
        # 1. 获取严格的年报数据
        annual_data = get_strictly_annual_data(client, stock)
        print(f"   📊 年报数据: {len(annual_data)} 年")
        
        # 2. 获取严格的季报数据
        quarterly_data = get_strictly_quarterly_data(client, stock)
        print(f"   📊 季报数据: {len(quarterly_data)} 季度")
        
        # 3. 基于年报数据计算因子
        annual_factors = calculate_annual_factors(annual_data, stock)
        
        # 4. 基于季报数据计算因子
        quarterly_factors = calculate_quarterly_factors(quarterly_data, stock)
        
        # 5. 显示结果
        print(f"   🎯 年报FCF CAGR: {annual_factors.get('fcf_cagr_annual', 'N/A')}")
        if annual_factors.get('fcf_cagr_period'):
            print(f"      计算期间: {annual_factors['fcf_cagr_period']}")
        
        print(f"   🎯 季报FCF CAGR: {quarterly_factors.get('fcf_cagr_quarterly', 'N/A')}")
        if quarterly_factors.get('fcf_cagr_quarter_type'):
            print(f"      计算说明: {quarterly_factors['fcf_cagr_quarter_type']}")
        
        print(f"   💼 年报ROIC: {annual_factors.get('roic_annual', 'N/A')}")
        
        all_results.extend([annual_factors, quarterly_factors])
    
    # 保存结果
    results_df = pd.DataFrame(all_results)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f"colin/separated_annual_quarterly_factors_{timestamp}.csv"
    results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"\n✅ 严格分离的因子结果已保存到: {output_file}")
    
    client.disconnect()
    
    print(f"\n🎯 修正要点:")
    print(f"1. ✅ 年报和季报完全分离计算")
    print(f"2. ✅ 年报CAGR只用年报数据")
    print(f"3. ✅ 季报CAGR只用同季度季报数据")
    print(f"4. ✅ 绝不混合不同类型的财务报告")
    print(f"\n🙏 感谢您的纠正！这样才是正确的财务分析方法！")

if __name__ == "__main__":
    main()

