import pandas as pd
from datetime import datetime, timedelta

print("=== 缺失季报数据补充机制 - 表结构设计 ===")

# 模拟的表结构设计
def create_quarterly_filling_table_structure():
    """
    创建季报数据补充表的结构示例
    """
    
    # 定义表结构
    table_structure = {
        # 基础标识字段
        'id': 'String',  # 主键，格式：{stock_symbol}|{financial_period}|{item_name}
        'stock_symbol': 'String',  # 股票代码，如 AAPL, MSFT
        'financial_period_absolute': 'String',  # 财务期间，如 FY2024Q1
        'statement_type': 'String',  # 报表类型：income_statement, balance_sheet, cash_flow
        'item_name': 'String',  # 会计科目名称
        'original_value': 'Nullable(Float64)',  # 原始值（如果存在）
        
        # 数据来源和状态
        'data_status': 'String',  # 数据状态：original, missing, estimated, corrected
        'fill_method': 'Nullable(String)',  # 填补方法：rolling_average, half_year_split, annual_split
        'source_periods': 'Nullable(String)',  # 用于计算的源期间，如 "FY2023Q3,FY2023Q4,FY2024Q1"
        
        # 估算和修正数据
        'filled_simple_estimates': 'Nullable(Float64)',  # 简单估算值（滚动平滑）
        'corrected_filled_simple_estimates': 'Nullable(Float64)',  # 修正后的估算值
        
        # 时间信息
        'original_effective_date': 'Nullable(Date)',  # 原始有效日期
        'estimated_effective_date': 'Nullable(Date)',  # 估算数据的有效日期
        'corrected_effective_date': 'Nullable(Date)',  # 修正数据的有效日期
        
        # 元数据
        'created_at': 'DateTime',  # 记录创建时间
        'updated_at': 'DateTime',  # 记录更新时间
        'confidence_score': 'Nullable(Float64)',  # 置信度评分 (0-1)
        'notes': 'Nullable(String)'  # 备注信息
    }
    
    return table_structure

# 创建模拟数据示例
def create_sample_data():
    """
    创建模拟的数据示例
    """
    
    sample_data = [
        # 案例1：CCEP缺失2024Q1数据，用滚动平滑估算
        {
            'id': 'CCEP|FY2024Q1|Total Revenue',
            'stock_symbol': 'CCEP',
            'financial_period_absolute': 'FY2024Q1',
            'statement_type': 'income_statement',
            'item_name': 'Total Revenue',
            'original_value': None,  # 缺失
            'data_status': 'estimated',
            'fill_method': 'rolling_average',
            'source_periods': 'FY2023Q2,FY2023Q3,FY2023Q4',  # 用前3个季度
            'filled_simple_estimates': 3250.5,  # 滚动平均结果
            'corrected_filled_simple_estimates': None,  # 尚未修正
            'original_effective_date': None,
            'estimated_effective_date': '2024-02-15',  # 基于上次季报发布日期
            'corrected_effective_date': None,
            'created_at': '2024-01-15 10:00:00',
            'updated_at': '2024-01-15 10:00:00',
            'confidence_score': 0.7,
            'notes': 'Using 3-quarter rolling average due to missing Q1 data'
        },
        
        # 案例2：CCEP获得半年报后修正Q1估算
        {
            'id': 'CCEP|FY2024Q1|Total Revenue',
            'stock_symbol': 'CCEP',
            'financial_period_absolute': 'FY2024Q1',
            'statement_type': 'income_statement',
            'item_name': 'Total Revenue',
            'original_value': None,
            'data_status': 'corrected',
            'fill_method': 'half_year_split',
            'source_periods': 'FY2024H1,FY2023Q1',  # 半年报拆分
            'filled_simple_estimates': 3250.5,
            'corrected_filled_simple_estimates': 3180.2,  # 基于半年报修正
            'original_effective_date': None,
            'estimated_effective_date': '2024-02-15',
            'corrected_effective_date': '2024-07-20',  # 半年报发布日期
            'created_at': '2024-01-15 10:00:00',
            'updated_at': '2024-07-20 15:30:00',
            'confidence_score': 0.9,
            'notes': 'Corrected using H1 data split by historical Q1/Q2 ratio'
        },
        
        # 案例3：POST缺失2024Q2，用年报拆分修正
        {
            'id': 'POST|FY2024Q2|Net Income',
            'stock_symbol': 'POST',
            'financial_period_absolute': 'FY2024Q2',
            'statement_type': 'income_statement',
            'item_name': 'Net Income',
            'original_value': None,
            'data_status': 'corrected',
            'fill_method': 'annual_split',
            'source_periods': 'FY2024,FY2023Q2,FY2022Q2',  # 年报拆分
            'filled_simple_estimates': 125.8,  # 原始估算
            'corrected_filled_simple_estimates': 142.3,  # 年报拆分修正
            'original_effective_date': None,
            'estimated_effective_date': '2024-05-15',
            'corrected_effective_date': '2025-02-28',  # 年报发布日期
            'created_at': '2024-04-10 09:00:00',
            'updated_at': '2025-02-28 11:45:00',
            'confidence_score': 0.85,
            'notes': 'Corrected using annual data split by historical Q2 patterns'
        },
        
        # 案例4：原始数据存在的情况
        {
            'id': 'AAPL|FY2024Q1|Total Revenue',
            'stock_symbol': 'AAPL',
            'financial_period_absolute': 'FY2024Q1',
            'statement_type': 'income_statement',
            'item_name': 'Total Revenue',
            'original_value': 119575.0,  # 原始数据存在
            'data_status': 'original',
            'fill_method': None,
            'source_periods': None,
            'filled_simple_estimates': None,
            'corrected_filled_simple_estimates': None,
            'original_effective_date': '2024-02-01',
            'estimated_effective_date': None,
            'corrected_effective_date': None,
            'created_at': '2024-02-01 16:00:00',
            'updated_at': '2024-02-01 16:00:00',
            'confidence_score': 1.0,
            'notes': 'Original quarterly data available'
        }
    ]
    
    return sample_data

# 显示表结构
print("\n1. 表结构设计 (ClickHouse DDL):")
print("=" * 60)

table_structure = create_quarterly_filling_table_structure()

create_table_sql = """
CREATE TABLE quarterly_data_filled (
    id String,
    stock_symbol String,
    financial_period_absolute String,
    statement_type String,
    item_name String,
    original_value Nullable(Float64),
    
    data_status String,
    fill_method Nullable(String),
    source_periods Nullable(String),
    
    filled_simple_estimates Nullable(Float64),
    corrected_filled_simple_estimates Nullable(Float64),
    
    original_effective_date Nullable(Date),
    estimated_effective_date Nullable(Date),
    corrected_effective_date Nullable(Date),
    
    created_at DateTime,
    updated_at DateTime,
    confidence_score Nullable(Float64),
    notes Nullable(String)
)
ENGINE = MergeTree()
ORDER BY (stock_symbol, financial_period_absolute, statement_type, item_name)
PARTITION BY toYYYYMM(estimated_effective_date);
"""

print(create_table_sql)

# 显示字段说明
print("\n2. 字段说明:")
print("=" * 60)

field_descriptions = {
    'id': '主键，格式：{stock_symbol}|{period}|{item_name}',
    'stock_symbol': '股票代码',
    'financial_period_absolute': '财务期间 (如FY2024Q1)',
    'statement_type': '报表类型 (income_statement/balance_sheet/cash_flow)',
    'item_name': '会计科目名称',
    'original_value': '原始值 (如果存在)',
    'data_status': '数据状态 (original/missing/estimated/corrected)',
    'fill_method': '填补方法 (rolling_average/half_year_split/annual_split)',
    'source_periods': '计算源期间 (逗号分隔)',
    'filled_simple_estimates': '简单估算值 (滚动平滑)',
    'corrected_filled_simple_estimates': '修正后估算值',
    'original_effective_date': '原始数据有效日期',
    'estimated_effective_date': '估算数据有效日期',
    'corrected_effective_date': '修正数据有效日期',
    'created_at': '记录创建时间',
    'updated_at': '记录更新时间',
    'confidence_score': '置信度评分 (0-1)',
    'notes': '备注信息'
}

for field, description in field_descriptions.items():
    print(f"  {field:30s}: {description}")

# 显示示例数据
print("\n3. 示例数据:")
print("=" * 60)

sample_data = create_sample_data()
df_sample = pd.DataFrame(sample_data)

# 显示关键字段
key_columns = [
    'stock_symbol', 'financial_period_absolute', 'item_name', 
    'data_status', 'fill_method', 'filled_simple_estimates', 
    'corrected_filled_simple_estimates', 'confidence_score'
]

print(df_sample[key_columns].to_string(index=False))

# 显示工作流程
print("\n4. 数据填补工作流程:")
print("=" * 60)

workflow_steps = [
    "步骤1: 识别缺失的季报数据",
    "步骤2: 使用滚动平滑法进行初始估算",
    "步骤3: 记录估算数据到 filled_simple_estimates",
    "步骤4: 设置估算数据的 effective_date",
    "步骤5: 等待半年报或年报数据发布",
    "步骤6: 使用半年报/年报数据修正估算",
    "步骤7: 记录修正数据到 corrected_filled_simple_estimates",
    "步骤8: 更新修正数据的 effective_date",
    "步骤9: 提高置信度评分",
    "步骤10: 记录修正过程到 notes"
]

for i, step in enumerate(workflow_steps, 1):
    print(f"  {step}")

print("\n5. 滚动平滑计算示例:")
print("=" * 60)

rolling_example = """
假设CCEP缺失FY2024Q1的Total Revenue数据：

历史数据：
- FY2023Q2: 3100.0
- FY2023Q3: 3200.0  
- FY2023Q4: 3450.0

滚动平滑计算：
FY2024Q1预估值 = (3100.0 + 3200.0 + 3450.0) ÷ 3 = 3250.0

后续修正（假设获得FY2024H1 = 6500.0）：
如果历史上Q1/H1比例约为0.49，则：
FY2024Q1修正值 = 6500.0 × 0.49 = 3185.0
"""

print(rolling_example)

print("\n=== 表结构设计完成 ===")

