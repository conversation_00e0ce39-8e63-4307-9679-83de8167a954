from clickhouse_driver import Client
import random

# 连接到lseg数据库
client_lseg = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='lseg'
)

# 连接到ap_research数据库
client_ap = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 全面的数据一致性检查 ===")

# 1. 统计ap_research和lseg的总体数据量
print("\n1. 总体数据量对比...")

# ap_research统计
query_ap_stats = """
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT instrument) as unique_instruments,
    COUNT(DISTINCT financial_period_absolute) as unique_periods,
    COUNT(DISTINCT CONCAT(instrument, '|', financial_period_absolute)) as unique_combinations
FROM priority_quality_fundamental_data_complete_deduped
WHERE period_end_date IS NOT NULL AND period_end_date != ''
"""

result_ap_stats = client_ap.execute(query_ap_stats)
ap_records, ap_instruments, ap_periods, ap_combinations = result_ap_stats[0]

print(f"  ap_research统计:")
print(f"    总记录数: {ap_records:,}")
print(f"    唯一instrument数: {ap_instruments:,}")
print(f"    唯一period数: {ap_periods:,}")
print(f"    唯一(instrument,period)组合数: {ap_combinations:,}")

# lseg统计（合并三个表）
print(f"\n  lseg原始数据统计:")
lseg_total_records = 0
lseg_instruments = set()
lseg_periods = set()
lseg_combinations = set()

tables = ['income_statement', 'balance_sheet_history', 'cash_flow']

for table in tables:
    print(f"    正在统计{table}...")
    query_lseg_stats = f"""
    SELECT 
        COUNT(*) as total_records,
        COUNT(DISTINCT instrument) as unique_instruments,
        COUNT(DISTINCT financial_period_absolute) as unique_periods
    FROM {table}
    WHERE financial_period_absolute != ''
    """
    
    try:
        result_lseg_stats = client_lseg.execute(query_lseg_stats)
        table_records, table_instruments, table_periods = result_lseg_stats[0]
        
        print(f"      {table}: {table_records:,}条记录, {table_instruments:,}个instrument, {table_periods:,}个period")
        lseg_total_records += table_records
        
        # 获取详细的instrument和period列表
        query_details = f"""
        SELECT DISTINCT instrument, financial_period_absolute
        FROM {table}
        WHERE financial_period_absolute != ''
        """
        
        result_details = client_lseg.execute(query_details)
        for instrument, period in result_details:
            lseg_instruments.add(instrument)
            lseg_periods.add(period)
            lseg_combinations.add(f"{instrument}|{period}")
            
    except Exception as e:
        print(f"      查询{table}时出错: {e}")

print(f"\n  lseg合计统计:")
print(f"    总记录数: {lseg_total_records:,}")
print(f"    唯一instrument数: {len(lseg_instruments):,}")
print(f"    唯一period数: {len(lseg_periods):,}")
print(f"    唯一(instrument,period)组合数: {len(lseg_combinations):,}")

# 2. 随机抽样大规模检查
print("\n2. 随机抽样大规模检查...")

# 获取ap_research中的所有组合
query_all_ap_combinations = """
SELECT DISTINCT 
    instrument,
    financial_period_absolute
FROM priority_quality_fundamental_data_complete_deduped
WHERE period_end_date IS NOT NULL AND period_end_date != ''
"""

result_all_ap = client_ap.execute(query_all_ap_combinations)
all_ap_combinations = [(instrument, period) for instrument, period in result_all_ap]

print(f"  ap_research中共有 {len(all_ap_combinations):,} 个唯一组合")

# 随机抽样500个进行检查
sample_size = min(500, len(all_ap_combinations))
sample_combinations = random.sample(all_ap_combinations, sample_size)

print(f"  随机抽样 {sample_size} 个组合进行检查...")

not_found_in_lseg = []
batch_size = 50

for i in range(0, len(sample_combinations), batch_size):
    batch = sample_combinations[i:i+batch_size]
    print(f"    检查进度: {i+1}-{min(i+batch_size, len(sample_combinations))}/{len(sample_combinations)}")
    
    for instrument, period in batch:
        found = False
        
        # 构建一个查询字符串，用IN子句一次性检查
        for table in tables:
            query_check = f"""
            SELECT COUNT(*) as count
            FROM {table}
            WHERE instrument = '{instrument}' AND financial_period_absolute = '{period}'
            """
            
            try:
                result_check = client_lseg.execute(query_check)
                if result_check[0][0] > 0:
                    found = True
                    break
            except Exception as e:
                continue
        
        if not found:
            not_found_in_lseg.append((instrument, period))

print(f"\n  抽样检查结果:")
print(f"    检查了 {sample_size} 个组合")
print(f"    在lseg中未找到: {len(not_found_in_lseg)} 个")

if not_found_in_lseg:
    print(f"    未找到的组合（前20个）:")
    for instrument, period in not_found_in_lseg[:20]:
        print(f"      {instrument} - {period}")
    if len(not_found_in_lseg) > 20:
        print(f"      ... 还有 {len(not_found_in_lseg) - 20} 个")

# 3. 检查特定的问题模式
print("\n3. 检查特定的问题模式...")

# 检查是否有空的period
query_empty_periods = """
SELECT COUNT(*) as count
FROM priority_quality_fundamental_data_complete_deduped
WHERE financial_period_absolute = '' OR financial_period_absolute IS NULL
"""

result_empty = client_ap.execute(query_empty_periods)
empty_count = result_empty[0][0]
print(f"  ap_research中空period的记录数: {empty_count}")

# 检查是否有特殊字符的period
query_special_periods = """
SELECT DISTINCT financial_period_absolute
FROM priority_quality_fundamental_data_complete_deduped
WHERE financial_period_absolute LIKE '%-%' 
   OR financial_period_absolute LIKE '%/%'
   OR financial_period_absolute LIKE '%\\%'
   OR financial_period_absolute LIKE '% %'
ORDER BY financial_period_absolute
LIMIT 20
"""

result_special = client_ap.execute(query_special_periods)
if result_special:
    print(f"  包含特殊字符的period格式:")
    for (period,) in result_special:
        print(f"    {period}")

# 4. 数据一致性总结
print(f"\n4. 数据一致性总结:")
consistency_rate = (sample_size - len(not_found_in_lseg)) / sample_size * 100
print(f"  随机抽样一致性: {consistency_rate:.2f}%")

if consistency_rate >= 99:
    print(f"  ✅ 数据一致性极高，ap_research数据质量优秀")
elif consistency_rate >= 95:
    print(f"  ✅ 数据一致性良好")
elif consistency_rate >= 90:
    print(f"  ⚠️ 数据一致性中等，需要进一步调查")
else:
    print(f"  ❌ 数据一致性较差，存在重大问题")

print(f"\n=== 检查完成 ===")

