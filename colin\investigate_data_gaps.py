from clickhouse_driver import Client

# 连接到lseg数据库
client_lseg = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='lseg'
)

# 连接到ap_research数据库
client_ap = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 调查数据断层的原因 ===")

# 需要调查的股票
problem_stocks = {
    'CCEP': 'CCEP.NB',
    'POST': 'POST.NB',
    'MRP': 'MRP.NB',
    'VG': 'VG.NB',
    'SIG': 'SIG.NB',
    'AU': 'AU.NB'
}

tables = ['income_statement', 'balance_sheet_history', 'cash_flow']

for stock_symbol, instrument in problem_stocks.items():
    print(f"\n{'='*60}")
    print(f"调查股票: {stock_symbol} (instrument: {instrument})")
    print(f"{'='*60}")
    
    # 1. 检查该股票在lseg中的所有相关instrument变体
    print(f"\n1. 查找{stock_symbol}的所有可能instrument变体...")
    
    all_variants = set()
    for table in tables:
        query_variants = f"""
        SELECT DISTINCT instrument
        FROM {table}
        WHERE instrument LIKE '%{stock_symbol}%'
        ORDER BY instrument
        """
        
        try:
            result_variants = client_lseg.execute(query_variants)
            table_variants = [variant for (variant,) in result_variants]
            all_variants.update(table_variants)
            
            if table_variants:
                print(f"  在{table}中找到的变体:")
                for variant in table_variants:
                    print(f"    {variant}")
        except Exception as e:
            print(f"  查询{table}时出错: {e}")
    
    print(f"  总共找到 {len(all_variants)} 个instrument变体")
    
    # 2. 检查每个变体的时间范围
    print(f"\n2. 检查各变体的时间范围...")
    
    for variant in sorted(all_variants):
        print(f"\n  检查variant: {variant}")
        variant_data = {}
        
        for table in tables:
            query_timerange = f"""
            SELECT 
                MIN(financial_period_absolute) as min_period,
                MAX(financial_period_absolute) as max_period,
                COUNT(DISTINCT financial_period_absolute) as period_count,
                COUNT(*) as total_records
            FROM {table}
            WHERE instrument = '{variant}'
              AND financial_period_absolute != ''
            """
            
            try:
                result_timerange = client_lseg.execute(query_timerange)
                if result_timerange and result_timerange[0][3] > 0:  # 有记录
                    min_period, max_period, period_count, total_records = result_timerange[0]
                    variant_data[table] = {
                        'min_period': min_period,
                        'max_period': max_period,
                        'period_count': period_count,
                        'total_records': total_records
                    }
                    print(f"    {table}: {min_period} 到 {max_period} ({period_count}个期间, {total_records}条记录)")
            except Exception as e:
                print(f"    查询{table}时出错: {e}")
        
        if not variant_data:
            print(f"    该variant没有数据")
    
    # 3. 检查是否有公司名称或代码变更
    print(f"\n3. 检查可能的公司变更...")
    
    # 查找包含相似名称的其他instrument
    similar_patterns = []
    
    if stock_symbol == 'CCEP':
        similar_patterns = ['COKE', 'CCE', 'KO', 'COCA']
    elif stock_symbol == 'POST':
        similar_patterns = ['POST', 'POSTHOLD']
    elif stock_symbol == 'MRP':
        similar_patterns = ['MURPHY', 'MUR']
    elif stock_symbol == 'VG':
        similar_patterns = ['VIRGIN', 'SPCE', 'VG']
    elif stock_symbol == 'SIG':
        similar_patterns = ['SIGNET', 'SIG']
    elif stock_symbol == 'AU':
        similar_patterns = ['ANGLO', 'GOLD', 'AU']
    
    for pattern in similar_patterns:
        print(f"\n  查找包含'{pattern}'的instrument...")
        
        for table in tables:
            query_similar = f"""
            SELECT DISTINCT instrument, COUNT(*) as records
            FROM {table}
            WHERE instrument LIKE '%{pattern}%'
              AND financial_period_absolute != ''
            GROUP BY instrument
            ORDER BY records DESC
            LIMIT 10
            """
            
            try:
                result_similar = client_lseg.execute(query_similar)
                if result_similar:
                    print(f"    在{table}中找到:")
                    for instr, records in result_similar:
                        print(f"      {instr}: {records}条记录")
            except Exception as e:
                continue
    
    # 4. 检查ap_research中的数据来源
    print(f"\n4. 检查ap_research中{stock_symbol}的数据情况...")
    
    query_ap_info = f"""
    SELECT 
        instrument,
        MIN(financial_period_absolute) as min_period,
        MAX(financial_period_absolute) as max_period,
        COUNT(DISTINCT financial_period_absolute) as period_count,
        COUNT(*) as total_records
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = '{stock_symbol}'
    GROUP BY instrument
    """
    
    try:
        result_ap_info = client_ap.execute(query_ap_info)
        if result_ap_info:
            for instrument_ap, min_p, max_p, period_c, total_r in result_ap_info:
                print(f"  在ap_research中:")
                print(f"    instrument: {instrument_ap}")
                print(f"    时间范围: {min_p} 到 {max_p}")
                print(f"    期间数: {period_c}, 总记录数: {total_r}")
        else:
            print(f"  在ap_research中未找到{stock_symbol}")
    except Exception as e:
        print(f"  查询ap_research时出错: {e}")
    
    # 5. 特别检查是否有数据在2017年后突然出现在其他instrument下
    print(f"\n5. 检查2017年后是否有相关数据...")
    
    for table in tables:
        query_recent = f"""
        SELECT DISTINCT 
            instrument,
            financial_period_absolute,
            COUNT(*) as records
        FROM {table}
        WHERE instrument LIKE '%{stock_symbol}%'
          AND financial_period_absolute >= 'FY2017'
        GROUP BY instrument, financial_period_absolute
        ORDER BY instrument, financial_period_absolute
        LIMIT 20
        """
        
        try:
            result_recent = client_lseg.execute(query_recent)
            if result_recent:
                print(f"  在{table}中2017年后的数据:")
                for instr, period, records in result_recent:
                    print(f"    {instr} - {period}: {records}条记录")
            else:
                print(f"  在{table}中2017年后没有相关数据")
        except Exception as e:
            continue

print(f"\n=== 调查完成 ===")

# 总结可能的原因
print(f"\n=== 可能的原因总结 ===")
print(f"1. 公司重组/合并: 公司可能被收购、合并或分拆，导致股票代码变更")
print(f"2. 交易所变更: 公司可能从一个交易所转移到另一个交易所")
print(f"3. 数据供应商覆盖变化: LSEG可能停止或改变了对某些公司的数据覆盖")
print(f"4. 股票代码重新分配: 原股票代码可能被分配给了其他公司")
print(f"5. 私有化: 公司可能被私有化，不再公开交易")
print(f"6. 破产/退市: 公司可能面临财务困难或被迫退市")

