from clickhouse_driver import Client

# 连接ClickHouse数据库
client = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 检查季报数据格式 ===")

# 1. 查看所有唯一的financial_period_absolute格式
print("\n1. 查看financial_period_absolute的格式样本...")
query_period_formats = """
SELECT DISTINCT financial_period_absolute
FROM priority_quality_fundamental_data_complete_deduped
WHERE period_end_date IS NOT NULL AND period_end_date != ''
ORDER BY financial_period_absolute
LIMIT 50
"""

result_formats = client.execute(query_period_formats)
print("  前50个唯一的financial_period_absolute格式:")
for i, (period,) in enumerate(result_formats):
    print(f"    {i+1:2d}. {period}")

# 2. 专门查看包含Q的格式
print("\n2. 包含'Q'的期间格式...")
query_q_formats = """
SELECT DISTINCT financial_period_absolute, COUNT(*) as count
FROM priority_quality_fundamental_data_complete_deduped
WHERE period_end_date IS NOT NULL AND period_end_date != ''
  AND financial_period_absolute LIKE '%Q%'
GROUP BY financial_period_absolute
ORDER BY count DESC
LIMIT 20
"""

result_q_formats = client.execute(query_q_formats)
if result_q_formats:
    print("  包含'Q'的期间格式（按数量排序）:")
    for period, count in result_q_formats:
        print(f"    {period} - {count:,}条记录")
else:
    print("  没有找到包含'Q'的期间格式")

# 3. 查看年报格式
print("\n3. 年报格式...")
query_fy_formats = """
SELECT DISTINCT financial_period_absolute, COUNT(*) as count
FROM priority_quality_fundamental_data_complete_deduped
WHERE period_end_date IS NOT NULL AND period_end_date != ''
  AND financial_period_absolute LIKE '%FY%'
GROUP BY financial_period_absolute
ORDER BY count DESC
LIMIT 20
"""

result_fy_formats = client.execute(query_fy_formats)
if result_fy_formats:
    print("  年报格式（按数量排序）:")
    for period, count in result_fy_formats:
        print(f"    {period} - {count:,}条记录")

print("\n=== 检查完成 ===")

