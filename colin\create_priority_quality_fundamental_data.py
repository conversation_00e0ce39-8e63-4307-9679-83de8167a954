from clickhouse_driver import Client
from datetime import datetime

def create_priority_quality_fundamental_data():
    """
    创建优质资产财报数据表，包含821个股票从2017年1月1日之后的所有财报数据
    使用stock_symbol匹配instrument（忽略后缀）
    """
    print("="*80)
    print("创建priority_quality_fundamental_data表 - 优质资产财报数据")
    print("="*80)
    
    try:
        # 连接到两个数据库
        lseg_client = Client(
            host='************',
            port=9000,
            user='default',
            password='5ur2pK8WZQdy2',
            database='lseg'
        )
        
        ap_client = Client(
            host='************',
            port=9000,
            user='default',
            password='5ur2pK8WZQdy2',
            database='ap_research'
        )
        
        print("✅ 成功连接到两个数据库!")
        
        # 1. 获取821个优质资产列表
        print(f"\n📋 获取优质资产列表...")
        
        quality_assets = ap_client.execute("""
            SELECT DISTINCT stock_symbol, turnover_type, original_stock_code, 
                   cumulative_return, annualized_return
            FROM priority_quality_stock_hfq
            ORDER BY cumulative_return DESC
        """)
        
        print(f"✅ 获取到 {len(quality_assets)} 个优质资产")
        
        # 创建股票代码集合
        stock_symbols = set(asset[0] for asset in quality_assets)
        asset_info_map = {asset[0]: asset for asset in quality_assets}  # symbol -> asset_info
        
        # 2. 检查财报表结构
        print(f"\n🔍 检查财报表结构...")
        
        financial_tables = ['income_statement', 'balance_sheet_history', 'cash_flow']
        table_structures = {}
        
        for table in financial_tables:
            try:
                structure = lseg_client.execute(f"DESCRIBE TABLE {table}")
                table_structures[table] = structure
                print(f"✅ {table}: {len(structure)} 列")
            except Exception as e:
                print(f"❌ 获取 {table} 结构失败: {e}")
        
        # 3. 创建新表
        table_name = 'priority_quality_fundamental_data'
        print(f"\n🏗️ 创建表: {table_name}")
        
        # 删除表如果存在
        try:
            ap_client.execute(f"DROP TABLE IF EXISTS {table_name}")
            print(f"✅ 清理现有表")
        except Exception as e:
            print(f"⚠️ 清理表时警告: {e}")
        
        # 创建表结构
        create_table_sql = f"""
        CREATE TABLE {table_name} (
            -- 股票基本信息
            stock_symbol String,
            original_stock_code String,
            turnover_type String,
            instrument String,
            
            -- 财报基本信息
            statement_type String,  -- income_statement, balance_sheet, cash_flow
            financial_period_absolute String,
            source String,
            item_name String,
            currency String,
            
            -- 财报数值
            value Float64,
            
            -- 股票表现信息
            cumulative_return Float64,
            annualized_return Float64,
            
            -- 元数据
            original_created_at DateTime,
            create_date DateTime DEFAULT now(),
            
            -- 索引
            INDEX idx_symbol stock_symbol TYPE bloom_filter GRANULARITY 1,
            INDEX idx_period financial_period_absolute TYPE bloom_filter GRANULARITY 1,
            INDEX idx_item item_name TYPE bloom_filter GRANULARITY 1
        ) ENGINE = MergeTree()
        PARTITION BY statement_type
        ORDER BY (stock_symbol, statement_type, financial_period_absolute, item_name)
        """
        
        ap_client.execute(create_table_sql)
        print(f"✅ 表创建成功!")
        
        # 4. 准备数据插入
        print(f"\n📤 开始财报数据提取和插入...")
        
        insert_sql = f"""
        INSERT INTO {table_name} (
            stock_symbol, original_stock_code, turnover_type, instrument,
            statement_type, financial_period_absolute, source, item_name, currency,
            value, cumulative_return, annualized_return, original_created_at
        ) VALUES
        """
        
        total_records = 0
        batch_size = 1000
        
        # 处理每个财报表
        for table in financial_tables:
            if table not in table_structures:
                continue
                
            print(f"\n🔧 处理 {table} 表...")
            
            # 确定数值列名
            if table == 'income_statement':
                value_column = 'income_statement'
            elif table == 'balance_sheet_history':
                value_column = 'balance_sheet'
            elif table == 'cash_flow':
                value_column = 'cash_flow_statement'
            
            # 提取数据
            print(f"  📊 查询 {table} 数据（2017年1月1日之后）...")
            
            try:
                # 分批获取数据以避免内存问题
                offset = 0
                batch_query_size = 10000
                
                while True:
                    financial_data = lseg_client.execute(f"""
                        SELECT instrument, financial_period_absolute, source, item_name, 
                               currency, {value_column}, created_at,
                               splitByChar('.', instrument)[1] as pure_symbol
                        FROM {table}
                        WHERE financial_period_absolute >= '2017-01-01'
                        AND {value_column} IS NOT NULL
                        AND splitByChar('.', instrument)[1] IN ({','.join([f"'{s}'" for s in stock_symbols])})
                        ORDER BY instrument, financial_period_absolute
                        LIMIT {batch_query_size} OFFSET {offset}
                    """)
                    
                    if not financial_data:
                        break
                    
                    print(f"    📥 获取 {len(financial_data)} 条 {table} 记录")
                    
                    # 准备批量插入数据
                    batch_data = []
                    for instrument, period, source, item_name, currency, value, created_at, pure_symbol in financial_data:
                        if pure_symbol in asset_info_map:
                            asset_info = asset_info_map[pure_symbol]
                            stock_symbol = asset_info[0]
                            original_stock_code = asset_info[2]
                            turnover_type = asset_info[1]
                            cumulative_return = asset_info[3]
                            annualized_return = asset_info[4]
                            
                            record = (
                                stock_symbol, original_stock_code, turnover_type, instrument,
                                table, period, source or '', item_name or '', currency or '',
                                float(value) if value is not None else 0.0,
                                cumulative_return, annualized_return, created_at
                            )
                            batch_data.append(record)
                        
                        # 批量插入
                        if len(batch_data) >= batch_size:
                            ap_client.execute(insert_sql, batch_data)
                            total_records += len(batch_data)
                            batch_data = []
                    
                    # 插入剩余数据
                    if batch_data:
                        ap_client.execute(insert_sql, batch_data)
                        total_records += len(batch_data)
                    
                    print(f"    ✅ {table} 已插入 {total_records:,} 条记录")
                    
                    # 如果返回的记录数少于批次大小，说明已经处理完了
                    if len(financial_data) < batch_query_size:
                        break
                    
                    offset += batch_query_size
                    
            except Exception as e:
                print(f"    ❌ 处理 {table} 时出错: {e}")
                continue
        
        print(f"\n🎉 财报数据插入完成!")
        print(f"📊 总计插入 {total_records:,} 条财报记录")
        
        # 5. 验证插入结果
        print(f"\n🔍 验证插入结果...")
        
        # 总记录数
        count_result = ap_client.execute(f"SELECT COUNT(*) FROM {table_name}")
        inserted_count = count_result[0][0]
        print(f"📊 表中总记录数: {inserted_count:,}")
        
        # 资产数量
        asset_count = ap_client.execute(f"SELECT COUNT(DISTINCT stock_symbol) FROM {table_name}")
        asset_num = asset_count[0][0]
        print(f"📈 有财报数据的资产数: {asset_num}")
        
        # 时间范围
        time_range = ap_client.execute(f"""
            SELECT MIN(financial_period_absolute) as start_date, 
                   MAX(financial_period_absolute) as end_date
            FROM {table_name}
        """)
        start_date, end_date = time_range[0]
        print(f"📅 财报数据时间范围: {start_date} 到 {end_date}")
        
        # 按报表类型统计
        statement_stats = ap_client.execute(f"""
            SELECT statement_type, 
                   COUNT(DISTINCT stock_symbol) as assets,
                   COUNT(*) as records,
                   COUNT(DISTINCT financial_period_absolute) as periods
            FROM {table_name}
            GROUP BY statement_type
            ORDER BY records DESC
        """)
        
        print(f"\n📈 按报表类型统计:")
        print(f"{'报表类型':<20} {'资产数':<8} {'记录数':<12} {'报告期数':<10}")
        print("-" * 55)
        
        for stmt_type, assets, records, periods in statement_stats:
            print(f"{stmt_type:<20} {assets:<8} {records:<12} {periods:<10}")
        
        # 按资产类型统计
        type_stats = ap_client.execute(f"""
            SELECT turnover_type, 
                   COUNT(DISTINCT stock_symbol) as assets,
                   COUNT(*) as records,
                   AVG(cumulative_return) as avg_return
            FROM {table_name}
            GROUP BY turnover_type
            ORDER BY assets DESC
        """)
        
        print(f"\n📊 按资产类型统计:")
        print(f"{'类型':<10} {'资产数':<8} {'记录数':<12} {'平均收益(%)':<15}")
        print("-" * 50)
        
        for t_type, assets, records, avg_return in type_stats:
            print(f"{t_type:<10} {assets:<8} {records:<12} {avg_return:<15.2f}")
        
        # 显示表现最佳且有财报数据的资产
        top_fundamental = ap_client.execute(f"""
            SELECT stock_symbol, original_stock_code, turnover_type,
                   cumulative_return, COUNT(*) as fundamental_records,
                   COUNT(DISTINCT financial_period_absolute) as periods,
                   COUNT(DISTINCT statement_type) as statement_types
            FROM {table_name}
            GROUP BY stock_symbol, original_stock_code, turnover_type, cumulative_return
            ORDER BY cumulative_return DESC
            LIMIT 10
        """)
        
        print(f"\n🏆 表现最佳且有财报数据的10个资产:")
        print(f"{'股票代码':<8} {'原始代码':<12} {'类型':<8} {'收益率(%)':<12} {'财报记录数':<12} {'报告期数':<10} {'报表类型数'}")
        print("-" * 85)
        
        for symbol, orig_code, t_type, cum_ret, records, periods, stmt_types in top_fundamental:
            print(f"{symbol:<8} {orig_code:<12} {t_type:<8} {cum_ret:<12.2f} {records:<12} {periods:<10} {stmt_types}")
        
        # 检查数据完整性 - 按年份统计
        yearly_stats = ap_client.execute(f"""
            SELECT toYear(toDateOrNull(financial_period_absolute)) as year,
                   COUNT(DISTINCT stock_symbol) as assets,
                   COUNT(*) as records
            FROM {table_name}
            WHERE financial_period_absolute != ''
            GROUP BY year
            ORDER BY year DESC
        """)
        
        print(f"\n📅 按年份统计财报数据:")
        print(f"{'年份':<6} {'资产数':<8} {'记录数':<12}")
        print("-" * 30)
        
        for year, assets, records in yearly_stats:
            if year and year >= 2017:
                print(f"{year:<6} {assets:<8} {records:<12}")
        
        # 样例数据展示
        sample_data = ap_client.execute(f"""
            SELECT stock_symbol, original_stock_code, statement_type,
                   financial_period_absolute, item_name, value, currency
            FROM {table_name}
            WHERE item_name LIKE '%Revenue%'
            ORDER BY cumulative_return DESC, financial_period_absolute DESC
            LIMIT 10
        """)
        
        print(f"\n📋 财报数据样例（收入相关）:")
        print(f"{'股票代码':<8} {'原始代码':<12} {'报表类型':<15} {'财报期间':<12} {'科目':<20} {'数值':<15} {'货币'}")
        print("-" * 100)
        
        for symbol, orig_code, stmt_type, period, item, value, currency in sample_data:
            print(f"{symbol:<8} {orig_code:<12} {stmt_type:<15} {period:<12} {item:<20} {value:<15.2f} {currency}")
        
        # 6. 保存创建日志
        print(f"\n💾 保存创建日志...")
        
        with open('priority_quality_fundamental_data_creation_log.txt', 'w', encoding='utf-8') as f:
            f.write("priority_quality_fundamental_data表创建日志\n")
            f.write("="*60 + "\n\n")
            f.write(f"创建时间: {datetime.now()}\n")
            f.write(f"表名: {table_name}\n")
            f.write(f"数据库: ap_research\n\n")
            
            f.write(f"数据统计:\n")
            f.write(f"- 总记录数: {inserted_count:,}\n")
            f.write(f"- 有财报数据的资产数: {asset_num}\n")
            f.write(f"- 财报数据时间范围: {start_date} 到 {end_date}\n")
            f.write(f"- 数据筛选条件: >= 2017-01-01\n\n")
            
            f.write(f"按报表类型统计:\n")
            for stmt_type, assets, records, periods in statement_stats:
                f.write(f"- {stmt_type}: {assets}个资产, {records}条记录, {periods}个报告期\n")
            
            f.write(f"\n按资产类型统计:\n")
            for t_type, assets, records, avg_return in type_stats:
                f.write(f"- {t_type}: {assets}个资产, {records}条记录, 平均收益{avg_return:.2f}%\n")
            
            f.write(f"\n表结构特点:\n")
            f.write(f"- 按报表类型和年份分区存储\n")
            f.write(f"- 主键：(stock_symbol, statement_type, financial_period_absolute, item_name)\n")
            f.write(f"- 包含布隆过滤器索引提升查询性能\n")
            f.write(f"- 匹配规则: stock_symbol = splitByChar('.', instrument)[1]\n")
        
        print(f"✅ 创建日志已保存: priority_quality_fundamental_data_creation_log.txt")
        
        print(f"\n" + "="*80)
        print(f"✅ priority_quality_fundamental_data表创建完成!")
        print(f"📊 表中包含 {asset_num} 个优质资产的 {inserted_count:,} 条财报记录")
        print(f"🎯 涵盖2017年1月1日至今的完整财报数据")
        print(f"📅 数据覆盖 {start_date} 到 {end_date}")
        print("="*80)
        
    except Exception as e:
        print(f"❌ 创建过程中发生错误: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    create_priority_quality_fundamental_data()