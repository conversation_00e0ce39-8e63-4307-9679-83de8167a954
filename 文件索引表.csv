文件名,文件大小,重要程度,用途说明,使用场景
stock_performance_2020_2025_cumulative.csv,140KB,★★★★★,979只高质量股票筛选结果,作为基础股票池
complete_stocks_original_codes.csv,5KB,★★★★★,817只完整股票代码列表,标准股票池
stocks_with_complete_financial_statements.csv,29KB,★★★★★,完整股票详细分析,了解数据覆盖情况
filtered_stocks_income_statement_matching.csv,33MB,★★★★★,利润表匹配数据,利润表财务分析
filtered_stocks_balance_sheet_matching.csv,12MB,★★★★★,资产负债表匹配数据,资产负债表财务分析
filtered_stocks_cash_flow_matching.csv,42MB,★★★★★,现金流量表匹配数据,现金流量表财务分析
match_filtered_stocks_with_financial_periods_final_correct.py,14KB,★★★★★,最终匹配脚本,重新生成匹配数据
save_complete_stocks.py,6KB,★★★★★,完整股票保存脚本,更新完整股票列表
financial_statements_coverage_analysis.csv,29KB,★★★★,覆盖率分析,了解数据完整性
stocks_without_financial_statements.csv,3.5KB,★★★★,缺失股票分析,了解数据缺失情况
stock_code_mapping.csv,6KB,★★★★,股票代码映射,处理代码格式差异
all_financial_items.csv,60KB,★★★,财务项目清单,了解可用财务指标
income_statement_items.csv,25KB,★★★,利润表项目清单,利润表指标参考
balance_sheet_items.csv,17KB,★★★,资产负债表项目清单,资产负债表指标参考
cash_flow_items.csv,18KB,★★★,现金流量表项目清单,现金流量表指标参考
stock_performance_2020.csv,133KB,★★★,2020年表现数据,年度表现分析
stock_performance_2021.csv,135KB,★★★,2021年表现数据,年度表现分析
stock_performance_2022.csv,137KB,★★★,2022年表现数据,年度表现分析
stock_performance_2023.csv,134KB,★★★,2023年表现数据,年度表现分析
stock_performance_2024.csv,140KB,★★★,2024年表现数据,年度表现分析
stock_performance_2025.csv,140KB,★★★,2025年表现数据,年度表现分析
mclean_factors_results.csv,197KB,★★★,McLean因子结果,多因子分析
revenue_growth_analysis_final.csv,134KB,★★★,收入增长分析,基本面分析
income_statement_with_announce.csv,37GB,★★,原始利润表数据,数据备份
balance_sheet_with_announce.csv,18GB,★★,原始资产负债表数据,数据备份
cash_flow_with_announce.csv,16GB,★★,原始现金流量表数据,数据备份
income_statement_with_announce_dedup.csv,1.7GB,★★,去重后利润表数据,中间结果
analyze_duplicates.py,4KB,★★,重复数据分析,数据质量检查
analyze_duplicates_and_missing.py,13KB,★★,重复和缺失数据分析,数据质量检查
clickhouse_query.py,6KB,★★,数据库连接工具,数据库操作
analyze_trading_days.py,3KB,★★,交易天数分析,交易特征分析
analyze_turnover_distribution.py,6KB,★★,换手率分布分析,交易特征分析
mclean_factors_implementation.py,25KB,★★,McLean因子实现,因子模型
improved_mclean_factors.py,14KB,★★,改进因子模型,高级因子分析
optimized_mclean_factors.py,14KB,★★,优化因子模型,高级因子分析
revenue_growth_corrected_final.py,13KB,★★,收入增长分析脚本,基本面分析
merge_industry_info.py,5KB,★★,行业信息合并,行业分析
fix_stock_code_format.py,4KB,★★,代码格式处理,数据标准化
create_stock_code_mapping.py,7KB,★★,代码映射创建,数据标准化
check_statement_types.py,950B,★,报表类型检查,数据验证
test_db_connection.py,1KB,★,数据库连接测试,环境检查
verify_dedup_logic.py,5KB,★,去重逻辑验证,数据验证
quick_check_dedup.py,2KB,★,快速去重检查,数据验证
show_dedup_examples.py,6KB,★,去重示例展示,数据验证
test_correct_dedup.py,5KB,★,正确去重测试,数据验证
test_sample_output.py,5KB,★,样本输出测试,功能测试 