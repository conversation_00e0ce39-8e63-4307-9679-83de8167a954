from clickhouse_driver import Client
import pandas as pd

# 连接ClickHouse数据库
client = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 季报数据覆盖情况分析 ===")

# 1. 总体季报数据统计
print("\n1. 总体季报数据统计...")
query_quarterly_summary = """
SELECT 
    COUNT(*) as quarterly_records,
    COUNT(DISTINCT stock_symbol) as quarterly_stocks,
    COUNT(DISTINCT financial_period_absolute) as unique_periods
FROM priority_quality_fundamental_data_complete_deduped
WHERE period_end_date IS NOT NULL AND period_end_date != ''
  AND (financial_period_absolute LIKE '%Q1%' 
       OR financial_period_absolute LIKE '%Q2%' 
       OR financial_period_absolute LIKE '%Q3%'
       OR financial_period_absolute LIKE '%Q4%')
"""

result_quarterly = client.execute(query_quarterly_summary)
quarterly_records, quarterly_stocks, unique_periods = result_quarterly[0]
print(f"  季报数据记录数: {quarterly_records:,}")
print(f"  有季报数据的股票数: {quarterly_stocks:,}")
print(f"  唯一季报期间数: {unique_periods:,}")

# 2. 按年份和季度分析覆盖情况
print("\n2. 按年份和季度分析覆盖情况...")
query_year_quarter_coverage = """
SELECT 
    fiscal_year,
    quarter,
    COUNT(DISTINCT stock_symbol) as stock_count
FROM (
    SELECT 
        stock_symbol,
        CASE 
            WHEN financial_period_absolute REGEXP 'FY([0-9]{4})Q([1-4])'
            THEN extractAll(financial_period_absolute, 'FY([0-9]{4})Q([1-4])')[1]
            ELSE NULL
        END as fiscal_year,
        CASE 
            WHEN financial_period_absolute REGEXP 'FY([0-9]{4})Q([1-4])'
            THEN extractAll(financial_period_absolute, 'FY([0-9]{4})Q([1-4])')[2]
            ELSE NULL
        END as quarter
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE period_end_date IS NOT NULL AND period_end_date != ''
      AND financial_period_absolute REGEXP 'FY[0-9]{4}Q[1-4]'
) parsed_data
WHERE fiscal_year IS NOT NULL AND quarter IS NOT NULL
GROUP BY fiscal_year, quarter
ORDER BY fiscal_year DESC, quarter
"""

result_coverage = client.execute(query_year_quarter_coverage)
if result_coverage:
    print("  各年份各季度的股票覆盖数:")
    print("  年份    Q1    Q2    Q3    Q4")
    print("  " + "-" * 30)
    
    # 按年份组织数据
    coverage_by_year = {}
    for fiscal_year, quarter, stock_count in result_coverage:
        if fiscal_year not in coverage_by_year:
            coverage_by_year[fiscal_year] = {}
        coverage_by_year[fiscal_year][quarter] = stock_count
    
    # 显示最近10年的数据
    recent_years = sorted(coverage_by_year.keys(), reverse=True)[:10]
    for year in recent_years:
        quarters = coverage_by_year[year]
        q1 = quarters.get('1', 0)
        q2 = quarters.get('2', 0)
        q3 = quarters.get('3', 0)
        q4 = quarters.get('4', 0)
        print(f"  {year}   {q1:4d}  {q2:4d}  {q3:4d}  {q4:4d}")

# 3. 分析每只股票的季报完整性
print("\n3. 分析每只股票的季报完整性...")
query_stock_completeness = """
SELECT 
    quarters_per_year,
    COUNT(*) as stock_year_combinations
FROM (
    SELECT 
        stock_symbol,
        fiscal_year,
        COUNT(DISTINCT quarter) as quarters_per_year
    FROM (
        SELECT 
            stock_symbol,
            CASE 
                WHEN financial_period_absolute REGEXP 'FY([0-9]{4})Q([1-4])'
                THEN extractAll(financial_period_absolute, 'FY([0-9]{4})Q([1-4])')[1]
                ELSE NULL
            END as fiscal_year,
            CASE 
                WHEN financial_period_absolute REGEXP 'FY([0-9]{4})Q([1-4])'
                THEN extractAll(financial_period_absolute, 'FY([0-9]{4})Q([1-4])')[2]
                ELSE NULL
            END as quarter
        FROM priority_quality_fundamental_data_complete_deduped
        WHERE period_end_date IS NOT NULL AND period_end_date != ''
          AND financial_period_absolute REGEXP 'FY[0-9]{4}Q[1-4]'
    ) parsed_data
    WHERE fiscal_year IS NOT NULL AND quarter IS NOT NULL
    GROUP BY stock_symbol, fiscal_year
) completeness_stats
GROUP BY quarters_per_year
ORDER BY quarters_per_year
"""

result_completeness = client.execute(query_stock_completeness)
if result_completeness:
    print("  每年季报完整性分布:")
    total_combinations = sum(count for _, count in result_completeness)
    for quarters, count in result_completeness:
        percentage = count / total_combinations * 100
        print(f"    {quarters}个季度/年: {count:,}个股票-年份组合 ({percentage:.1f}%)")
    
    print(f"  总股票-年份组合数: {total_combinations:,}")

# 4. 找出季报数据不完整的股票示例
print("\n4. 季报数据不完整的股票示例...")
query_incomplete_stocks = """
SELECT 
    stock_symbol,
    fiscal_year,
    quarters_available,
    missing_quarters
FROM (
    SELECT 
        stock_symbol,
        fiscal_year,
        COUNT(DISTINCT quarter) as quarters_available,
        arrayStringConcat(
            arraySort(
                arrayMap(x -> toString(x), 
                    arrayFilter(x -> x NOT IN groupArray(toInt32(quarter)), [1,2,3,4])
                )
            ), ','
        ) as missing_quarters
    FROM (
        SELECT 
            stock_symbol,
            CASE 
                WHEN financial_period_absolute REGEXP 'FY([0-9]{4})Q([1-4])'
                THEN extractAll(financial_period_absolute, 'FY([0-9]{4})Q([1-4])')[1]
                ELSE NULL
            END as fiscal_year,
            CASE 
                WHEN financial_period_absolute REGEXP 'FY([0-9]{4})Q([1-4])'
                THEN extractAll(financial_period_absolute, 'FY([0-9]{4})Q([1-4])')[2]
                ELSE NULL
            END as quarter
        FROM priority_quality_fundamental_data_complete_deduped
        WHERE period_end_date IS NOT NULL AND period_end_date != ''
          AND financial_period_absolute REGEXP 'FY[0-9]{4}Q[1-4]'
    ) parsed_data
    WHERE fiscal_year IS NOT NULL AND quarter IS NOT NULL
    GROUP BY stock_symbol, fiscal_year
    HAVING quarters_available < 4
) incomplete_data
ORDER BY fiscal_year DESC, stock_symbol
LIMIT 20
"""

result_incomplete = client.execute(query_incomplete_stocks)
if result_incomplete:
    print("  季报不完整的股票示例（前20个）:")
    print("  股票代码   年份   可用季度  缺失季度")
    print("  " + "-" * 40)
    for stock, year, available, missing in result_incomplete:
        print(f"  {stock:10s} {year} {available:8d}  {missing}")
else:
    print("  所有股票的季报数据都是完整的！")

# 5. 分析最近5年的季报覆盖率
print("\n5. 最近5年季报覆盖率...")
query_recent_coverage = """
SELECT 
    fiscal_year,
    COUNT(DISTINCT stock_symbol) as total_stocks_with_data,
    SUM(CASE WHEN quarters_available = 4 THEN 1 ELSE 0 END) as complete_stocks,
    AVG(quarters_available) as avg_quarters
FROM (
    SELECT 
        stock_symbol,
        fiscal_year,
        COUNT(DISTINCT quarter) as quarters_available
    FROM (
        SELECT 
            stock_symbol,
            CASE 
                WHEN financial_period_absolute REGEXP 'FY([0-9]{4})Q([1-4])'
                THEN extractAll(financial_period_absolute, 'FY([0-9]{4})Q([1-4])')[1]
                ELSE NULL
            END as fiscal_year,
            CASE 
                WHEN financial_period_absolute REGEXP 'FY([0-9]{4})Q([1-4])'
                THEN extractAll(financial_period_absolute, 'FY([0-9]{4})Q([1-4])')[2]
                ELSE NULL
            END as quarter
        FROM priority_quality_fundamental_data_complete_deduped
        WHERE period_end_date IS NOT NULL AND period_end_date != ''
          AND financial_period_absolute REGEXP 'FY[0-9]{4}Q[1-4]'
    ) parsed_data
    WHERE fiscal_year IS NOT NULL AND quarter IS NOT NULL
    GROUP BY stock_symbol, fiscal_year
) yearly_stats
WHERE toInt32(fiscal_year) >= 2020
GROUP BY fiscal_year
ORDER BY fiscal_year DESC
"""

result_recent = client.execute(query_recent_coverage)
if result_recent:
    print("  年份   总股票数  完整股票数  完整率   平均季度数")
    print("  " + "-" * 50)
    for year, total, complete, avg_quarters in result_recent:
        complete_rate = complete / total * 100 if total > 0 else 0
        print(f"  {year}   {total:8d}  {complete:10d}  {complete_rate:6.1f}%  {avg_quarters:10.1f}")

print("\n=== 分析完成 ===")

