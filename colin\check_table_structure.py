from clickhouse_driver import Client

# 连接ClickHouse数据库
client = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='ap_research'
)

print("=== 检查表结构 ===")

# 检查 priority_quality_fundamental_data_complete_deduped 表结构
query_structure = """
DESCRIBE priority_quality_fundamental_data_complete_deduped
"""

result = client.execute(query_structure)
print("\npriority_quality_fundamental_data_complete_deduped 表结构:")
for row in result:
    print(f"  {row[0]} - {row[1]}")

# 查看几条样本数据
print("\n样本数据:")
query_sample = """
SELECT *
FROM priority_quality_fundamental_data_complete_deduped
LIMIT 3
"""

result_sample = client.execute(query_sample)
if result_sample:
    print(f"  共 {len(result_sample[0])} 列")
    for i, row in enumerate(result_sample):
        print(f"  记录 {i+1}: {row}")

print("\n=== 检查完成 ===")


