import pandas as pd
import numpy as np
from datetime import datetime
from clickhouse_driver import Client

def connect_to_ap_research():
    """连接到ap_research数据库"""
    try:
        client = Client(
            host='************',
            port=9000,
            user='default',
            password='5ur2pK8WZQdy2',
            database='ap_research'
        )
        print("✅ 连接ap_research数据库成功")
        return client
    except Exception as e:
        print(f"❌ 连接数据库失败: {e}")
        return None

# 字段优先级配置
FIELD_CONFIGS = {
    'shareholders_equity': {
        'field_name': 'Shareholders_Equity',
        'fields': [
            "Shareholders' Equity - Attributable to Parent Shareholders - Total",  # 优先级1
            "Common Equity - Total",                                              # 优先级2  
            "Common Equity Attributable to Parent Shareholders",                 # 优先级3
            "Total Shareholders' Equity - including Minority Interest & Hybrid Debt"  # 优先级4
        ]
    },
    'net_income': {
        'field_name': 'Net_Income',
        'fields': [
            "Normalized Net Income - Bottom Line",     # 优先级1
            "Net Income",                              # 优先级2
            "Net Income Available to Common Shareholders"  # 优先级3
        ]
    },
    'total_debt': {
        'field_name': 'Total_Debt', 
        'fields': [
            "Debt - Total",           # 优先级1
            "Total Debt",             # 优先级2
            "Long-term Debt - Total"  # 优先级3
        ]
    },
    'free_cash_flow': {
        'field_name': 'Free_Cash_Flow',
        'fields': [
            "Free Cash Flow",           # 优先级1
            "Operating Cash Flow",      # 优先级2
            "Cash Flow from Operations" # 优先级3
        ]
    }
}

def get_field_with_priority_and_flag(data, field_config, stock_symbol, factor_name):
    """
    根据字段优先级获取数据，并生成字段标志
    
    Returns:
        tuple: (value, field_flag)
    """
    field_name = field_config['field_name']
    field_list = field_config['fields']
    
    for i, field in enumerate(field_list):
        value = data.get(field, None)
        if value is not None and value != 0:
            if i == 0:
                # 使用主字段，无需标志
                return value, ""
            else:
                # 使用备用字段，生成标志
                field_flag = f"[{field_name}_P{i+1}]"  # P表示Priority
                return value, field_flag
    
    # 如果所有字段都没有数据
    field_flag = f"[{field_name}_NA]"
    return 0, field_flag

def calculate_enhanced_factors(stock_symbol, stock_data, listing_years):
    """
    计算增强版因子（带字段标志）
    """
    factors = {'stock_symbol': stock_symbol}
    field_flags = {'stock_symbol': stock_symbol}  # 存储字段标志
    
    if len(stock_data) == 0:
        return factors, field_flags
    
    latest_data = stock_data.iloc[-1]
    
    # 1. 调整后ROCE (adjusted_roce)
    try:
        net_income, ni_flag = get_field_with_priority_and_flag(
            latest_data, FIELD_CONFIGS['net_income'], stock_symbol, 'adjusted_roce'
        )
        equity, eq_flag = get_field_with_priority_and_flag(
            latest_data, FIELD_CONFIGS['shareholders_equity'], stock_symbol, 'adjusted_roce'
        )
        
        if equity > 0:
            factors['adjusted_roce'] = (net_income / equity) * 100
        else:
            factors['adjusted_roce'] = None
            
        # 合并字段标志
        combined_flag = ni_flag + eq_flag
        field_flags['adjusted_roce'] = combined_flag if combined_flag else ""
        
    except:
        factors['adjusted_roce'] = None
        field_flags['adjusted_roce'] = "[ERROR]"
    
    # 2. ROIC (roic)
    try:
        net_income, ni_flag = get_field_with_priority_and_flag(
            latest_data, FIELD_CONFIGS['net_income'], stock_symbol, 'roic'
        )
        debt, debt_flag = get_field_with_priority_and_flag(
            latest_data, FIELD_CONFIGS['total_debt'], stock_symbol, 'roic'
        )
        equity, eq_flag = get_field_with_priority_and_flag(
            latest_data, FIELD_CONFIGS['shareholders_equity'], stock_symbol, 'roic'
        )
        
        invested_capital = debt + equity
        if invested_capital > 0:
            factors['roic'] = (net_income / invested_capital) * 100
        else:
            factors['roic'] = None
            
        # 合并字段标志
        combined_flag = ni_flag + debt_flag + eq_flag
        field_flags['roic'] = combined_flag if combined_flag else ""
        
    except:
        factors['roic'] = None
        field_flags['roic'] = "[ERROR]"
    
    # 3. FCF CAGR (fcf_cagr)
    try:
        if listing_years >= 3:
            years_to_use = min(8, listing_years)
            
            # 当前FCF
            current_fcf, current_flag = get_field_with_priority_and_flag(
                latest_data, FIELD_CONFIGS['free_cash_flow'], stock_symbol, 'fcf_cagr'
            )
            
            # 过去FCF
            past_data = stock_data.iloc[-years_to_use]
            past_fcf, past_flag = get_field_with_priority_and_flag(
                past_data, FIELD_CONFIGS['free_cash_flow'], stock_symbol, 'fcf_cagr'
            )
            
            if past_fcf > 0 and current_fcf > 0:
                cagr = ((current_fcf / past_fcf) ** (1/(years_to_use-1)) - 1) * 100
                factors['fcf_cagr'] = cagr
            else:
                factors['fcf_cagr'] = None
                
            # 合并字段标志（如果当前和过去使用不同字段）
            if current_flag != past_flag:
                combined_flag = f"{current_flag}→{past_flag}" if current_flag or past_flag else ""
            else:
                combined_flag = current_flag
            field_flags['fcf_cagr'] = combined_flag
        else:
            factors['fcf_cagr'] = None
            field_flags['fcf_cagr'] = "[INSUFFICIENT_YEARS]"
    except:
        factors['fcf_cagr'] = None
        field_flags['fcf_cagr'] = "[ERROR]"
    
    # 4. ROIC CAGR (roic_cagr)
    try:
        if listing_years >= 3:
            years_to_use = min(8, listing_years)
            current_roic = factors.get('roic', 0) or 0
            
            # 计算过去的ROIC
            past_data = stock_data.iloc[-years_to_use]
            past_ni, past_ni_flag = get_field_with_priority_and_flag(
                past_data, FIELD_CONFIGS['net_income'], stock_symbol, 'roic_cagr'
            )
            past_debt, past_debt_flag = get_field_with_priority_and_flag(
                past_data, FIELD_CONFIGS['total_debt'], stock_symbol, 'roic_cagr'
            )
            past_equity, past_eq_flag = get_field_with_priority_and_flag(
                past_data, FIELD_CONFIGS['shareholders_equity'], stock_symbol, 'roic_cagr'
            )
            
            past_invested_capital = past_debt + past_equity
            if past_invested_capital > 0:
                past_roic = (past_ni / past_invested_capital) * 100
                
                if past_roic > 0 and current_roic > 0:
                    cagr = ((current_roic / past_roic) ** (1/(years_to_use-1)) - 1) * 100
                    factors['roic_cagr'] = cagr
                else:
                    factors['roic_cagr'] = None
            else:
                factors['roic_cagr'] = None
                
            # 合并过去数据的字段标志
            past_combined_flag = past_ni_flag + past_debt_flag + past_eq_flag
            field_flags['roic_cagr'] = past_combined_flag if past_combined_flag else ""
        else:
            factors['roic_cagr'] = None
            field_flags['roic_cagr'] = "[INSUFFICIENT_YEARS]"
    except:
        factors['roic_cagr'] = None
        field_flags['roic_cagr'] = "[ERROR]"
    
    return factors, field_flags

def main():
    """主函数"""
    print("=== 完整增强版因子计算系统（带字段标志）===")
    print()
    print("📋 字段优先级说明:")
    print("   P1 = 主字段 (无标志)")
    print("   P2 = 备用字段优先级2")
    print("   P3 = 备用字段优先级3")
    print("   P4 = 备用字段优先级4")
    print("   NA = 无数据")
    print("   → = 时间序列中使用了不同字段")
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        return
    
    # 测试Mega7股票
    mega7_stocks = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META']
    
    print(f"\n=== 测试Mega7股票 ===")
    
    # 获取基本面数据
    print("\n1. 获取基本面数据...")
    query = """
    SELECT 
        stock_symbol,
        financial_period_absolute,
        item_name,
        value
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol IN ('AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META')
      AND substring(financial_period_absolute, 1, 2) = 'FY'
    ORDER BY stock_symbol, financial_period_absolute
    """
    
    try:
        result = client.execute(query)
        df = pd.DataFrame(result, columns=['stock_symbol', 'period', 'item_name', 'value'])
        print(f"   📊 获取到 {len(df)} 条基本面记录")
    except Exception as e:
        print(f"❌ 获取基本面数据失败: {e}")
        return
    
    # 转换为透视表
    pivot_df = df.pivot_table(
        index=['stock_symbol', 'period'], 
        columns='item_name', 
        values='value', 
        aggfunc='first'
    ).reset_index()
    
    all_factors = []
    all_field_flags = []
    
    # 计算每只股票的因子
    print(f"\n2. 计算因子...")
    for stock in mega7_stocks:
        stock_data = pivot_df[pivot_df['stock_symbol'] == stock].copy()
        if len(stock_data) == 0:
            continue
        
        # 按期间排序
        stock_data = stock_data.sort_values('period')
        listing_years = len(stock_data)
        
        # 计算因子
        factors, field_flags = calculate_enhanced_factors(stock, stock_data, listing_years)
        
        all_factors.append(factors)
        all_field_flags.append(field_flags)
        
        print(f"   📊 {stock}: {listing_years}年数据")
    
    # 创建结果DataFrame
    factors_df = pd.DataFrame(all_factors)
    flags_df = pd.DataFrame(all_field_flags)
    
    # 合并因子和标志
    combined_results = []
    for i, stock in enumerate(mega7_stocks):
        if i < len(factors_df):
            result_row = {'stock_symbol': stock}
            
            for factor in ['adjusted_roce', 'roic', 'fcf_cagr', 'roic_cagr']:
                factor_value = factors_df.iloc[i].get(factor)
                field_flag = flags_df.iloc[i].get(factor, "")
                
                # 创建带标志的因子名
                if factor_value is not None:
                    result_row[factor] = factor_value
                    if field_flag:
                        result_row[f"{factor}_field_flag"] = field_flag
                    else:
                        result_row[f"{factor}_field_flag"] = "PRIMARY"
                else:
                    result_row[factor] = None
                    result_row[f"{factor}_field_flag"] = field_flag if field_flag else "N/A"
            
            combined_results.append(result_row)
    
    # 创建最终结果DataFrame
    final_df = pd.DataFrame(combined_results)
    
    # 保存结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f"colin/enhanced_factors_with_flags_{timestamp}.csv"
    final_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"\n3. 结果展示:")
    print(f"✅ 增强版因子数据已保存到: {output_file}")
    
    # 显示结果摘要
    print(f"\n📊 Mega7因子计算结果:")
    for _, row in final_df.iterrows():
        stock = row['stock_symbol']
        print(f"\n🔍 {stock}:")
        
        for factor in ['adjusted_roce', 'roic', 'fcf_cagr', 'roic_cagr']:
            value = row[factor]
            flag = row[f"{factor}_field_flag"]
            
            if value is not None:
                if flag == "PRIMARY":
                    print(f"   ✅ {factor}: {value:.4f}")
                else:
                    print(f"   ⚠️  {factor}: {value:.4f} {flag}")
            else:
                print(f"   ❌ {factor}: N/A {flag}")
    
    # 统计字段使用情况
    print(f"\n📈 字段使用统计:")
    
    field_usage_stats = {}
    for factor in ['adjusted_roce', 'roic', 'fcf_cagr', 'roic_cagr']:
        primary_count = 0
        backup_count = 0
        na_count = 0
        
        for _, row in final_df.iterrows():
            flag = row[f"{factor}_field_flag"]
            if flag == "PRIMARY":
                primary_count += 1
            elif "P" in flag:
                backup_count += 1
            else:
                na_count += 1
        
        field_usage_stats[factor] = {
            'primary': primary_count,
            'backup': backup_count, 
            'na': na_count
        }
        
        print(f"   {factor}: 主字段 {primary_count}个, 备用字段 {backup_count}个, 无数据 {na_count}个")
    
    client.disconnect()
    
    print(f"\n🎯 系统特性:")
    print(f"   ✅ 字段优先级自动降级")
    print(f"   ✅ 详细字段使用标志")
    print(f"   ✅ 时间序列字段一致性跟踪")
    print(f"   ✅ 完整的数据质量透明度")
    print(f"\n🚀 现在每个因子都有完整的数据来源标识！")

if __name__ == "__main__":
    main()

