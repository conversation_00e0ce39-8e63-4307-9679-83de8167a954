from clickhouse_driver import Client
import pandas as pd
import numpy as np
from datetime import datetime

def connect_to_ap_research():
    """连接到ap_research数据库"""
    client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')
    print("✅ 连接ap_research数据库成功")
    return client

def calculate_robust_tax_rate(stock_symbol, client):
    """计算健壮的有效税率，处理异常值问题"""
    
    print(f"🔍 修复 {stock_symbol} 的有效税率:")
    
    # 获取年报数据
    query = f"""
    SELECT 
        stock_symbol,
        financial_period_absolute,
        statement_type,
        item_name,
        value,
        period_end_date
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE stock_symbol = '{stock_symbol}'
      AND financial_period_absolute REGEXP '^FY[0-9]{{4}}$'
    ORDER BY financial_period_absolute ASC
    """
    
    result = client.execute(query)
    if not result:
        print(f"   ❌ {stock_symbol}: 无年报数据")
        return None
        
    # 转换为DataFrame
    df = pd.DataFrame(result, columns=[
        'stock_symbol', 'financial_period_absolute', 'statement_type', 'item_name', 'value', 'period_end_date'
    ])
    
    # 透视数据
    pivot_df = df.pivot_table(
        index=['stock_symbol', 'financial_period_absolute', 'period_end_date'],
        columns='item_name',
        values='value',
        aggfunc='first'
    ).reset_index()
    
    pivot_df['fiscal_year'] = pivot_df['financial_period_absolute'].str.extract(r'FY(\d{4})').astype(int)
    pivot_df = pivot_df.sort_values('fiscal_year')
    
    print(f"   📊 获取到 {len(pivot_df)} 年年报数据")
    
    # 计算每年的有效税率
    tax_data = []
    raw_tax_rates = []
    
    for idx, row in pivot_df.iterrows():
        year = row['fiscal_year']
        
        # 所得税费用
        tax_expense = row.get('Income Taxes', 0) or 0
        
        # EBIT (税前利润)
        ebit = row.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
        
        # 计算原始税率
        if ebit > 0:  # 只有正的税前利润才计算税率
            raw_tax_rate = (tax_expense / ebit) * 100
            raw_tax_rates.append(raw_tax_rate)
            
            print(f"   FY{year}: 原始税率 = {raw_tax_rate:.2f}% (税费={tax_expense:,.0f}, EBIT={ebit:,.0f})")
            
            # 异常值检测和处理
            is_normal = True
            reason = ""
            
            # 1. 负税率检查
            if raw_tax_rate < 0:
                if raw_tax_rate > -50:  # 轻微负税率可能是税收优惠
                    reason = "税收优惠/退税"
                else:
                    is_normal = False
                    reason = "极端负税率"
            
            # 2. 超高税率检查
            elif raw_tax_rate > 100:
                is_normal = False
                reason = "超高税率异常"
            
            # 3. 合理范围税率
            elif 0 <= raw_tax_rate <= 50:
                reason = "正常范围"
            
            # 4. 偏高但可能合理的税率
            elif 50 < raw_tax_rate <= 100:
                reason = "偏高但可接受"
            
            if is_normal:
                tax_data.append({
                    'year': year,
                    'tax_expense': tax_expense,
                    'ebit': ebit,
                    'tax_rate': raw_tax_rate,
                    'status': 'normal',
                    'reason': reason
                })
                print(f"     ✅ 接受: {reason}")
            else:
                print(f"     ❌ 拒绝: {reason}")
        else:
            if ebit <= 0:
                print(f"   FY{year}: 跳过 (EBIT≤0: {ebit:,.0f})")
            else:
                print(f"   FY{year}: 跳过 (无税费数据)")
    
    if len(tax_data) == 0:
        print(f"   ❌ 有效税率: 无有效数据")
        return None
    
    # 计算统计指标
    tax_rates = [d['tax_rate'] for d in tax_data]
    
    current_tax_rate = tax_rates[-1] if tax_rates else None
    avg_tax_rate = np.mean(tax_rates) if len(tax_rates) >= 1 else None
    tax_rate_std = np.std(tax_rates, ddof=1) if len(tax_rates) >= 2 else None
    
    print(f"   📊 有效数据: {len(tax_data)}年")
    print(f"   📈 当前税率: {current_tax_rate:.2f}%")
    if avg_tax_rate is not None:
        print(f"   📊 平均税率: {avg_tax_rate:.2f}%")
    if tax_rate_std is not None:
        print(f"   📊 税率标准差: {tax_rate_std:.2f}%")
    
    # 异常值统计
    total_raw = len(raw_tax_rates)
    accepted = len(tax_data)
    rejected = total_raw - accepted
    
    if rejected > 0:
        print(f"   ⚠️  异常值处理: 拒绝{rejected}个异常值，接受{accepted}个正常值")
    
    return {
        'stock_symbol': stock_symbol,
        'current_tax_rate': current_tax_rate,
        'avg_tax_rate': avg_tax_rate,
        'tax_rate_std': tax_rate_std,
        'valid_years': len(tax_data),
        'total_years': total_raw,
        'rejected_count': rejected,
        'tax_data': tax_data
    }

def main():
    """修复有效税率异常值处理问题"""
    
    print("=== 修复有效税率异常值处理问题 ===")
    print()
    
    client = connect_to_ap_research()
    
    # 测试有异常税率问题的股票
    problem_stocks = ['AMZN', 'TSLA', 'AAPL', 'META', 'MSFT', 'GOOGL']
    
    results = []
    
    for stock in problem_stocks:
        print(f"{'='*60}")
        result = calculate_robust_tax_rate(stock, client)
        if result:
            results.append(result)
        print()
    
    print("📋 修复结果总结:")
    print("-" * 100)
    print(f"{'股票':<8} {'当前税率':<10} {'平均税率':<10} {'标准差':<10} {'有效年':<8} {'异常值':<8} {'处理率'}")
    print("-" * 100)
    
    for r in results:
        processing_rate = f"{(r['valid_years']/r['total_years']*100):.1f}%" if r['total_years'] > 0 else "N/A"
        
        current_str = f"{r['current_tax_rate']:.1f}%" if r['current_tax_rate'] is not None else "N/A"
        avg_str = f"{r['avg_tax_rate']:.1f}%" if r['avg_tax_rate'] is not None else "N/A"
        std_str = f"{r['tax_rate_std']:.1f}%" if r['tax_rate_std'] is not None else "N/A"
        
        print(f"{r['stock_symbol']:<8} {current_str:<10} {avg_str:<10} {std_str:<10} "
              f"{r['valid_years']:<8} {r['rejected_count']:<8} {processing_rate}")
    
    print("-" * 100)
    print()
    
    print("🎯 修复要点:")
    print("   ✅ 只使用正EBIT年份计算税率")
    print("   ✅ 拒绝极端异常税率 (<-50% 或 >100%)")
    print("   ✅ 接受轻微负税率 (可能是税收优惠)")
    print("   ✅ 接受偏高但合理的税率 (≤100%)")
    print("   ✅ 提供详细的异常值处理统计")
    
    print()
    print("📊 税率合理性标准:")
    print("   ✅ 正常范围: 0% - 50%")
    print("   ⚠️  偏高但可接受: 50% - 100%") 
    print("   ⚠️  轻微负税率: -50% - 0% (税收优惠)")
    print("   ❌ 极端异常: <-50% 或 >100%")
    
    # 保存结果
    if results:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"colin/fixed_tax_rates_{timestamp}.csv"
        
        # 准备保存数据
        save_data = []
        for r in results:
            save_data.append({
                'stock_symbol': r['stock_symbol'],
                'current_tax_rate': r['current_tax_rate'],
                'avg_tax_rate': r['avg_tax_rate'],
                'tax_rate_std': r['tax_rate_std'],
                'valid_years': r['valid_years'],
                'rejected_count': r['rejected_count']
            })
        
        df_results = pd.DataFrame(save_data)
        df_results.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"✅ 修复结果已保存到: {filename}")

if __name__ == "__main__":
    main()

