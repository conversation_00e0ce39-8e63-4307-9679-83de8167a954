from clickhouse_driver import Client
import pandas as pd
import numpy as np
import time

# 连接ClickHouse数据库
client = Client(
    host='************',
    port=9000,
    user='default',
    password='5ur2pK8WZQdy2',
    database='lseg'
)

print("=== 改进的Colin McLean因子计算 ===")
print("基于三大财务报表数据，适应2025年美股市场")

def clean_instrument_code(code):
    """清理股票代码，移除后缀"""
    if code is None:
        return None
    # 移除常见的后缀
    suffixes = ['.NB', '.O', '.K', '.N', '.A', '.P', '.Q', '.Z', '.T', '.V', '.W', '.X', '.Y', '.L', '.M', '.S', '.U', '.R', '.G', '.H', '.I', '.J', '.B', '.C', '.D', '.E', '.F']
    for suffix in suffixes:
        if code.endswith(suffix):
            return code[:-len(suffix)]
    return code

def get_total_revenue_data():
    """获取Total Revenue数据"""
    print("\n1. 获取Total Revenue数据...")
    
    revenue_query = """
    SELECT
        instrument,
        financial_period_absolute,
        MAX(CASE WHEN item_name = 'Revenue from Business Activities - Total' THEN income_statement ELSE NULL END) AS total_revenue,
        MAX(CASE WHEN financial_concept_code = 'SNTS' THEN income_statement ELSE NULL END) AS total_revenue_fcc
    FROM lseg.income_statement
    WHERE (
        item_name = 'Revenue from Business Activities - Total'
        OR financial_concept_code = 'SNTS'
    )
    AND financial_period_absolute LIKE '%Q%'
    GROUP BY instrument, financial_period_absolute
    HAVING (
        total_revenue IS NOT NULL
        OR total_revenue_fcc IS NOT NULL
    )
    """
    
    df_revenue = pd.DataFrame(client.execute(revenue_query), 
                             columns=['instrument', 'financial_period_absolute', 'total_revenue', 'total_revenue_fcc'])
    
    # 合并两种方式获取的revenue数据
    df_revenue['total_revenue_final'] = df_revenue['total_revenue'].combine_first(df_revenue['total_revenue_fcc'])
    df_revenue = df_revenue.drop(columns=['total_revenue', 'total_revenue_fcc'])
    
    # 清理股票代码
    df_revenue['instrument_clean'] = df_revenue['instrument'].apply(clean_instrument_code)
    
    print(f"  Total Revenue数据记录数: {len(df_revenue):,}")
    return df_revenue

def get_operating_profit_data():
    """获取营业利润数据"""
    print("\n2. 获取营业利润数据...")
    
    profit_query = """
    SELECT
        instrument,
        financial_period_absolute,
        MAX(CASE WHEN item_name LIKE '%Operating Profit%' THEN income_statement ELSE NULL END) AS operating_profit,
        MAX(CASE WHEN item_name LIKE '%Operating Income%' THEN income_statement ELSE NULL END) AS operating_income
    FROM lseg.income_statement
    WHERE (
        item_name LIKE '%Operating Profit%'
        OR item_name LIKE '%Operating Income%'
    )
    AND financial_period_absolute LIKE '%Q%'
    GROUP BY instrument, financial_period_absolute
    HAVING (
        operating_profit IS NOT NULL
        OR operating_income IS NOT NULL
    )
    """
    
    df_profit = pd.DataFrame(client.execute(profit_query), 
                            columns=['instrument', 'financial_period_absolute', 'operating_profit', 'operating_income'])
    
    # 合并营业利润数据
    df_profit['operating_profit_final'] = df_profit['operating_profit'].combine_first(df_profit['operating_income'])
    df_profit = df_profit.drop(columns=['operating_profit', 'operating_income'])
    
    # 清理股票代码
    df_profit['instrument_clean'] = df_profit['instrument'].apply(clean_instrument_code)
    
    print(f"  营业利润数据记录数: {len(df_profit):,}")
    return df_profit

def get_cash_flow_data():
    """获取经营现金流数据"""
    print("\n3. 获取经营现金流数据...")
    
    cashflow_query = """
    SELECT
        instrument,
        financial_period_absolute,
        MAX(CASE WHEN item_name LIKE '%Operating Activities%' THEN cash_flow_statement ELSE NULL END) AS operating_cash_flow
    FROM lseg.cash_flow
    WHERE item_name LIKE '%Operating Activities%'
    AND financial_period_absolute LIKE '%Q%'
    GROUP BY instrument, financial_period_absolute
    HAVING operating_cash_flow IS NOT NULL
    """
    
    df_cashflow = pd.DataFrame(client.execute(cashflow_query), 
                              columns=['instrument', 'financial_period_absolute', 'operating_cash_flow'])
    
    # 清理股票代码
    df_cashflow['instrument_clean'] = df_cashflow['instrument'].apply(clean_instrument_code)
    
    print(f"  经营现金流数据记录数: {len(df_cashflow):,}")
    return df_cashflow

def get_balance_sheet_data():
    """获取资产负债表关键数据"""
    print("\n4. 获取资产负债表数据...")
    
    balance_query = """
    SELECT
        instrument,
        financial_period_absolute,
        MAX(CASE WHEN item_name LIKE '%Total Assets%' THEN balance_sheet ELSE NULL END) AS total_assets,
        MAX(CASE WHEN item_name LIKE '%Total Liabilities%' THEN balance_sheet ELSE NULL END) AS total_liabilities,
        MAX(CASE WHEN item_name LIKE '%Cash%' THEN balance_sheet ELSE NULL END) AS cash_equivalents,
        MAX(CASE WHEN item_name LIKE '%Current Assets%' THEN balance_sheet ELSE NULL END) AS current_assets,
        MAX(CASE WHEN item_name LIKE '%Current Liabilities%' THEN balance_sheet ELSE NULL END) AS current_liabilities
    FROM lseg.balance_sheet_history
    WHERE (
        item_name LIKE '%Total Assets%'
        OR item_name LIKE '%Total Liabilities%'
        OR item_name LIKE '%Cash%'
        OR item_name LIKE '%Current Assets%'
        OR item_name LIKE '%Current Liabilities%'
    )
    AND financial_period_absolute LIKE '%Q%'
    GROUP BY instrument, financial_period_absolute
    """
    
    df_balance = pd.DataFrame(client.execute(balance_query), 
                             columns=['instrument', 'financial_period_absolute', 'total_assets', 'total_liabilities', 
                                     'cash_equivalents', 'current_assets', 'current_liabilities'])
    
    # 清理股票代码
    df_balance['instrument_clean'] = df_balance['instrument'].apply(clean_instrument_code)
    
    print(f"  资产负债表数据记录数: {len(df_balance):,}")
    return df_balance

def get_market_data():
    """获取市值数据"""
    print("\n5. 获取市值数据...")
    
    market_query = """
    SELECT
        ric,
        close as close_price,
        trade_date
    FROM lseg.hfq_intrday_1_day
    WHERE trade_date = (
        SELECT MAX(trade_date)
        FROM lseg.hfq_intrday_1_day
        WHERE trade_date != '0000-00-00'
    )
    AND close > 0
    """
    
    df_market = pd.DataFrame(client.execute(market_query), 
                            columns=['ric', 'close_price', 'trade_date'])
    
    # 清理股票代码
    df_market['instrument_clean'] = df_market['ric'].apply(clean_instrument_code)
    
    print(f"  市值数据记录数: {len(df_market):,}")
    return df_market

def calculate_factors(df_merged):
    """计算改进的因子"""
    print("\n6. 计算改进的因子...")
    
    # 按股票和财年分组计算
    df_factors = df_merged.groupby('instrument_clean').agg({
        'total_revenue_final': ['count', 'mean', 'std'],
        'operating_profit_final': ['mean', 'std'],
        'operating_cash_flow': ['mean', 'std'],
        'total_assets': 'mean',
        'total_liabilities': 'mean',
        'cash_equivalents': 'mean',
        'current_assets': 'mean',
        'current_liabilities': 'mean',
        'close_price': 'last'
    }).reset_index()
    
    # 重命名列
    df_factors.columns = ['instrument_clean', 'revenue_count', 'avg_revenue', 'revenue_std',
                         'avg_operating_profit', 'operating_profit_std',
                         'avg_operating_cashflow', 'operating_cashflow_std',
                         'avg_total_assets', 'avg_total_liabilities',
                         'avg_cash', 'avg_current_assets', 'avg_current_liabilities',
                         'latest_price']
    
    # 计算因子
    df_factors['revenue_growth_stability'] = df_factors['revenue_std'] / df_factors['avg_revenue']
    df_factors['operating_margin'] = df_factors['avg_operating_profit'] / df_factors['avg_revenue']
    df_factors['cash_flow_quality'] = df_factors['avg_operating_cashflow'] / df_factors['avg_operating_profit']
    df_factors['debt_ratio'] = df_factors['avg_total_liabilities'] / df_factors['avg_total_assets']
    df_factors['cash_richness'] = df_factors['avg_cash'] / df_factors['avg_total_assets']
    df_factors['liquidity_ratio'] = df_factors['avg_current_assets'] / df_factors['avg_current_liabilities']
    df_factors['asset_turnover'] = df_factors['avg_revenue'] / df_factors['avg_total_assets']
    df_factors['ps_ratio'] = (df_factors['latest_price'] * 1000) / df_factors['avg_revenue']  # 假设每股价格，需要调整
    
    # 筛选条件
    df_factors['factor_1'] = (df_factors['revenue_growth_stability'] < 0.4) & (df_factors['revenue_count'] >= 3)
    df_factors['factor_2'] = df_factors['operating_margin'] > 0.1
    df_factors['factor_3'] = df_factors['cash_flow_quality'] > 1.2
    df_factors['factor_4'] = df_factors['debt_ratio'] < 0.4
    df_factors['factor_5'] = df_factors['cash_richness'] > 0.05
    df_factors['factor_6'] = df_factors['liquidity_ratio'] > 1.5
    df_factors['factor_7'] = df_factors['asset_turnover'] > 0.6
    df_factors['factor_8'] = df_factors['ps_ratio'] < 5.0
    
    # 计算综合评分
    factor_columns = ['factor_1', 'factor_2', 'factor_3', 'factor_4', 'factor_5', 'factor_6', 'factor_7', 'factor_8']
    df_factors['total_score'] = df_factors[factor_columns].sum(axis=1)
    
    print(f"  计算完成的因子记录数: {len(df_factors):,}")
    return df_factors

def analyze_results(df_factors):
    """分析结果"""
    print("\n7. 分析因子结果...")
    
    # 因子覆盖率
    factor_columns = ['factor_1', 'factor_2', 'factor_3', 'factor_4', 'factor_5', 'factor_6', 'factor_7', 'factor_8']
    factor_names = ['营收稳定性', '营业利润率', '现金流质量', '债务比率', '现金充裕度', '流动性比率', '资产周转率', '市销率']
    
    print(f"\n总股票数: {len(df_factors):,}")
    print("\n各因子覆盖率:")
    for i, (col, name) in enumerate(zip(factor_columns, factor_names)):
        coverage = df_factors[col].sum()
        percentage = (coverage / len(df_factors)) * 100
        print(f"  {name}: {coverage:,} ({percentage:.1f}%)")
    
    # 评分分布
    print(f"\n评分分布:")
    score_counts = df_factors['total_score'].value_counts().sort_index()
    for score, count in score_counts.items():
        percentage = (count / len(df_factors)) * 100
        print(f"  评分{score}: {count:,} 只股票 ({percentage:.1f}%)")
    
    # 筛选高分股票
    high_score_stocks = df_factors[df_factors['total_score'] >= 6]
    print(f"\n高分股票 (评分>=6): {len(high_score_stocks):,} 只")
    
    return high_score_stocks

def main():
    """主函数"""
    try:
        # 获取数据
        df_revenue = get_total_revenue_data()
        df_profit = get_operating_profit_data()
        df_cashflow = get_cash_flow_data()
        df_balance = get_balance_sheet_data()
        df_market = get_market_data()
        
        # 合并数据
        print("\n6. 合并所有数据...")
        
        # 先删除重复的instrument列，只保留instrument_clean
        df_revenue_clean = df_revenue.drop(columns=['instrument'])
        df_profit_clean = df_profit.drop(columns=['instrument'])
        df_cashflow_clean = df_cashflow.drop(columns=['instrument'])
        df_balance_clean = df_balance.drop(columns=['instrument'])
        df_market_clean = df_market.drop(columns=['ric'])
        
        df_merged = df_revenue_clean.merge(df_profit_clean, on=['instrument_clean', 'financial_period_absolute'], how='inner')
        df_merged = df_merged.merge(df_cashflow_clean, on=['instrument_clean', 'financial_period_absolute'], how='inner')
        df_merged = df_merged.merge(df_balance_clean, on=['instrument_clean', 'financial_period_absolute'], how='inner')
        df_merged = df_merged.merge(df_market_clean, on='instrument_clean', how='inner')
        
        print(f"  合并后数据记录数: {len(df_merged):,}")
        
        # 计算因子
        df_factors = calculate_factors(df_merged)
        
        # 分析结果
        high_score_stocks = analyze_results(df_factors)
        
        # 保存结果
        print("\n8. 保存结果...")
        df_factors.to_csv('improved_mclean_factors_results.csv', index=False, encoding='utf-8')
        high_score_stocks.to_csv('improved_mclean_high_score_stocks.csv', index=False, encoding='utf-8')
        
        print("  因子结果已保存到: improved_mclean_factors_results.csv")
        print("  高分股票已保存到: improved_mclean_high_score_stocks.csv")
        
        print("\n=== 改进的Colin McLean因子计算完成 ===")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 