def get_eps_with_priority(data, price_data=None):
    """
    按优先级获取EPS，优先使用直接的EPS字段，备选手动计算
    """
    
    # EPS字段优先级映射
    eps_fields = [
        'EPS - Diluted - excluding Extraordinary Items Applicable to Common - Total',
        'EPS - Basic - excluding Extraordinary Items Applicable to Common - Total', 
        'EPS - Diluted - including Extraordinary Items Applicable to Common - Total',
        'EPS - Basic - including Extraordinary Items Applicable to Common - Total',
        'EPS - Diluted - excluding Extraordinary Items - Normalized - Total',
        'EPS - Basic - excluding Extraordinary Items - Normalized - Total'
    ]
    
    # 1. 优先使用直接的EPS字段
    for i, field in enumerate(eps_fields):
        eps_value = data.get(field, 0) or 0
        if eps_value != 0:
            priority = "PRIMARY" if i == 0 else f"P{i+1}"
            return eps_value, priority, "DIRECT_EPS"
    
    # 2. 备选方案：手动计算EPS
    net_income_fields = [
        'Normalized Net Income - Bottom Line',
        'Net Income - Basic - including Extraordinary Items Applicable to Common - Total',
        'Net Income - Diluted - including Extraordinary Items Applicable to Common - Total',
        'Net Income before Minority Interest',
        'Net Income after Minority Interest'
    ]
    
    share_fields = [
        'Common Shares - Outstanding - Total',
        'Common Shares - Outstanding - Issue Specific',
        'Shares used to calculate Diluted EPS - Total',
        'Shares used to calculate Basic EPS - Total'
    ]
    
    # 获取净利润
    net_income, ni_priority = get_field_with_priority(data, net_income_fields)
    # 获取股份数
    shares, share_priority = get_field_with_priority(data, share_fields)
    
    if net_income > 0 and shares > 0:
        calculated_eps = net_income / shares
        flag = f"CALCULATED_EPS(NI:{ni_priority},SHARE:{share_priority})"
        return calculated_eps, "FALLBACK", flag
    
    return 0, "NA", "NO_EPS_DATA"

def get_field_with_priority(data, field_priority_list):
    """按优先级获取字段值"""
    for i, field in enumerate(field_priority_list):
        value = data.get(field, 0) or 0
        if value != 0:
            priority = "PRIMARY" if i == 0 else f"P{i+1}"
            return value, priority
    return 0, "NA"

# 示例：改进后的valuation_bubble_signal计算
def calculate_improved_valuation_bubble_signal(latest_data, price_data, pivot_data, field_mappings):
    """改进的估值泡沫信号计算，优先使用直接EPS字段"""
    
    try:
        # 使用改进的EPS获取方法
        eps, eps_priority, eps_method = get_eps_with_priority(latest_data, price_data)
        
        if eps > 0 and price_data:
            pe_ratio = price_data['close_price'] / eps
            
            # 计算收入CAGR（保持原有逻辑）
            if len(pivot_data) >= 3:
                revenues = []
                for i in range(min(5, len(pivot_data))):
                    data = pivot_data.iloc[-(i+1)]
                    revenue, _ = get_field_with_priority(data, field_mappings['revenue'])
                    if revenue > 0:
                        revenues.append({'year': len(pivot_data) - i, 'value': revenue})
                
                revenues.reverse()
                
                if len(revenues) >= 2:
                    start_revenue = revenues[0]['value']
                    end_revenue = revenues[-1]['value']
                    years = len(revenues) - 1
                    
                    if start_revenue > 0:
                        revenue_cagr = (pow(end_revenue / start_revenue, 1 / years) - 1) * 100
                        
                        if revenue_cagr > 0:
                            peg_ratio = pe_ratio / revenue_cagr
                            return {
                                'valuation_bubble_signal': min(peg_ratio, 5),
                                'valuation_bubble_signal_field_flag': f"EPS:{eps_priority},METHOD:{eps_method}",
                                'valuation_bubble_signal_eps_value': eps,
                                'valuation_bubble_signal_pe_ratio': pe_ratio,
                                'valuation_bubble_signal_revenue_cagr': revenue_cagr
                            }
        
        return {
            'valuation_bubble_signal': None,
            'valuation_bubble_signal_field_flag': f"EPS:{eps_priority},METHOD:{eps_method}",
            'valuation_bubble_signal_eps_value': eps if eps > 0 else None,
            'valuation_bubble_signal_pe_ratio': None,
            'valuation_bubble_signal_revenue_cagr': None
        }
        
    except Exception as e:
        return {
            'valuation_bubble_signal': None,
            'valuation_bubble_signal_field_flag': f"ERROR:{str(e)}",
            'valuation_bubble_signal_eps_value': None,
            'valuation_bubble_signal_pe_ratio': None,
            'valuation_bubble_signal_revenue_cagr': None
        }

# 测试示例
if __name__ == "__main__":
    print("🔧 EPS优化计算示例")
    print("=" * 60)
    
    print("💡 优势:")
    print("1. ✅ 优先使用数据库中现成的EPS字段（更准确）")
    print("2. ✅ 多层级备选方案确保健壮性")
    print("3. ✅ 详细的字段使用追踪")
    print("4. ✅ 包含EPS值、PE比率、收入CAGR等详细信息")
    
    print("\n📊 预期效果:")
    print("• 提高EPS计算精度")
    print("• 减少手动计算的误差")
    print("• 更好的字段映射追踪")
    print("• 为其他需要EPS的因子提供复用基础")
    
    print("\n🎯 适用场景:")
    print("• valuation_bubble_signal (PEG比率)")
    print("• 其他需要EPS的估值因子")
    print("• PE比率相关的分析")

