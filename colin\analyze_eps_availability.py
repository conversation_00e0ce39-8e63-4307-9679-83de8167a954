from clickhouse_driver import Client
import pandas as pd

def connect_to_ap_research():
    """连接到ap_research数据库"""
    client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')
    print("✅ 连接ap_research数据库成功")
    return client

def analyze_eps_data():
    """分析EPS数据的可用性"""
    
    print("🔍 分析基本面数据中的EPS情况")
    print("=" * 80)
    
    client = connect_to_ap_research()
    
    # 1. 搜索所有EPS相关的字段
    print("1. 搜索所有EPS相关字段:")
    print("-" * 60)
    
    eps_query = """
    SELECT DISTINCT item_name, statement_type, COUNT(*) as frequency
    FROM priority_quality_fundamental_data_complete_deduped
    WHERE item_name ILIKE '%EPS%' 
       OR item_name ILIKE '%earnings per share%'
       OR item_name ILIKE '%per share%'
    GROUP BY item_name, statement_type
    ORDER BY frequency DESC
    """
    
    eps_result = client.execute(eps_query)
    
    print("📊 EPS相关字段:")
    for row in eps_result:
        print(f"   {row[0]:<60} | {row[1]:<20} | 频次: {row[2]:,}")
    
    print()
    
    # 2. 检查Mega7的EPS数据情况
    print("2. 检查Mega7的EPS数据情况:")
    print("-" * 60)
    
    mega7_stocks = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META']
    
    for stock in mega7_stocks:
        print(f"\n📈 {stock}:")
        
        # 查找该股票的EPS相关数据
        stock_eps_query = f"""
        SELECT 
            financial_period_absolute,
            item_name,
            value
        FROM priority_quality_fundamental_data_complete_deduped
        WHERE stock_symbol = '{stock}'
          AND financial_period_absolute REGEXP '^FY[0-9]{{4}}$'
          AND (item_name ILIKE '%EPS%' 
               OR item_name ILIKE '%earnings per share%'
               OR item_name ILIKE '%per share%')
        ORDER BY financial_period_absolute DESC, item_name
        LIMIT 10
        """
        
        stock_eps_result = client.execute(stock_eps_query)
        
        if stock_eps_result:
            for row in stock_eps_result:
                print(f"   {row[0]} | {row[1]:<50} | {row[2]:>10.4f}")
        else:
            print("   ❌ 未找到直接的EPS数据")
            
            # 检查净利润和股份数据用于计算EPS
            calc_eps_query = f"""
            SELECT 
                financial_period_absolute,
                item_name,
                value
            FROM priority_quality_fundamental_data_complete_deduped
            WHERE stock_symbol = '{stock}'
              AND financial_period_absolute = (
                  SELECT MAX(financial_period_absolute) 
                  FROM priority_quality_fundamental_data_complete_deduped 
                  WHERE stock_symbol = '{stock}' 
                  AND financial_period_absolute REGEXP '^FY[0-9]{{4}}$'
              )
              AND (item_name = 'Normalized Net Income - Bottom Line'
                   OR item_name LIKE '%Shares%Outstanding%'
                   OR item_name LIKE '%Common Shares%')
            ORDER BY item_name
            """
            
            calc_eps_result = client.execute(calc_eps_query)
            
            if calc_eps_result:
                print("   🔧 可用于计算EPS的数据:")
                net_income = None
                shares = None
                
                for row in calc_eps_result:
                    print(f"   {row[0]} | {row[1]:<50} | {row[2]:>15,.0f}")
                    
                    if 'Net Income' in row[1]:
                        net_income = row[2]
                    elif 'Shares' in row[1] and 'Outstanding' in row[1]:
                        shares = row[2]
                
                if net_income is not None and shares is not None and shares > 0:
                    calculated_eps = net_income / shares
                    print(f"   💡 计算得出EPS = {net_income:,.0f} / {shares:,.0f} = ${calculated_eps:.4f}")
                else:
                    print("   ❌ 无法计算EPS - 缺少净利润或股份数据")
    
    print()
    
    # 3. 分析当前valuation_bubble_signal的计算方式
    print("3. 当前valuation_bubble_signal的EPS计算方式:")
    print("-" * 60)
    
    print("📊 当前实现:")
    print("""
    # 在 valuation_bubble_signal 计算中
    net_income = get_field_with_priority(latest_data, net_income_fields)
    shares = get_field_with_priority(latest_data, share_fields)
    
    if net_income > 0 and shares > 0 and price_data:
        eps = net_income / shares  # 手动计算EPS
        pe_ratio = price_data['close_price'] / eps
        # ... 继续PEG计算
    """)
    
    print("✅ 优点:")
    print("   • 使用最可靠的净利润数据")
    print("   • 使用最新的股份数据")
    print("   • 有字段优先级保障")
    print()
    
    print("🔧 建议改进:")
    print("   1. 优先使用直接的EPS字段（如果存在）")
    print("   2. 备选方案：净利润/股份数计算")
    print("   3. 多种EPS字段的优先级映射")
    
    # 4. 建议的EPS字段优先级
    print()
    print("4. 建议的EPS字段优先级映射:")
    print("-" * 60)
    
    suggested_eps_fields = [
        "Earnings Per Share - Basic - including Extraordinary Items",
        "Earnings Per Share - Diluted - including Extraordinary Items", 
        "Earnings Per Share - Basic - excluding Extraordinary Items",
        "Earnings Per Share - Diluted - excluding Extraordinary Items",
        "Income Available to Common Shares / Shares Outstanding"  # 计算字段
    ]
    
    print("💡 建议的EPS字段优先级:")
    for i, field in enumerate(suggested_eps_fields):
        priority = "PRIMARY" if i == 0 else f"P{i+1}"
        print(f"   {priority:<8}: {field}")
    
    print()
    print("📝 如果所有直接EPS字段都不可用，则使用:")
    print("   FALLBACK : 净利润 / 流通股数 (手动计算)")
    
    return eps_result

if __name__ == "__main__":
    analyze_eps_data()

