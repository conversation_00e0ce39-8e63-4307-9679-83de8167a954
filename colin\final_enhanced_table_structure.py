from datetime import datetime, date

print("=== 最终增强的表结构设计 ===")

def create_enhanced_quarterly_data_filled_table():
    """
    创建增强的季报数据补充表结构
    """
    print("\n1. 增强的quarterly_data_filled表结构...")
    
    table_sql = """
    CREATE TABLE IF NOT EXISTS ap_research.quarterly_data_filled_enhanced (
        -- 基本标识字段
        id String,
        stock_symbol String,
        financial_period_absolute String,
        statement_type String,
        item_name String,
        
        -- 原始数据字段
        original_value Nullable(Float64),
        original_effective_date Nullable(Date),
        
        -- 估算数据字段
        filled_simple_estimates Nullable(Float64),
        estimated_effective_date Nullable(Date),
        
        -- 修正数据字段
        corrected_filled_simple_estimates Nullable(Float64),
        corrected_effective_date Nullable(Date),
        
        -- 数据状态字段
        data_status String,  -- original, estimated, corrected_real, corrected_estimated, etc.
        fill_method Nullable(String),  -- rolling_average, direct_calculation, etc.
        confidence_score Nullable(Float64),
        
        -- 会计科目管理字段（新增）
        item_status String,  -- active, historical_only, current_only, renamed
        item_source String,  -- historical_pattern, current_report, both
        item_mapping Nullable(String),  -- 科目映射：如果重命名，记录原/新科目名
        correction_applicable String,  -- yes, no, partial
        historical_frequency Nullable(Int32),  -- 历史出现频次
        last_historical_period Nullable(String),  -- 最后出现的历史期间
        first_current_period Nullable(String),  -- 首次出现的当前期间
        
        -- 详细标注字段（新增）
        estimation_notes Nullable(String),  -- 估算阶段的详细说明
        correction_notes Nullable(String),  -- 修正阶段的详细说明
        item_change_reason Nullable(String),  -- 科目变化原因
        
        -- 源数据追踪字段
        source_periods Nullable(String),  -- 用于计算的源期间
        calculation_formula Nullable(String),  -- 具体计算公式
        
        -- 元数据字段
        created_at DateTime,
        updated_at DateTime,
        processing_version String,  -- 处理版本号
        notes Nullable(String)  -- 其他备注信息
    )
    ENGINE = MergeTree()
    ORDER BY (stock_symbol, financial_period_absolute, statement_type, item_name)
    PARTITION BY toYYYYMM(estimated_effective_date);
    """
    
    print("SQL创建语句:")
    print(table_sql)
    
    return table_sql

def provide_field_usage_examples():
    """
    提供字段使用示例
    """
    print(f"\n2. 字段使用示例...")
    
    examples = [
        {
            'scenario': 'active科目 - 正常估算和修正',
            'record': {
                'id': 'CCEP_FY2023Q1_income_statement_Total_Revenue',
                'stock_symbol': 'CCEP',
                'financial_period_absolute': 'FY2023Q1',
                'statement_type': 'income_statement',
                'item_name': 'Total Revenue',
                'original_value': None,
                'filled_simple_estimates': 2500.0,
                'corrected_filled_simple_estimates': 2650.0,
                'data_status': 'corrected_estimated',
                'fill_method': 'rolling_average',
                'confidence_score': 0.75,
                'item_status': 'active',
                'item_source': 'both',
                'item_mapping': None,
                'correction_applicable': 'yes',
                'historical_frequency': 3,
                'last_historical_period': 'FY2022Q1',
                'first_current_period': 'FY2023',
                'estimation_notes': '基于历史Q1数据（2020Q1-2022Q1）进行滚动平均估算',
                'correction_notes': '已使用FY2023年报数据修正，科目在最新报告中存在',
                'item_change_reason': None
            }
        },
        
        {
            'scenario': 'historical_only科目 - 只估算不修正',
            'record': {
                'id': 'CCEP_FY2023Q1_income_statement_Depreciation',
                'stock_symbol': 'CCEP',
                'financial_period_absolute': 'FY2023Q1',
                'statement_type': 'income_statement',
                'item_name': 'Depreciation',
                'original_value': None,
                'filled_simple_estimates': 150.0,
                'corrected_filled_simple_estimates': None,
                'data_status': 'estimated',
                'fill_method': 'rolling_average',
                'confidence_score': 0.60,
                'item_status': 'historical_only',
                'item_source': 'historical_pattern',
                'item_mapping': None,
                'correction_applicable': 'no',
                'historical_frequency': 3,
                'last_historical_period': 'FY2022Q1',
                'first_current_period': None,
                'estimation_notes': '基于历史Q1数据（2020Q1-2022Q1）进行滚动平均估算，科目在最新报告中已不存在',
                'correction_notes': '科目在FY2023年报中不存在，可能因会计准则变更而停用，无法修正',
                'item_change_reason': 'accounting_standard_change'
            }
        },
        
        {
            'scenario': 'current_only科目 - 无法估算',
            'record': {
                'id': 'CCEP_FY2023Q1_income_statement_EBITDA',
                'stock_symbol': 'CCEP',
                'financial_period_absolute': 'FY2023Q1',
                'statement_type': 'income_statement',
                'item_name': 'EBITDA',
                'original_value': None,
                'filled_simple_estimates': None,
                'corrected_filled_simple_estimates': None,
                'data_status': 'missing',
                'fill_method': None,
                'confidence_score': None,
                'item_status': 'current_only',
                'item_source': 'current_report',
                'item_mapping': None,
                'correction_applicable': 'no',
                'historical_frequency': None,
                'last_historical_period': None,
                'first_current_period': 'FY2023',
                'estimation_notes': '科目在历史Q1报告中从未出现，无历史数据基础，无法进行估算',
                'correction_notes': '不适用，因为没有估算值需要修正',
                'item_change_reason': 'new_business_line'
            }
        },
        
        {
            'scenario': 'renamed科目 - 映射处理',
            'record': {
                'id': 'MRP_FY2023Q2_income_statement_RD_Expenses',
                'stock_symbol': 'MRP',
                'financial_period_absolute': 'FY2023Q2',
                'statement_type': 'income_statement',
                'item_name': 'R&D Expenses',
                'original_value': None,
                'filled_simple_estimates': 45.0,
                'corrected_filled_simple_estimates': 48.5,
                'data_status': 'corrected_estimated',
                'fill_method': 'rolling_average',
                'confidence_score': 0.80,
                'item_status': 'renamed',
                'item_source': 'both',
                'item_mapping': 'Research and Development',
                'correction_applicable': 'yes',
                'historical_frequency': 4,
                'last_historical_period': 'FY2022Q2',
                'first_current_period': 'FY2023',
                'estimation_notes': '基于历史"Research and Development"Q2数据进行估算',
                'correction_notes': '已使用最新"R&D Expenses"科目数据修正，检测到科目重命名（语义相似度0.85，数值相关性0.92）',
                'item_change_reason': 'item_name_standardization'
            }
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n  📊 示例{i}: {example['scenario']}")
        record = example['record']
        
        # 基本信息
        print(f"     基本信息:")
        print(f"       股票: {record['stock_symbol']}")
        print(f"       期间: {record['financial_period_absolute']}")
        print(f"       科目: {record['item_name']}")
        print(f"       报表: {record['statement_type']}")
        
        # 科目状态
        print(f"     科目状态:")
        print(f"       item_status: {record['item_status']}")
        print(f"       item_source: {record['item_source']}")
        print(f"       correction_applicable: {record['correction_applicable']}")
        if record['item_mapping']:
            print(f"       item_mapping: {record['item_mapping']}")
        
        # 数据值
        print(f"     数据值:")
        print(f"       估算值: {record['filled_simple_estimates']}")
        print(f"       修正值: {record['corrected_filled_simple_estimates']}")
        print(f"       数据状态: {record['data_status']}")
        print(f"       置信度: {record['confidence_score']}")
        
        # 详细标注
        print(f"     详细标注:")
        print(f"       估算说明: {record['estimation_notes']}")
        print(f"       修正说明: {record['correction_notes']}")
        if record['item_change_reason']:
            print(f"       变化原因: {record['item_change_reason']}")
    
    return examples

def create_item_status_summary_view():
    """
    创建科目状态汇总视图
    """
    print(f"\n3. 科目状态汇总视图...")
    
    view_sql = """
    -- 创建科目状态汇总视图
    CREATE VIEW IF NOT EXISTS ap_research.item_status_summary AS
    SELECT 
        stock_symbol,
        statement_type,
        item_status,
        COUNT(*) as item_count,
        COUNT(CASE WHEN filled_simple_estimates IS NOT NULL THEN 1 END) as estimated_count,
        COUNT(CASE WHEN corrected_filled_simple_estimates IS NOT NULL THEN 1 END) as corrected_count,
        AVG(confidence_score) as avg_confidence,
        COUNT(CASE WHEN correction_applicable = 'yes' THEN 1 END) as correctable_count,
        COUNT(CASE WHEN correction_applicable = 'no' THEN 1 END) as non_correctable_count
    FROM quarterly_data_filled_enhanced
    GROUP BY stock_symbol, statement_type, item_status
    ORDER BY stock_symbol, statement_type, item_status;
    
    -- 科目变化追踪视图
    CREATE VIEW IF NOT EXISTS ap_research.item_change_tracking AS
    SELECT 
        stock_symbol,
        item_name,
        statement_type,
        item_status,
        item_mapping,
        item_change_reason,
        last_historical_period,
        first_current_period,
        estimation_notes,
        correction_notes
    FROM quarterly_data_filled_enhanced
    WHERE item_status IN ('historical_only', 'current_only', 'renamed')
    ORDER BY stock_symbol, statement_type, item_name;
    """
    
    print("汇总视图SQL:")
    print(view_sql)
    
    return view_sql

def provide_quality_control_queries():
    """
    提供质量控制查询
    """
    print(f"\n4. 质量控制查询...")
    
    quality_queries = {
        'item_status_distribution': """
        -- 科目状态分布统计
        SELECT 
            item_status,
            COUNT(*) as total_records,
            COUNT(DISTINCT stock_symbol) as affected_stocks,
            COUNT(DISTINCT item_name) as unique_items,
            AVG(confidence_score) as avg_confidence
        FROM quarterly_data_filled_enhanced
        GROUP BY item_status
        ORDER BY total_records DESC;
        """,
        
        'correction_coverage': """
        -- 修正覆盖率分析
        SELECT 
            stock_symbol,
            COUNT(*) as total_estimated,
            COUNT(CASE WHEN correction_applicable = 'yes' THEN 1 END) as correctable,
            COUNT(CASE WHEN corrected_filled_simple_estimates IS NOT NULL THEN 1 END) as actually_corrected,
            ROUND(COUNT(CASE WHEN corrected_filled_simple_estimates IS NOT NULL THEN 1 END) * 100.0 / 
                  COUNT(CASE WHEN correction_applicable = 'yes' THEN 1 END), 2) as correction_rate
        FROM quarterly_data_filled_enhanced
        WHERE filled_simple_estimates IS NOT NULL
        GROUP BY stock_symbol
        ORDER BY correction_rate DESC;
        """,
        
        'item_change_analysis': """
        -- 科目变化分析
        SELECT 
            item_change_reason,
            COUNT(*) as occurrence_count,
            COUNT(DISTINCT stock_symbol) as affected_stocks,
            COUNT(DISTINCT item_name) as affected_items
        FROM quarterly_data_filled_enhanced
        WHERE item_change_reason IS NOT NULL
        GROUP BY item_change_reason
        ORDER BY occurrence_count DESC;
        """,
        
        'missing_estimation_analysis': """
        -- 无法估算的科目分析
        SELECT 
            stock_symbol,
            statement_type,
            item_name,
            item_status,
            estimation_notes,
            first_current_period
        FROM quarterly_data_filled_enhanced
        WHERE filled_simple_estimates IS NULL
          AND item_status = 'current_only'
        ORDER BY stock_symbol, statement_type, item_name;
        """
    }
    
    for query_name, query_sql in quality_queries.items():
        print(f"\n  📊 {query_name.upper()}:")
        print(f"     {query_sql.strip()}")
    
    return quality_queries

def main():
    """
    主函数
    """
    print("开始设计最终增强的表结构...")
    
    try:
        # 1. 增强表结构
        table_sql = create_enhanced_quarterly_data_filled_table()
        
        # 2. 字段使用示例
        examples = provide_field_usage_examples()
        
        # 3. 汇总视图
        view_sql = create_item_status_summary_view()
        
        # 4. 质量控制查询
        quality_queries = provide_quality_control_queries()
        
        print(f"\n=== 增强功能总结 ===")
        print(f"✅ 新增7个科目管理字段")
        print(f"✅ 新增2个详细标注字段")
        print(f"✅ 完整的4种科目状态支持")
        print(f"✅ 科目重命名检测和映射")
        print(f"✅ 详细的处理说明和原因追踪")
        print(f"✅ 质量控制和统计分析视图")
        
        print(f"\n=== 关键改进 ===")
        print(f"1. current_only科目会被明确标注无法估算的原因")
        print(f"2. historical_only科目会说明无法修正的原因")
        print(f"3. renamed科目会记录映射关系和检测置信度")
        print(f"4. 所有科目变化都有详细的notes说明")
        print(f"5. 提供完整的质量控制和追踪机制")
        
        print(f"\n=== 特别针对EBITDA等current_only科目 ===")
        print(f"📝 estimation_notes: '科目在历史Q1报告中从未出现，无历史数据基础，无法进行估算'")
        print(f"📝 correction_notes: '不适用，因为没有估算值需要修正'")
        print(f"📝 item_status: 'current_only'")
        print(f"📝 correction_applicable: 'no'")
        print(f"📝 data_status: 'missing'")
        
    except Exception as e:
        print(f"❌ 设计过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

