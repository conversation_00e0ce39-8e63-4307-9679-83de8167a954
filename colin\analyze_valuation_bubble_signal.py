#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析valuation_bubble_signal的计算过程和含义
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from final_complete_24_factors_with_field_mapping import (
    connect_to_ap_research, 
    get_stock_data,
    get_price_data,
    get_field_with_priority
)
import pandas as pd

def analyze_valuation_bubble_signal():
    """分析valuation_bubble_signal的计算过程"""
    print("🔍 分析valuation_bubble_signal (PEG比率) 的计算过程...")
    
    # 连接数据库
    client = connect_to_ap_research()
    if not client:
        print("❌ 无法连接到数据库")
        return
    
    try:
        test_stock = 'AAPL'
        calculation_date = '2024-01-15'
        
        print(f"📊 分析股票: {test_stock}")
        print(f"📅 计算日期: {calculation_date}")
        
        # 获取数据
        stock_data = get_stock_data(client, test_stock, calculation_date)
        price_data = get_price_data(client, test_stock, calculation_date)
        
        pivot_data = stock_data.pivot_table(
            index=['financial_period_absolute', 'period_end_date', 'effective_date'],
            columns='item_name',
            values='value',
            aggfunc='first'
        ).reset_index()
        
        latest_data = pivot_data.iloc[-1]
        
        # 字段映射
        field_mappings = {
            'net_income': [
                'Normalized Net Income - Bottom Line',
                'Net Income - Basic - including Extraordinary Items Applicable to Common - Total',
                'Net Income - Diluted - including Extraordinary Items Applicable to Common - Total',
                'Net Income before Minority Interest',
                'Net Income after Minority Interest',
                'Net Income (Loss) - Total',
                'Net Income - Total'
            ],
            'shares': [
                'Common Shares - Outstanding - Total',
                'Shares Outstanding - Diluted Average',
                'Shares used to calculate Diluted EPS - Total'
            ],
            'revenue': [
                'Revenue from Business Activities - Total',
                'Revenue from Goods & Services'
            ]
        }
        
        print(f"\n📊 PEG比率计算分解:")
        print("=" * 60)
        
        # 1. 获取基础数据
        net_income, _ = get_field_with_priority(latest_data, field_mappings['net_income'])
        shares, _ = get_field_with_priority(latest_data, field_mappings['shares'])
        
        print(f"1️⃣ 基础数据:")
        print(f"   净利润: ${net_income:,.0f} 百万")
        print(f"   股本: {shares:,.0f} 百万股")
        print(f"   股价: ${price_data['close_price']:.2f}")
        
        # 2. 计算EPS和PE
        eps = net_income / shares
        pe_ratio = price_data['close_price'] / eps
        
        print(f"\n2️⃣ 估值指标:")
        print(f"   EPS: ${eps:.2f}")
        print(f"   PE比率: {pe_ratio:.1f}")
        
        # 3. 计算收入CAGR
        print(f"\n3️⃣ 收入增长分析:")
        revenues = []
        for i in range(min(5, len(pivot_data))):
            data = pivot_data.iloc[-(i+1)]
            revenue, _ = get_field_with_priority(data, field_mappings['revenue'])
            fiscal_year = data['financial_period_absolute']
            if revenue > 0:
                revenues.append({'year': fiscal_year, 'revenue': revenue})
                print(f"   {fiscal_year}: ${revenue:,.0f} 百万")
        
        revenues.reverse()
        
        if len(revenues) >= 2:
            start_revenue = revenues[0]['revenue']
            end_revenue = revenues[-1]['revenue']
            years = len(revenues) - 1
            
            revenue_cagr = (pow(end_revenue / start_revenue, 1 / years) - 1) * 100
            
            print(f"\n   收入CAGR计算:")
            print(f"   起始收入 ({revenues[0]['year']}): ${start_revenue:,.0f} 百万")
            print(f"   结束收入 ({revenues[-1]['year']}): ${end_revenue:,.0f} 百万")
            print(f"   年数: {years}")
            print(f"   收入CAGR: {revenue_cagr:.2f}%")
            
            # 4. 计算PEG比率
            if revenue_cagr > 0:
                peg_ratio = pe_ratio / revenue_cagr
                
                print(f"\n4️⃣ PEG比率计算:")
                print(f"   PEG = PE / 增长率")
                print(f"   PEG = {pe_ratio:.1f} / {revenue_cagr:.2f}%")
                print(f"   PEG = {peg_ratio:.1f}")
                
                # 5. PEG比率解读
                print(f"\n5️⃣ PEG比率解读:")
                print(f"   PEG = {peg_ratio:.1f}")
                
                if peg_ratio < 1:
                    print(f"   📈 PEG < 1: 相对增长率而言，估值较低（可能被低估）")
                elif peg_ratio < 2:
                    print(f"   ⚖️ 1 < PEG < 2: 估值相对合理")
                elif peg_ratio < 3:
                    print(f"   ⚠️ 2 < PEG < 3: 估值偏高，需要谨慎")
                else:
                    print(f"   🚨 PEG > 3: 估值泡沫风险较高")
                
                # 6. AAPL具体分析
                print(f"\n6️⃣ AAPL具体分析:")
                print(f"   AAPL的PEG = {peg_ratio:.1f} 表明:")
                print(f"   - PE比率: {pe_ratio:.1f}倍 (相对较高)")
                print(f"   - 收入增长: {revenue_cagr:.2f}% (增长放缓)")
                print(f"   - 结论: 相对于其增长率，AAPL估值显著偏高")
                print(f"   - 这解释了为什么 'valuation_bubble_signal' 数值较大")
                
                # 7. 为什么数值这么大
                print(f"\n7️⃣ 为什么数值这么大？")
                print(f"   原因分析:")
                print(f"   1. AAPL收入增长放缓: 只有{revenue_cagr:.2f}%的CAGR")
                print(f"   2. 但PE比率仍然很高: {pe_ratio:.1f}倍")
                print(f"   3. PEG = 高PE / 低增长 = 高数值")
                print(f"   4. 这正是'估值泡沫信号'想要捕捉的情况")
                
            else:
                print(f"   ❌ 收入CAGR <= 0，无法计算PEG")
        else:
            print(f"   ❌ 历史数据不足，无法计算CAGR")
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            client.disconnect()
            print("🔌 数据库连接已关闭")

if __name__ == "__main__":
    analyze_valuation_bubble_signal()
