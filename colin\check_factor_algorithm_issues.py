from clickhouse_driver import Client
import pandas as pd
import numpy as np
from datetime import datetime

def connect_to_ap_research():
    """连接到ap_research数据库"""
    client = Client(host='************', port=9000, user='default', password='5ur2pK8WZQdy2', database='ap_research')
    print("✅ 连接ap_research数据库成功")
    return client

def check_algorithm_issues():
    """检查所有因子的算法逻辑问题"""
    
    print("=== 检查所有因子的算法逻辑问题 ===")
    print()
    
    client = connect_to_ap_research()
    
    # 测试股票：选择Mega7中的几个进行详细检查
    test_stocks = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META']
    
    print("🔍 检查重点算法问题类型:")
    print("   1. CAGR计算中的负值处理")
    print("   2. 除零错误和分母为零")
    print("   3. 标准差计算中的数据不足")
    print("   4. 百分比计算中的异常值")
    print("   5. 时间序列中的数据缺失")
    print()
    
    for stock_symbol in test_stocks:
        print(f"🔍 检查 {stock_symbol}:")
        
        # 获取该股票的年报数据
        query = f"""
        SELECT 
            stock_symbol,
            financial_period_absolute,
            statement_type,
            item_name,
            value,
            period_end_date
        FROM priority_quality_fundamental_data_complete_deduped
        WHERE stock_symbol = '{stock_symbol}'
          AND financial_period_absolute REGEXP '^FY[0-9]{{4}}$'
        ORDER BY financial_period_absolute ASC
        """
        
        try:
            result = client.execute(query)
            if not result:
                print(f"   ❌ {stock_symbol}: 无年报数据")
                continue
                
            # 转换为DataFrame
            df = pd.DataFrame(result, columns=[
                'stock_symbol', 'financial_period_absolute', 'statement_type', 'item_name', 'value', 'period_end_date'
            ])
            
            # 透视数据
            pivot_df = df.pivot_table(
                index=['stock_symbol', 'financial_period_absolute', 'period_end_date'],
                columns='item_name',
                values='value',
                aggfunc='first'
            ).reset_index()
            
            pivot_df['fiscal_year'] = pivot_df['financial_period_absolute'].str.extract(r'FY(\d{4})').astype(int)
            pivot_df = pivot_df.sort_values('fiscal_year')
            
            years_data = len(pivot_df)
            print(f"   📊 {stock_symbol}: {years_data}年年报数据")
            
            # 1. 检查Revenue CAGR问题
            revenue_col = 'Revenue from Business Activities - Total'
            if revenue_col in pivot_df.columns:
                revenue_data = pivot_df[revenue_col].dropna()
                if len(revenue_data) >= 3:
                    print(f"   💰 Revenue数据: {len(revenue_data)}年")
                    
                    # 检查负值
                    negative_revenues = revenue_data[revenue_data <= 0]
                    if len(negative_revenues) > 0:
                        print(f"     ❌ 发现{len(negative_revenues)}年负营收或零营收")
                        print(f"     📊 负值年份: {negative_revenues.index.tolist()}")
                    
                    # 检查CAGR计算逻辑
                    if len(revenue_data) >= 5:
                        first_val = revenue_data.iloc[0]
                        last_val = revenue_data.iloc[-1]
                        if first_val > 0 and last_val > 0:
                            years = len(revenue_data) - 1
                            cagr = ((last_val / first_val) ** (1/years) - 1) * 100
                            print(f"     ✅ Revenue CAGR: {cagr:.2f}%")
                        else:
                            print(f"     ❌ Revenue CAGR无法计算: 首值={first_val}, 末值={last_val}")
            
            # 2. 检查FCF CAGR问题
            fcf_col = 'Free Cash Flow'
            if fcf_col in pivot_df.columns:
                fcf_data = pivot_df[fcf_col].dropna()
                if len(fcf_data) >= 3:
                    print(f"   💸 FCF数据: {len(fcf_data)}年")
                    
                    # 检查负值模式
                    negative_fcf = fcf_data[fcf_data <= 0]
                    positive_fcf = fcf_data[fcf_data > 0]
                    
                    print(f"     📊 正值FCF: {len(positive_fcf)}年, 负值/零FCF: {len(negative_fcf)}年")
                    
                    if len(negative_fcf) > 0:
                        print(f"     ⚠️  负值FCF年份: {negative_fcf.index.tolist()}")
                        
                        # 寻找最长连续正值区间（修复后的逻辑）
                        continuous_positive = []
                        current_run = []
                        
                        for i, val in enumerate(fcf_data):
                            if val > 0:
                                current_run.append((i, val))
                            else:
                                if len(current_run) > 1:
                                    continuous_positive.append(current_run)
                                current_run = []
                        
                        if len(current_run) > 1:
                            continuous_positive.append(current_run)
                        
                        if continuous_positive:
                            longest_run = max(continuous_positive, key=len)
                            print(f"     ✅ 最长连续正值区间: {len(longest_run)}年")
                            
                            if len(longest_run) >= 2:
                                first_val = longest_run[0][1]
                                last_val = longest_run[-1][1]
                                years = len(longest_run) - 1
                                cagr = ((last_val / first_val) ** (1/years) - 1) * 100
                                print(f"     ✅ 修正FCF CAGR: {cagr:.2f}%")
                        else:
                            print(f"     ❌ FCF CAGR: 无连续正值区间")
            
            # 3. 检查ROIC CAGR问题
            ni_col = 'Normalized Net Income - Bottom Line'
            debt_col = 'Debt - Total'
            equity_cols = [
                'Shareholders\' Equity - Attributable to Parent Shareholders - Total',
                'Common Equity - Total',
                'Common Equity Attributable to Parent Shareholders'
            ]
            
            if ni_col in pivot_df.columns:
                ni_data = pivot_df[ni_col].dropna()
                print(f"   📈 净利润数据: {len(ni_data)}年")
                
                # 检查负净利润
                negative_ni = ni_data[ni_data <= 0]
                if len(negative_ni) > 0:
                    print(f"     ❌ 发现{len(negative_ni)}年负净利润")
                
                # 检查股东权益字段可用性
                equity_available = False
                for eq_col in equity_cols:
                    if eq_col in pivot_df.columns:
                        eq_data = pivot_df[eq_col].dropna()
                        if len(eq_data) > 0:
                            print(f"     ✅ {eq_col}: {len(eq_data)}年数据")
                            equity_available = True
                            break
                
                if not equity_available:
                    print(f"     ❌ 所有股东权益字段均无数据")
            
            # 4. 检查Operating Margin标准差问题
            op_income_col = 'Operating Income'
            if op_income_col in pivot_df.columns and revenue_col in pivot_df.columns:
                op_income_data = pivot_df[op_income_col].dropna()
                revenue_data_for_margin = pivot_df[revenue_col].dropna()
                
                if len(op_income_data) >= 2 and len(revenue_data_for_margin) >= 2:
                    # 计算营业利润率
                    margins = []
                    for i in range(min(len(op_income_data), len(revenue_data_for_margin))):
                        if revenue_data_for_margin.iloc[i] > 0:
                            margin = (op_income_data.iloc[i] / revenue_data_for_margin.iloc[i]) * 100
                            margins.append(margin)
                    
                    if len(margins) >= 2:
                        margin_std = np.std(margins, ddof=1)
                        print(f"   📊 营业利润率标准差: {margin_std:.2f}% ({len(margins)}年数据)")
                        
                        # 检查异常值
                        extreme_margins = [m for m in margins if abs(m) > 100]
                        if extreme_margins:
                            print(f"     ⚠️  发现极端利润率: {extreme_margins}")
                    else:
                        print(f"   ❌ 营业利润率: 数据不足计算标准差")
            
            # 5. 检查有效税率相关问题
            tax_col = 'Income Taxes'
            ebit_col = 'Earnings before Interest & Taxes (EBIT)'
            
            if tax_col in pivot_df.columns and ebit_col in pivot_df.columns:
                tax_data = pivot_df[tax_col].dropna()
                ebit_data = pivot_df[ebit_col].dropna()
                
                if len(tax_data) >= 2 and len(ebit_data) >= 2:
                    # 计算有效税率
                    tax_rates = []
                    for i in range(min(len(tax_data), len(ebit_data))):
                        if ebit_data.iloc[i] > 0:  # EBIT > 0
                            tax_rate = (tax_data.iloc[i] / ebit_data.iloc[i]) * 100
                            tax_rates.append(tax_rate)
                    
                    if len(tax_rates) >= 2:
                        # 检查异常税率
                        extreme_rates = [r for r in tax_rates if r < 0 or r > 100]
                        if extreme_rates:
                            print(f"   ⚠️  异常有效税率: {extreme_rates} ({len(tax_rates)}年数据)")
                        else:
                            print(f"   ✅ 有效税率正常: {len(tax_rates)}年数据")
            
            print()
            
        except Exception as e:
            print(f"   ❌ {stock_symbol}: 检查出错 - {str(e)}")
            print()
    
    print("🎯 总结潜在算法问题:")
    print("   1. ❌ FCF CAGR: 需要智能处理负值FCF")
    print("   2. ❌ Revenue CAGR: 需要检查负营收")  
    print("   3. ❌ ROIC CAGR: 需要处理负净利润")
    print("   4. ❌ 营业利润率标准差: 需要处理极端值")
    print("   5. ❌ 有效税率: 需要处理异常税率")
    print("   6. ❌ 股东权益字段: 需要多字段fallback")

if __name__ == "__main__":
    check_algorithm_issues()

