"""
26个因子的完整实现细节
包括每个因子使用的具体字段、时间跨度、计算逻辑、公式等
"""

def print_complete_implementation_details():
    """打印每个因子的完整实现细节"""
    
    print("=== 26个因子完整实现细节检查 ===")
    print("详细列出每个因子的字段、时间跨度、逻辑、公式")
    print()
    
    factors = [
        {
            "序号": 1,
            "因子名": "tech_premium",
            "中文名": "技术溢价",
            "使用字段": [
                "Research & Development Expense",
                "Research & Development Expense - Supplemental", 
                "Revenue from Business Activities - Total"
            ],
            "时间跨度": "最新1年年报数据",
            "计算逻辑": """
            1. 获取最新年度数据：stock_data.iloc[-1]
            2. 计算总研发费用：rd_expense + rd_supplemental
            3. 获取营业收入：revenue
            4. 计算比例：(总研发费用 / 营业收入) × 100
            """,
            "公式": "tech_premium = ((R&D_expense + R&D_supplemental) / Revenue) × 100",
            "条件判断": "revenue > 0",
            "覆盖率": "42.3% (347/821)",
            "实际代码逻辑": """
            rd_expense = latest_data.get('Research & Development Expense', 0) or 0
            rd_supplemental = latest_data.get('Research & Development Expense - Supplemental', 0) or 0
            total_rd = rd_expense + rd_supplemental
            revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
            if revenue > 0:
                factors['tech_premium'] = (total_rd / revenue) * 100
            """
        },
        
        {
            "序号": 2,
            "因子名": "tech_gap_warning",
            "中文名": "技术差距警告",
            "使用字段": [
                "Research & Development Expense (当年和上年)",
                "Research & Development Expense - Supplemental (当年和上年)",
                "Revenue from Business Activities - Total (当年和上年)"
            ],
            "时间跨度": "最新2年年报数据（需上市≥2年）",
            "计算逻辑": """
            1. 先计算当前年度tech_premium
            2. 获取上一年数据：stock_data.iloc[-2]
            3. 计算上年tech_premium
            4. 计算变化率：(当前-上年)/上年 × 100
            """,
            "公式": "tech_gap_warning = (current_rd_ratio - prev_rd_ratio) / prev_rd_ratio × 100",
            "条件判断": "listing_years >= 2 and current_rd_ratio > 0 and prev_rd_ratio > 0",
            "覆盖率": "42.3% (347/821)",
            "实际代码逻辑": """
            if listing_years >= 2:
                current_rd_ratio = factors.get('tech_premium', 0) or 0
                prev_data = stock_data.iloc[-2]
                prev_rd = (prev_data.get('Research & Development Expense', 0) or 0) + 
                         (prev_data.get('Research & Development Expense - Supplemental', 0) or 0)
                prev_revenue = prev_data.get('Revenue from Business Activities - Total', 0) or 0
                if prev_revenue > 0:
                    prev_rd_ratio = (prev_rd / prev_revenue) * 100
                    if current_rd_ratio > 0 and prev_rd_ratio > 0:
                        factors['tech_gap_warning'] = (current_rd_ratio - prev_rd_ratio) / prev_rd_ratio * 100
            """
        },
        
        {
            "序号": 3,
            "因子名": "patent_density",
            "中文名": "专利密度",
            "使用字段": [
                "Research & Development Expense",
                "Research & Development Expense - Supplemental",
                "Total Assets"
            ],
            "时间跨度": "最新1年年报数据",
            "计算逻辑": """
            1. 获取最新年度数据：stock_data.iloc[-1]
            2. 计算总研发费用：rd_expense + rd_supplemental
            3. 获取总资产：total_assets
            4. 计算比例：(总研发费用 / 总资产) × 100
            """,
            "公式": "patent_density = (total_rd / total_assets) × 100",
            "条件判断": "total_assets > 0",
            "覆盖率": "42.3% (347/821)",
            "实际代码逻辑": """
            total_rd = (latest_data.get('Research & Development Expense', 0) or 0) + 
                      (latest_data.get('Research & Development Expense - Supplemental', 0) or 0)
            total_assets = latest_data.get('Total Assets', 0) or 0
            if total_assets > 0:
                factors['patent_density'] = (total_rd / total_assets) * 100
            """
        },
        
        {
            "序号": 4,
            "因子名": "ecosystem_cash_ratio",
            "中文名": "生态现金比率",
            "使用字段": [
                "Cash & Cash Equivalents",
                "Short-Term Investments",
                "Revenue from Business Activities - Total"
            ],
            "时间跨度": "最新1年年报数据",
            "计算逻辑": """
            1. 获取现金及现金等价物
            2. 获取短期投资
            3. 计算总现金：cash + short_term_investments
            4. 除以营业收入：(总现金 / 营业收入) × 100
            """,
            "公式": "ecosystem_cash_ratio = ((cash + short_term_investments) / revenue) × 100",
            "条件判断": "revenue > 0",
            "覆盖率": "95.4% (783/821)",
            "实际代码逻辑": """
            cash = latest_data.get('Cash & Cash Equivalents', 0) or 0
            short_term_investments = latest_data.get('Short-Term Investments', 0) or 0
            total_cash = cash + short_term_investments
            revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
            if revenue > 0:
                factors['ecosystem_cash_ratio'] = (total_cash / revenue) * 100
            """
        },
        
        {
            "序号": 5,
            "因子名": "adjusted_roce",
            "中文名": "调整后ROCE",
            "使用字段": [
                "Net Income",
                "Total Shareholders' Equity"
            ],
            "时间跨度": "最新1年年报数据",
            "计算逻辑": """
            1. 获取净利润
            2. 获取股东权益总额
            3. 计算ROE：(净利润 / 股东权益) × 100
            注意：实际计算的是ROE而不是ROCE
            """,
            "公式": "adjusted_roce = (net_income / total_equity) × 100",
            "条件判断": "total_equity > 0",
            "覆盖率": "0.0% (0/821)",
            "问题": "字段'Total Shareholders' Equity'在数据库中不存在",
            "实际代码逻辑": """
            net_income = latest_data.get('Net Income', 0) or 0
            total_equity = latest_data.get('Total Shareholders\\' Equity', 0) or 0
            if total_equity > 0:
                factors['adjusted_roce'] = (net_income / total_equity) * 100
            """
        },
        
        {
            "序号": 6,
            "因子名": "fcf_quality",
            "中文名": "自由现金流质量",
            "使用字段": [
                "Free Cash Flow",
                "Net Income"
            ],
            "时间跨度": "最新1年年报数据",
            "计算逻辑": """
            1. 获取自由现金流
            2. 获取净利润
            3. 计算比值：自由现金流 / 净利润
            """,
            "公式": "fcf_quality = fcf / net_income",
            "条件判断": "net_income > 0",
            "覆盖率": "0.0% (0/821)",
            "问题": "字段'Free Cash Flow'在数据库中不存在",
            "实际代码逻辑": """
            fcf = latest_data.get('Free Cash Flow', 0) or 0
            net_income = latest_data.get('Net Income', 0) or 0
            if net_income > 0:
                factors['fcf_quality'] = fcf / net_income
            """
        },
        
        {
            "序号": 7,
            "因子名": "dynamic_safety_margin",
            "中文名": "动态安全边际",
            "使用字段": [
                "依赖tech_premium和ecosystem_cash_ratio的计算结果"
            ],
            "时间跨度": "最新1年年报数据",
            "计算逻辑": """
            1. 检查tech_premium和ecosystem_cash_ratio都非空
            2. 直接相加：tech_premium + ecosystem_cash_ratio
            """,
            "公式": "dynamic_safety_margin = tech_premium + ecosystem_cash_ratio",
            "条件判断": "tech_premium is not None and ecosystem_cash_ratio is not None",
            "覆盖率": "42.1% (346/821)",
            "实际代码逻辑": """
            if factors.get('tech_premium') is not None and factors.get('ecosystem_cash_ratio') is not None:
                factors['dynamic_safety_margin'] = factors['tech_premium'] + factors['ecosystem_cash_ratio']
            """
        },
        
        {
            "序号": 8,
            "因子名": "revenue_growth_continuity",
            "中文名": "营收增长连续性",
            "使用字段": [
                "Revenue from Business Activities - Total (多年)"
            ],
            "时间跨度": "动态：上市≥3年用3年，上市2年用2年，<2年缺失",
            "计算逻辑": """
            1. 计算最近3年（或可得年数）的营收同比增长率
            2. 计算这些增长率的标准差
            3. 标准差越小表示增长越连续稳定
            """,
            "公式": "revenue_growth_continuity = std([growth_rate_1, growth_rate_2, ...])",
            "条件判断": "listing_years >= 2 and len(revenue_growth_rates) >= 2",
            "覆盖率": "99.5% (817/821)",
            "实际代码逻辑": """
            if listing_years >= 2:
                revenue_growth_rates = []
                for i in range(1, min(4, listing_years)):  # 最多看3年
                    current = stock_data.iloc[-i]['Revenue from Business Activities - Total'] or 0
                    previous = stock_data.iloc[-i-1]['Revenue from Business Activities - Total'] or 0
                    if previous > 0:
                        growth_rate = (current - previous) / previous * 100
                        revenue_growth_rates.append(growth_rate)
                if len(revenue_growth_rates) >= 2:
                    factors['revenue_growth_continuity'] = np.std(revenue_growth_rates)
            """
        },
        
        {
            "序号": 9,
            "因子名": "effective_tax_rate_improvement",
            "中文名": "有效税率改善",
            "使用字段": [
                "Income Taxes",
                "Income before Taxes"
            ],
            "时间跨度": "最新1年年报数据",
            "计算逻辑": """
            1. 获取所得税费用
            2. 获取税前利润
            3. 计算有效税率：(所得税费用 / 税前利润) × 100
            注意：实际计算税率而非改善程度
            """,
            "公式": "effective_tax_rate_improvement = (tax_expense / pretax_income) × 100",
            "条件判断": "pretax_income > 0",
            "覆盖率": "86.4% (709/821)",
            "实际代码逻辑": """
            tax_expense = latest_data.get('Income Taxes', 0) or 0
            pretax_income = latest_data.get('Income before Taxes', 0) or 0
            if pretax_income > 0:
                factors['effective_tax_rate_improvement'] = (tax_expense / pretax_income) * 100
            """
        },
        
        {
            "序号": 10,
            "因子名": "financial_health",
            "中文名": "财务健康度",
            "使用字段": [
                "Current Assets - Total",
                "Current Liabilities - Total"
            ],
            "时间跨度": "最新1年年报数据",
            "计算逻辑": """
            1. 获取流动资产总额
            2. 获取流动负债总额
            3. 计算流动比率：流动资产 / 流动负债
            """,
            "公式": "financial_health = current_assets / current_liabilities",
            "条件判断": "current_liabilities > 0",
            "覆盖率": "0.0% (0/821)",
            "问题": "字段'Current Assets/Liabilities - Total'在数据库中不存在",
            "实际代码逻辑": """
            current_assets = latest_data.get('Current Assets - Total', 0) or 0
            current_liabilities = latest_data.get('Current Liabilities - Total', 0) or 0
            if current_liabilities > 0:
                factors['financial_health'] = current_assets / current_liabilities
            """
        },
        
        {
            "序号": 11,
            "因子名": "valuation_bubble_signal",
            "中文名": "估值泡沫信号",
            "使用字段": [
                "close_price",
                "Earnings Per Share - Diluted"
            ],
            "时间跨度": "最新1年数据（价格+财务）",
            "计算逻辑": """
            1. 获取收盘价
            2. 获取稀释每股收益
            3. 计算PE比率：收盘价 / 每股收益
            """,
            "公式": "valuation_bubble_signal = close_price / eps",
            "条件判断": "eps > 0",
            "覆盖率": "0.0% (0/821)",
            "问题": "字段'Earnings Per Share - Diluted'在数据库中不存在",
            "实际代码逻辑": """
            close_price = latest_data.get('close_price', 0) or 0
            eps = latest_data.get('Earnings Per Share - Diluted', 0) or 0
            if eps > 0:
                pe_ratio = close_price / eps
                factors['valuation_bubble_signal'] = pe_ratio
            """
        },
        
        {
            "序号": 12,
            "因子名": "rd_intensity",
            "中文名": "研发强度",
            "使用字段": [
                "与tech_premium完全相同"
            ],
            "时间跨度": "最新1年年报数据",
            "计算逻辑": """
            1. 直接引用tech_premium的计算结果
            2. 实际上是重复因子
            """,
            "公式": "rd_intensity = tech_premium",
            "条件判断": "无额外条件",
            "覆盖率": "42.3% (347/821)",
            "问题": "与tech_premium完全重复",
            "实际代码逻辑": """
            factors['rd_intensity'] = factors.get('tech_premium')
            """
        },
        
        {
            "序号": 13,
            "因子名": "roce_stability",
            "中文名": "ROCE稳定性",
            "使用字段": [
                "Net Income (多年)",
                "Total Shareholders' Equity (多年)"
            ],
            "时间跨度": "动态：上市≥5年用5年，3-4年用实际年限，<3年缺失",
            "计算逻辑": """
            1. 计算最近5年（或可得年数）的ROCE值
            2. ROCE = 净利润 / 股东权益
            3. 计算ROCE值的标准差
            """,
            "公式": "roce_stability = std([roce_1, roce_2, ...])",
            "条件判断": "listing_years >= 3 and len(roce_values) >= 3",
            "覆盖率": "0.0% (0/821)",
            "问题": "依赖股东权益字段，数据库中不存在",
            "实际代码逻辑": """
            if listing_years >= 3:
                roce_values = []
                for i in range(min(5, listing_years)):
                    year_data = stock_data.iloc[-i-1]
                    net_income = year_data.get('Net Income', 0) or 0
                    total_equity = year_data.get('Total Shareholders\\' Equity', 0) or 0
                    if total_equity > 0:
                        roce = (net_income / total_equity) * 100
                        roce_values.append(roce)
                if len(roce_values) >= 3:
                    factors['roce_stability'] = np.std(roce_values)
            """
        },
        
        {
            "序号": 14,
            "因子名": "revenue_yoy",
            "中文名": "营收同比增长",
            "使用字段": [
                "Revenue from Business Activities - Total (当年和上年)"
            ],
            "时间跨度": "最新2年年报数据（需上市≥2年）",
            "计算逻辑": """
            1. 获取当年营收
            2. 获取上年营收
            3. 计算同比增长率：(当年-上年)/上年 × 100
            """,
            "公式": "revenue_yoy = (current_revenue - prev_revenue) / prev_revenue × 100",
            "条件判断": "listing_years >= 2 and prev_revenue > 0",
            "覆盖率": "99.4% (816/821)",
            "实际代码逻辑": """
            if listing_years >= 2:
                current_revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
                prev_revenue = stock_data.iloc[-2].get('Revenue from Business Activities - Total', 0) or 0
                if prev_revenue > 0:
                    factors['revenue_yoy'] = (current_revenue - prev_revenue) / prev_revenue * 100
            """
        },
        
        {
            "序号": 15,
            "因子名": "revenue_cagr",
            "中文名": "营收CAGR",
            "使用字段": [
                "Revenue from Business Activities - Total (多年)"
            ],
            "时间跨度": "动态：上市≥8年用8年，5-7年用实际年限，3-4年用实际年限，<3年缺失",
            "计算逻辑": """
            1. 确定时间窗口：min(8年, 实际上市年限)
            2. 获取当前年度和起始年度营收
            3. 计算CAGR：((当前/起始)^(1/(年数-1)) - 1) × 100
            """,
            "公式": "revenue_cagr = ((current_revenue / past_revenue)^(1/(years_to_use-1)) - 1) × 100",
            "条件判断": "listing_years >= 3 and past_revenue > 0 and current_revenue > 0",
            "覆盖率": "99.0% (813/821)",
            "实际代码逻辑": """
            if listing_years >= 3:
                years_to_use = min(8, listing_years)
                current_revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
                past_revenue = stock_data.iloc[-years_to_use].get('Revenue from Business Activities - Total', 0) or 0
                if past_revenue > 0 and current_revenue > 0:
                    cagr = ((current_revenue / past_revenue) ** (1/(years_to_use-1)) - 1) * 100
                    factors['revenue_cagr'] = cagr
            """
        },
        
        {
            "序号": 16,
            "因子名": "net_income_yoy",
            "中文名": "净利润同比增长",
            "使用字段": [
                "Net Income (当年和上年)"
            ],
            "时间跨度": "最新2年年报数据（需上市≥2年）",
            "计算逻辑": """
            1. 获取当年净利润
            2. 获取上年净利润
            3. 计算同比增长率：(当年-上年)/|上年| × 100
            """,
            "公式": "net_income_yoy = (current_ni - prev_ni) / abs(prev_ni) × 100",
            "条件判断": "listing_years >= 2 and prev_ni != 0",
            "覆盖率": "0.0% (0/821)",
            "问题": "字段'Net Income'在数据库中不存在",
            "实际代码逻辑": """
            if listing_years >= 2:
                current_ni = latest_data.get('Net Income', 0) or 0
                prev_ni = stock_data.iloc[-2].get('Net Income', 0) or 0
                if prev_ni != 0:
                    factors['net_income_yoy'] = (current_ni - prev_ni) / abs(prev_ni) * 100
            """
        },
        
        {
            "序号": 17,
            "因子名": "profit_revenue_ratio",
            "中文名": "利润营收比",
            "使用字段": [
                "Net Income",
                "Revenue from Business Activities - Total"
            ],
            "时间跨度": "最新1年年报数据",
            "计算逻辑": """
            1. 获取净利润
            2. 获取营业收入
            3. 计算净利润率：(净利润 / 营业收入) × 100
            """,
            "公式": "profit_revenue_ratio = (net_income / revenue) × 100",
            "条件判断": "revenue > 0",
            "覆盖率": "99.5% (817/821)",
            "注意": "覆盖率高，可能使用了替代字段计算净利润",
            "实际代码逻辑": """
            net_income = latest_data.get('Net Income', 0) or 0
            revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
            if revenue > 0:
                factors['profit_revenue_ratio'] = (net_income / revenue) * 100
            """
        },
        
        {
            "序号": 18,
            "因子名": "fcf_per_share",
            "中文名": "每股自由现金流",
            "使用字段": [
                "Free Cash Flow",
                "Shares Outstanding - Diluted Average"
            ],
            "时间跨度": "最新1年年报数据",
            "计算逻辑": """
            1. 获取自由现金流
            2. 获取稀释后平均股本
            3. 计算每股FCF：自由现金流 / 股本
            """,
            "公式": "fcf_per_share = fcf / shares",
            "条件判断": "shares > 0",
            "覆盖率": "0.0% (0/821)",
            "问题": "FCF和股本字段在数据库中不存在",
            "实际代码逻辑": """
            fcf = latest_data.get('Free Cash Flow', 0) or 0
            shares = latest_data.get('Shares Outstanding - Diluted Average', 0) or 0
            if shares > 0:
                factors['fcf_per_share'] = fcf / shares
            """
        },
        
        {
            "序号": 19,
            "因子名": "fcf_cagr",
            "中文名": "自由现金流CAGR",
            "使用字段": [
                "Free Cash Flow (多年)"
            ],
            "时间跨度": "动态：同revenue_cagr逻辑",
            "计算逻辑": """
            1. 确定时间窗口：min(8年, 实际上市年限)
            2. 获取当前年度和起始年度FCF
            3. 计算CAGR：((当前FCF/起始FCF)^(1/(年数-1)) - 1) × 100
            """,
            "公式": "fcf_cagr = ((current_fcf / past_fcf)^(1/(years_to_use-1)) - 1) × 100",
            "条件判断": "listing_years >= 3 and past_fcf > 0 and current_fcf > 0",
            "覆盖率": "70.2% (576/821)",
            "注意": "覆盖率中等，可能使用了替代FCF计算方法",
            "实际代码逻辑": """
            if listing_years >= 3:
                years_to_use = min(8, listing_years)
                current_fcf = latest_data.get('Free Cash Flow', 0) or 0
                past_fcf = stock_data.iloc[-years_to_use].get('Free Cash Flow', 0) or 0
                if past_fcf > 0 and current_fcf > 0:
                    cagr = ((current_fcf / past_fcf) ** (1/(years_to_use-1)) - 1) * 100
                    factors['fcf_cagr'] = cagr
            """
        },
        
        {
            "序号": 20,
            "因子名": "fcf_net_income_ratio",
            "中文名": "自由现金流净利润比",
            "使用字段": [
                "Free Cash Flow",
                "Net Income"
            ],
            "时间跨度": "最新1年年报数据",
            "计算逻辑": """
            1. 获取自由现金流
            2. 获取净利润
            3. 计算比值：自由现金流 / 净利润
            """,
            "公式": "fcf_net_income_ratio = fcf / net_income",
            "条件判断": "net_income > 0",
            "覆盖率": "0.0% (0/821)",
            "问题": "与fcf_quality重复，且字段不存在",
            "实际代码逻辑": """
            fcf = latest_data.get('Free Cash Flow', 0) or 0
            net_income = latest_data.get('Net Income', 0) or 0
            if net_income > 0:
                factors['fcf_net_income_ratio'] = fcf / net_income
            """
        },
        
        {
            "序号": 21,
            "因子名": "operating_margin",
            "中文名": "营业利润率",
            "使用字段": [
                "Operating Income",
                "Revenue from Business Activities - Total"
            ],
            "时间跨度": "最新1年年报数据",
            "计算逻辑": """
            1. 获取营业收入（Operating Income）
            2. 获取营业收入（Revenue）
            3. 计算营业利润率：(营业收入 / 营业收入) × 100
            """,
            "公式": "operating_margin = (operating_income / revenue) × 100",
            "条件判断": "revenue > 0",
            "覆盖率": "99.5% (817/821)",
            "实际代码逻辑": """
            operating_income = latest_data.get('Operating Income', 0) or 0
            revenue = latest_data.get('Revenue from Business Activities - Total', 0) or 0
            if revenue > 0:
                factors['operating_margin'] = (operating_income / revenue) * 100
            """
        },
        
        {
            "序号": 22,
            "因子名": "operating_margin_std",
            "中文名": "营业利润率稳定性",
            "使用字段": [
                "Operating Income (多年)",
                "Revenue from Business Activities - Total (多年)"
            ],
            "时间跨度": "动态：上市≥4年用4年，2-3年用实际年限，<2年缺失",
            "计算逻辑": """
            1. 计算最近4年（或可得年数）的营业利润率
            2. 计算这些利润率的标准差
            3. 标准差越小表示盈利越稳定
            """,
            "公式": "operating_margin_std = std([margin_1, margin_2, ...])",
            "条件判断": "listing_years >= 2 and len(margins) >= 2",
            "覆盖率": "99.9% (820/821)",
            "实际代码逻辑": """
            if listing_years >= 2:
                margins = []
                for i in range(min(4, listing_years)):
                    year_data = stock_data.iloc[-i-1]
                    operating_income = year_data.get('Operating Income', 0) or 0
                    revenue = year_data.get('Revenue from Business Activities - Total', 0) or 0
                    if revenue > 0:
                        margin = (operating_income / revenue) * 100
                        margins.append(margin)
                if len(margins) >= 2:
                    factors['operating_margin_std'] = np.std(margins)
            """
        },
        
        {
            "序号": 23,
            "因子名": "roic",
            "中文名": "ROIC",
            "使用字段": [
                "Net Income",
                "Total Debt",
                "Total Shareholders' Equity"
            ],
            "时间跨度": "最新1年年报数据",
            "计算逻辑": """
            1. 获取净利润
            2. 计算投入资本：总债务 + 股东权益
            3. 计算ROIC：(净利润 / 投入资本) × 100
            """,
            "公式": "roic = (net_income / (total_debt + total_equity)) × 100",
            "条件判断": "invested_capital > 0",
            "覆盖率": "0.0% (0/821)",
            "问题": "债务和权益字段在数据库中不存在",
            "实际代码逻辑": """
            net_income = latest_data.get('Net Income', 0) or 0
            total_debt = latest_data.get('Total Debt', 0) or 0
            total_equity = latest_data.get('Total Shareholders\\' Equity', 0) or 0
            invested_capital = total_debt + total_equity
            if invested_capital > 0:
                factors['roic'] = (net_income / invested_capital) * 100
            """
        },
        
        {
            "序号": 24,
            "因子名": "roic_cagr",
            "中文名": "ROIC CAGR",
            "使用字段": [
                "依赖ROIC计算（Net Income, Total Debt, Total Shareholders' Equity多年）"
            ],
            "时间跨度": "动态：同revenue_cagr逻辑",
            "计算逻辑": """
            1. 计算当前年度ROIC
            2. 计算起始年度ROIC
            3. 计算ROIC的CAGR：((当前ROIC/起始ROIC)^(1/(年数-1)) - 1) × 100
            """,
            "公式": "roic_cagr = ((current_roic / past_roic)^(1/(years_to_use-1)) - 1) × 100",
            "条件判断": "listing_years >= 3 and past_roic > 0 and current_roic > 0",
            "覆盖率": "0.0% (0/821)",
            "问题": "依赖ROIC计算，字段问题导致无法计算",
            "实际代码逻辑": """
            if listing_years >= 3:
                years_to_use = min(8, listing_years)
                current_roic = factors.get('roic', 0) or 0
                # 计算过去的ROIC
                past_data = stock_data.iloc[-years_to_use]
                past_ni = past_data.get('Net Income', 0) or 0
                past_debt = past_data.get('Total Debt', 0) or 0
                past_equity = past_data.get('Total Shareholders\\' Equity', 0) or 0
                past_invested_capital = past_debt + past_equity
                if past_invested_capital > 0:
                    past_roic = (past_ni / past_invested_capital) * 100
                    if past_roic > 0 and current_roic > 0:
                        cagr = ((current_roic / past_roic) ** (1/(years_to_use-1)) - 1) * 100
                        factors['roic_cagr'] = cagr
            """
        },
        
        {
            "序号": 25,
            "因子名": "effective_tax_rate",
            "中文名": "有效税率",
            "使用字段": [
                "Income Taxes",
                "Earnings before Interest & Taxes (EBIT)"
            ],
            "时间跨度": "最新1年年报数据",
            "计算逻辑": """
            1. 获取所得税费用
            2. 获取EBIT
            3. 计算有效税率：(所得税费用 / EBIT) × 100
            """,
            "公式": "effective_tax_rate = (tax_expense / ebit) × 100",
            "条件判断": "ebit > 0",
            "覆盖率": "91.2% (749/821)",
            "实际代码逻辑": """
            tax_expense = latest_data.get('Income Taxes', 0) or 0
            ebit = latest_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
            if ebit > 0:
                factors['effective_tax_rate'] = (tax_expense / ebit) * 100
            """
        },
        
        {
            "序号": 26,
            "因子名": "effective_tax_rate_std",
            "中文名": "有效税率稳定性",
            "使用字段": [
                "Income Taxes (多年)",
                "Earnings before Interest & Taxes (EBIT) (多年)"
            ],
            "时间跨度": "动态：同operating_margin_std逻辑",
            "计算逻辑": """
            1. 计算最近4年（或可得年数）的有效税率
            2. 计算这些税率的标准差
            3. 标准差越小表示税率越稳定
            """,
            "公式": "effective_tax_rate_std = std([tax_rate_1, tax_rate_2, ...])",
            "条件判断": "listing_years >= 2 and len(tax_rates) >= 2",
            "覆盖率": "91.2% (749/821)",
            "实际代码逻辑": """
            if listing_years >= 2:
                tax_rates = []
                for i in range(min(4, listing_years)):
                    year_data = stock_data.iloc[-i-1]
                    tax_expense = year_data.get('Income Taxes', 0) or 0
                    ebit = year_data.get('Earnings before Interest & Taxes (EBIT)', 0) or 0
                    if ebit > 0:
                        tax_rate = (tax_expense / ebit) * 100
                        tax_rates.append(tax_rate)
                if len(tax_rates) >= 2:
                    factors['effective_tax_rate_std'] = np.std(tax_rates)
            """
        }
    ]
    
    # 打印每个因子的详细信息
    for factor in factors:
        print(f"{'='*100}")
        print(f"【{factor['序号']}】{factor['因子名']} ({factor['中文名']})")
        print(f"{'='*100}")
        
        print(f"📊 使用字段:")
        for field in factor['使用字段']:
            print(f"    • {field}")
        
        print(f"\n⏰ 时间跨度: {factor['时间跨度']}")
        
        print(f"\n📐 计算公式: {factor['公式']}")
        
        print(f"\n🔧 计算逻辑:")
        for line in factor['计算逻辑'].strip().split('\n'):
            if line.strip():
                print(f"    {line.strip()}")
        
        print(f"\n✅ 条件判断: {factor['条件判断']}")
        
        print(f"\n📈 覆盖率: {factor['覆盖率']}")
        
        if '问题' in factor:
            print(f"\n⚠️ 问题: {factor['问题']}")
        
        if '注意' in factor:
            print(f"\n💡 注意: {factor['注意']}")
        
        print(f"\n💻 实际代码逻辑:")
        for line in factor['实际代码逻辑'].strip().split('\n'):
            if line.strip():
                print(f"    {line.strip()}")
        
        print("\n")
    
    # 总结统计
    print(f"{'='*100}")
    print("📋 完整实现总结")
    print(f"{'='*100}")
    
    # 按时间跨度分类
    single_year = []
    multi_year = []
    dynamic_window = []
    
    for factor in factors:
        if "最新1年" in factor['时间跨度']:
            single_year.append(factor['因子名'])
        elif "动态" in factor['时间跨度']:
            dynamic_window.append(factor['因子名'])
        else:
            multi_year.append(factor['因子名'])
    
    print(f"📅 按时间跨度分类:")
    print(f"    单年数据 ({len(single_year)}个): {', '.join(single_year)}")
    print(f"    多年数据 ({len(multi_year)}个): {', '.join(multi_year)}")  
    print(f"    动态窗口 ({len(dynamic_window)}个): {', '.join(dynamic_window)}")
    
    # 按数据类型分类
    revenue_based = []
    rd_based = []
    cash_based = []
    profit_based = []
    tax_based = []
    
    for factor in factors:
        fields_str = ' '.join(factor['使用字段'])
        if 'Revenue from Business Activities' in fields_str:
            revenue_based.append(factor['因子名'])
        if 'Research & Development' in fields_str:
            rd_based.append(factor['因子名'])
        if 'Cash' in fields_str or 'Free Cash Flow' in fields_str:
            cash_based.append(factor['因子名'])
        if 'Net Income' in fields_str or 'Operating Income' in fields_str:
            profit_based.append(factor['因子名'])
        if 'Income Taxes' in fields_str or 'EBIT' in fields_str:
            tax_based.append(factor['因子名'])
    
    print(f"\n📊 按数据类型分类:")
    print(f"    营收相关 ({len(revenue_based)}个): {', '.join(revenue_based)}")
    print(f"    研发相关 ({len(rd_based)}个): {', '.join(rd_based)}")
    print(f"    现金流相关 ({len(cash_based)}个): {', '.join(cash_based)}")
    print(f"    利润相关 ({len(profit_based)}个): {', '.join(profit_based)}")
    print(f"    税务相关 ({len(tax_based)}个): {', '.join(tax_based)}")
    
    # 问题汇总
    problems = []
    for factor in factors:
        if '问题' in factor:
            problems.append(f"{factor['因子名']}: {factor['问题']}")
    
    print(f"\n🚨 问题汇总 ({len(problems)}个):")
    for problem in problems:
        print(f"    • {problem}")

if __name__ == "__main__":
    print_complete_implementation_details()

